<?php
session_start();
require_once 'config.php';

// Para demo, usar usuario por defecto si no está logueado
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['username'] = 'usuario';
}

$user_id = $_SESSION['user_id'];
$username = $_SESSION['username'] ?? 'usuario';

// Obtener aplicaciones de la base de datos
$apps_db = [];
try {
    $stmt = $pdo->prepare("
        SELECT * FROM support_apps
        WHERE status = 'active' OR status IS NULL
        ORDER BY platform, name
    ");
    $stmt->execute();
    $apps_db = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    error_log("Error cargando apps de BD: " . $e->getMessage());
}

// Obtener archivos APK del directorio apploader
$apps_files = [];
$apploader_dir = __DIR__ . '/apploader';
if (is_dir($apploader_dir)) {
    $files = glob($apploader_dir . '/*.{apk,ipa,exe,dmg,deb,zip,msi}', GLOB_BRACE);
    foreach ($files as $file) {
        $filename = basename($file);
        $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
        $size = filesize($file);

        // Determinar plataforma por extensión
        $platform = 'other';
        switch ($extension) {
            case 'apk':
                $platform = 'android';
                break;
            case 'ipa':
                $platform = 'ios';
                break;
            case 'exe':
            case 'msi':
                $platform = 'windows';
                break;
            case 'dmg':
                $platform = 'macos';
                break;
        }

        $apps_files[] = [
            'id' => 'file_' . md5($filename),
            'name' => pathinfo($filename, PATHINFO_FILENAME),
            'version' => 'Última versión',
            'platform' => $platform,
            'file_path' => 'apploader/' . $filename,
            'file_size' => $size,
            'description' => 'Aplicación IPTV para ' . ucfirst($platform),
            'features' => 'Reproducción IPTV, EPG, Favoritos',
            'download_url' => 'apploader/' . $filename,
            'status' => 'active',
            'source' => 'file'
        ];
    }
}

// Combinar aplicaciones de BD y archivos
$apps = array_merge($apps_db, $apps_files);

// Debug: mostrar cuántas apps se encontraron
error_log("Apps de BD: " . count($apps_db) . ", Apps de archivos: " . count($apps_files) . ", Total: " . count($apps));

// Función para formatear bytes
function formatBytes($size, $precision = 2) {
    if ($size == 0) return '0 B';
    $base = log($size, 1024);
    $suffixes = array('B', 'KB', 'MB', 'GB', 'TB');
    return round(pow(1024, $base - floor($base)), $precision) . ' ' . $suffixes[floor($base)];
}

// Agrupar por plataforma
$apps_by_platform = [];
foreach ($apps as $app) {
    $apps_by_platform[$app['platform']][] = $app;
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📱 Aplicaciones IPTV - RGS</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            /* Paleta de colores moderna */
            --primary-color: #6366f1;
            --primary-dark: #4f46e5;
            --secondary-color: #1e293b;
            --dark-bg: #0f172a;
            --surface: #334155;

            /* Colores de texto */
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --text-muted: #94a3b8;

            /* Colores de acento */
            --accent-color: #10b981;
            --accent-secondary: #06b6d4;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --success-color: #22c55e;

            /* Gradientes modernos */
            --gradient-primary: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            --gradient-secondary: linear-gradient(135deg, #10b981 0%, #06b6d4 100%);
            --gradient-hero: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            --gradient-surface: linear-gradient(135deg, #1e293b 0%, #334155 100%);

            /* Espaciado consistente */
            --space-xs: 0.25rem;
            --space-sm: 0.5rem;
            --space-md: 1rem;
            --space-lg: 1.5rem;
            --space-xl: 2rem;

            /* Border radius moderno */
            --radius-sm: 6px;
            --radius-md: 8px;
            --radius-lg: 12px;
            --radius-xl: 16px;

            /* Sombras mejoradas */
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);

            /* Transiciones suaves */
            --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);

            /* Compatibilidad */
            --border-color: #334155;
            --gradient-bg: var(--gradient-hero);
            --border-radius: var(--radius-lg);
            --transition: var(--transition-normal);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gradient-hero);
            background-attachment: fixed;
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
            font-feature-settings: "kern" 1, "liga" 1;
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .header {
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(20px) saturate(180%);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding: 0.75rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: var(--shadow-lg);
        }

        .header-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--space-lg);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.4rem;
            font-weight: 800;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-decoration: none;
            letter-spacing: -0.025em;
        }

        .nav-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .nav-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 0.75rem;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: var(--text-secondary);
            text-decoration: none;
            border-radius: var(--radius-lg);
            backdrop-filter: blur(10px);
            transition: var(--transition-normal);
            font-weight: 500;
            font-size: 0.9rem;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: var(--primary-color);
            color: var(--text-primary);
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .nav-btn.back-btn {
            background: var(--gradient-primary);
            border-color: var(--primary-color);
            color: white;
        }

        .nav-btn.back-btn:hover {
            background: var(--gradient-primary);
            transform: translateY(-2px) scale(1.05);
        }

        .main-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: var(--space-lg) var(--space-lg);
        }

        .page-header {
            text-align: center;
            margin-bottom: var(--space-xl);
        }

        .page-title {
            font-size: clamp(1.8rem, 4vw, 2.2rem);
            font-weight: 800;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: var(--space-sm);
            letter-spacing: -0.025em;
        }

        .page-subtitle {
            color: var(--text-secondary);
            font-size: 1rem;
            max-width: 500px;
            margin: 0 auto;
            line-height: 1.5;
        }

        .platform-section {
            margin-bottom: var(--space-xl);
        }

        .platform-header {
            display: flex;
            align-items: center;
            gap: var(--space-md);
            margin-bottom: var(--space-lg);
            padding-bottom: var(--space-sm);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .platform-icon {
            width: 40px;
            height: 40px;
            background: var(--gradient-primary);
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.3rem;
            box-shadow: var(--shadow-md);
        }

        .platform-title {
            font-size: 1.4rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .apps-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: var(--space-lg);
        }

        .app-card {
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid rgba(255, 255, 255, 0.08);
            border-radius: var(--radius-xl);
            backdrop-filter: blur(10px);
            padding: var(--space-lg);
            transition: var(--transition-normal);
            position: relative;
            overflow: hidden;
        }

        .app-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--gradient-primary);
            transform: scaleX(0);
            transition: var(--transition-normal);
        }

        .app-card:hover {
            background: rgba(255, 255, 255, 0.06);
            border-color: var(--primary-color);
            transform: translateY(-8px);
            box-shadow: var(--shadow-xl);
        }

        .app-card:hover::before {
            transform: scaleX(1);
        }

        .app-header {
            display: flex;
            align-items: flex-start;
            gap: var(--space-md);
            margin-bottom: var(--space-md);
        }

        .app-icon {
            width: 48px;
            height: 48px;
            background: var(--gradient-secondary);
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            flex-shrink: 0;
            box-shadow: var(--shadow-md);
        }

        .app-info {
            flex: 1;
        }

        .app-name {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--space-xs);
        }

        .app-version {
            color: var(--text-secondary);
            font-size: 0.8rem;
            margin-bottom: var(--space-xs);
        }

        .app-size {
            color: var(--text-secondary);
            font-size: 0.8rem;
        }

        .app-description {
            color: var(--text-secondary);
            margin-bottom: var(--space-md);
            line-height: 1.5;
            font-size: 0.9rem;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .app-features {
            margin-bottom: var(--space-md);
        }

        .features-title {
            font-size: 0.9rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--space-xs);
        }

        .features-list {
            list-style: none;
            padding: 0;
        }

        .features-list li {
            color: var(--text-secondary);
            font-size: 0.8rem;
            margin-bottom: var(--space-xs);
            display: flex;
            align-items: center;
            gap: var(--space-xs);
        }

        .features-list li::before {
            content: '✓';
            color: var(--success-color);
            font-weight: bold;
            font-size: 0.7rem;
        }

        .app-actions {
            display: flex;
            gap: var(--space-sm);
            flex-wrap: wrap;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: var(--radius-lg);
            cursor: pointer;
            font-size: 0.8rem;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: var(--space-xs);
            transition: var(--transition-normal);
            flex: 1;
            justify-content: center;
            min-width: 100px;
        }

        .btn-primary {
            background: var(--gradient-primary);
            color: white;
            box-shadow: var(--shadow-sm);
        }

        .btn-success {
            background: var(--gradient-secondary);
            color: white;
            box-shadow: var(--shadow-sm);
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-md);
        }

        .empty-platform {
            text-align: center;
            padding: var(--space-xl);
            color: var(--text-secondary);
            background: rgba(255, 255, 255, 0.03);
            border-radius: var(--radius-xl);
            border: 1px solid rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(10px);
        }

        .empty-platform i {
            font-size: 2.5rem;
            margin-bottom: var(--space-md);
            opacity: 0.5;
        }

        .download-info {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.3);
            padding: var(--space-lg);
            border-radius: var(--radius-xl);
            margin-bottom: var(--space-xl);
            color: var(--success-color);
            backdrop-filter: blur(10px);
        }

        .download-info h3 {
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .download-info ul {
            list-style: none;
            padding: 0;
        }

        .download-info li {
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .download-info li::before {
            content: '•';
            color: var(--success-color);
            font-weight: bold;
        }

        /* Responsive Mejorado */
        @media (max-width: 768px) {
            .header-container {
                flex-direction: column;
                gap: var(--space-md);
                padding: 0 var(--space-md);
            }

            .main-content {
                padding: var(--space-md);
            }

            .page-title {
                font-size: clamp(1.5rem, 4vw, 1.8rem);
            }

            .apps-grid {
                grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
                gap: var(--space-md);
            }

            .app-card {
                padding: var(--space-md);
            }

            .app-header {
                gap: var(--space-sm);
            }

            .app-icon {
                width: 40px;
                height: 40px;
                font-size: 1.3rem;
            }

            .app-actions {
                flex-direction: column;
                gap: var(--space-xs);
            }

            .btn {
                min-width: auto;
                padding: 0.4rem 0.8rem;
                font-size: 0.75rem;
                width: 100%;
            }

            .platform-icon {
                width: 36px;
                height: 36px;
                font-size: 1.1rem;
            }

            .platform-title {
                font-size: 1.2rem;
            }
        }

        @media (max-width: 480px) {
            .apps-grid {
                grid-template-columns: 1fr;
                gap: var(--space-sm);
            }

            .app-card {
                padding: var(--space-sm);
            }

            .app-header {
                flex-direction: column;
                text-align: center;
                gap: var(--space-sm);
            }

            .page-header {
                margin-bottom: var(--space-lg);
            }

            .platform-section {
                margin-bottom: var(--space-lg);
            }

            .nav-btn span {
                display: none;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-container">
            <a href="user_apps.php" class="logo">
                <i class="fas fa-mobile-alt"></i>
                <span>Aplicaciones IPTV</span>
            </a>
            
            <div class="nav-buttons">
                <a href="index2.php" class="nav-btn back-btn">
                    <i class="fas fa-arrow-left"></i>
                    <span>Volver a Servicios</span>
                </a>
                <a href="index.php" class="nav-btn">
                    <i class="fas fa-home"></i>
                    <span>Inicio</span>
                </a>
            </div>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-mobile-alt" style="color: var(--primary-color);"></i>
                Aplicaciones IPTV Oficiales
            </h1>
            <p class="page-subtitle">
                Descarga las aplicaciones oficiales para disfrutar de tu servicio IPTV en todos tus dispositivos
            </p>
        </div>

        <div class="download-info">
            <h3>
                <i class="fas fa-info-circle"></i>
                Información de Descarga
            </h3>
            <ul>
                <li>Todas las aplicaciones son oficiales y seguras</li>
                <li>Compatibles con tu servicio IPTV actual</li>
                <li>Actualizaciones automáticas disponibles</li>
                <li>Soporte técnico incluido para todas las plataformas</li>
                <li>Configuración automática con tus credenciales</li>
            </ul>
        </div>

        <?php if (empty($apps_by_platform)): ?>
        <div class="empty-platform">
            <i class="fas fa-mobile-alt"></i>
            <h3>No hay aplicaciones disponibles</h3>
            <p>Las aplicaciones estarán disponibles próximamente</p>
        </div>
        <?php else: ?>
            <?php 
            $platform_icons = [
                'android' => 'fab fa-android',
                'ios' => 'fab fa-apple',
                'windows' => 'fab fa-windows',
                'macos' => 'fab fa-apple',
                'smart_tv' => 'fas fa-tv',
                'firestick' => 'fas fa-fire',
                'web' => 'fas fa-globe',
                'other' => 'fas fa-desktop'
            ];
            
            $platform_names = [
                'android' => 'Android',
                'ios' => 'iOS / iPhone / iPad',
                'windows' => 'Windows',
                'macos' => 'macOS',
                'smart_tv' => 'Smart TV',
                'firestick' => 'Amazon Fire TV / Firestick',
                'web' => 'Navegador Web',
                'other' => 'Otras Plataformas'
            ];
            ?>
            
            <?php foreach ($apps_by_platform as $platform => $platform_apps): ?>
            <div class="platform-section">
                <div class="platform-header">
                    <div class="platform-icon">
                        <i class="<?php echo $platform_icons[$platform] ?? 'fas fa-desktop'; ?>"></i>
                    </div>
                    <h2 class="platform-title">
                        <?php echo $platform_names[$platform] ?? ucfirst($platform); ?>
                    </h2>
                </div>
                
                <div class="apps-grid">
                    <?php foreach ($platform_apps as $app): ?>
                    <div class="app-card">
                        <div class="app-header">
                            <div class="app-icon">
                                <i class="<?php echo $platform_icons[$platform] ?? 'fas fa-mobile-alt'; ?>"></i>
                            </div>
                            <div class="app-info">
                                <h3 class="app-name"><?php echo htmlspecialchars($app['name']); ?></h3>
                                <div class="app-version">Versión <?php echo htmlspecialchars($app['version']); ?></div>
                                <?php if ($app['file_size']): ?>
                                <div class="app-size">Tamaño: <?php echo format_file_size($app['file_size']); ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="app-description">
                            <?php echo nl2br(htmlspecialchars($app['description'])); ?>
                        </div>
                        
                        <?php if ($app['features']): ?>
                        <div class="app-features">
                            <div class="features-title">Características:</div>
                            <ul class="features-list">
                                <?php 
                                $features = explode("\n", $app['features']);
                                foreach ($features as $feature): 
                                    $feature = trim($feature);
                                    if ($feature):
                                ?>
                                <li><?php echo htmlspecialchars($feature); ?></li>
                                <?php 
                                    endif;
                                endforeach; 
                                ?>
                            </ul>
                        </div>
                        <?php endif; ?>
                        
                        <div class="app-actions">
                            <?php if (!empty($app['file_path']) && file_exists($app['file_path'])): ?>
                            <!-- Descarga directa de archivo local -->
                            <a href="<?php echo htmlspecialchars($app['file_path']); ?>"
                               class="btn btn-success"
                               download
                               onclick="trackDownload('<?php echo $app['id']; ?>')">
                                <i class="fas fa-download"></i>
                                Descargar APK
                                <?php if (!empty($app['file_size'])): ?>
                                <small style="display: block; font-size: 0.8em; opacity: 0.8;">
                                    (<?php echo formatBytes($app['file_size']); ?>)
                                </small>
                                <?php endif; ?>
                            </a>
                            <?php elseif (!empty($app['download_url'])): ?>
                            <!-- Descarga desde URL externa -->
                            <a href="<?php echo htmlspecialchars($app['download_url']); ?>"
                               class="btn btn-success"
                               target="_blank"
                               onclick="trackDownload('<?php echo $app['id']; ?>')">
                                <i class="fas fa-download"></i>
                                Descargar
                            </a>
                            <?php endif; ?>

                            <?php if (!empty($app['external_url'])): ?>
                            <a href="<?php echo htmlspecialchars($app['external_url']); ?>"
                               class="btn btn-primary"
                               target="_blank">
                                <i class="fas fa-external-link-alt"></i>
                                Ver en Tienda
                            </a>
                            <?php endif; ?>

                            <?php if (empty($app['file_path']) && empty($app['download_url']) && empty($app['external_url'])): ?>
                            <div class="btn" style="background: var(--border-color); cursor: not-allowed;">
                                <i class="fas fa-exclamation-triangle"></i>
                                No disponible
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </main>

    <script>
        // Función para rastrear descargas
        function trackDownload(appId) {
            // En una implementación real, esto enviaría estadísticas al servidor
            console.log('Descarga iniciada para app ID:', appId);
            
            // Opcional: mostrar mensaje de descarga
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: var(--success-color);
                color: white;
                padding: 1rem 1.5rem;
                border-radius: var(--border-radius);
                z-index: 1000;
                animation: slideIn 0.3s ease;
            `;
            notification.innerHTML = '<i class="fas fa-download"></i> Descarga iniciada...';
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // Animación de entrada para las tarjetas
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        });

        document.querySelectorAll('.app-card').forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(card);
        });

        // Detectar plataforma del usuario y resaltar
        function detectPlatform() {
            const userAgent = navigator.userAgent.toLowerCase();
            let platform = 'other';
            
            if (userAgent.includes('android')) {
                platform = 'android';
            } else if (userAgent.includes('iphone') || userAgent.includes('ipad')) {
                platform = 'ios';
            } else if (userAgent.includes('windows')) {
                platform = 'windows';
            } else if (userAgent.includes('mac')) {
                platform = 'macos';
            }
            
            // Resaltar la sección de la plataforma detectada
            const platformSection = document.querySelector(`[data-platform="${platform}"]`);
            if (platformSection) {
                platformSection.style.border = '2px solid var(--accent-color)';
                platformSection.style.background = 'rgba(16, 185, 129, 0.05)';
                
                // Scroll suave a la sección
                setTimeout(() => {
                    platformSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
                }, 500);
            }
        }

        // Ejecutar detección de plataforma
        detectPlatform();

        // Agregar atributos de plataforma a las secciones
        document.querySelectorAll('.platform-section').forEach((section, index) => {
            const platforms = ['android', 'ios', 'windows', 'macos', 'smart_tv', 'firestick', 'web', 'other'];
            if (platforms[index]) {
                section.setAttribute('data-platform', platforms[index]);
            }
        });
    </script>
</body>
</html>
