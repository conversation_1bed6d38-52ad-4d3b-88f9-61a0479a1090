// Script para el modal de renovación de servicio

let currentRenewalStep = 1;
let uploadedRenewalFile = null;

// Abrir modal de renovación
function openRenewalModal() {
    document.getElementById('renewalModal').style.display = 'flex';
    currentRenewalStep = 1;
    updateRenewalProgressIndicator();
    document.body.style.overflow = 'hidden';
}

// Cerrar modal de renovación
function closeRenewalModal() {
    document.getElementById('renewalModal').style.display = 'none';
    document.body.style.overflow = 'auto';
    resetRenewalForm();
}

// Resetear formulario
function resetRenewalForm() {
    document.getElementById('renewalForm').reset();
    currentRenewalStep = 1;
    uploadedRenewalFile = null;
    
    // Ocultar todos los pasos
    document.querySelectorAll('#renewalModal .form-step').forEach(step => {
        step.classList.remove('active');
    });
    
    // Mostrar primer paso
    document.getElementById('renewalStep1').classList.add('active');
    
    // Resetear indicador de progreso
    updateRenewalProgressIndicator();
    
    // Ocultar preview de archivo
    document.getElementById('uploadPreview').style.display = 'none';
    document.getElementById('uploadArea').style.display = 'block';
}

// Navegar al siguiente paso
function nextRenewalStep(step) {
    if (validateRenewalStep(currentRenewalStep)) {
        // Ocultar paso actual
        document.getElementById(`renewalStep${currentRenewalStep}`).classList.remove('active');
        
        // Mostrar siguiente paso
        currentRenewalStep = step;
        document.getElementById(`renewalStep${currentRenewalStep}`).classList.add('active');
        
        // Actualizar indicador de progreso
        updateRenewalProgressIndicator();
        
        // Actualizar resumen si es el último paso
        if (currentRenewalStep === 4) {
            updateRenewalSummary();
        }
    }
}

// Navegar al paso anterior
function prevRenewalStep(step) {
    // Ocultar paso actual
    document.getElementById(`renewalStep${currentRenewalStep}`).classList.remove('active');
    
    // Mostrar paso anterior
    currentRenewalStep = step;
    document.getElementById(`renewalStep${currentRenewalStep}`).classList.add('active');
    
    // Actualizar indicador de progreso
    updateRenewalProgressIndicator();
}

// Validar paso actual
function validateRenewalStep(step) {
    switch(step) {
        case 1:
            const firstName = document.getElementById('renewalFirstName').value.trim();
            const lastName = document.getElementById('renewalLastName').value.trim();
            const email = document.getElementById('renewalEmail').value.trim();
            const phone = document.getElementById('renewalPhone').value.trim();
            const platformId = document.getElementById('platformId').value.trim();
            
            if (!firstName || !lastName || !email || !phone || !platformId) {
                showToast('Por favor completa todos los campos obligatorios', 'error');
                return false;
            }
            
            if (!isValidEmail(email)) {
                showToast('Por favor ingresa un email válido', 'error');
                return false;
            }
            break;
            
        case 2:
            const renewalPeriod = document.querySelector('input[name="renewalPeriod"]:checked');
            if (!renewalPeriod) {
                showToast('Por favor selecciona un plan de renovación', 'error');
                return false;
            }
            break;
            
        case 3:
            // El comprobante de pago es opcional, pero si se sube debe ser válido
            if (uploadedRenewalFile && !isValidFileType(uploadedRenewalFile)) {
                showToast('Por favor sube un archivo válido (JPG, PNG, PDF)', 'error');
                return false;
            }
            break;
    }
    
    return true;
}

// Actualizar indicador de progreso
function updateRenewalProgressIndicator() {
    document.querySelectorAll('#renewalModal .progress-step').forEach((step, index) => {
        if (index + 1 <= currentRenewalStep) {
            step.classList.add('active');
        } else {
            step.classList.remove('active');
        }
    });
}

// Actualizar resumen
function updateRenewalSummary() {
    // Plan seleccionado
    const selectedPlan = document.querySelector('input[name="renewalPeriod"]:checked');
    const planText = selectedPlan ? selectedPlan.nextElementSibling.querySelector('h5').textContent : '-';
    document.getElementById('summaryPlan').textContent = planText;
    
    // ID de plataforma
    const platformId = document.getElementById('platformId').value;
    document.getElementById('summaryPlatformId').textContent = platformId || '-';
    
    // Promoción
    const wantsPromo = document.getElementById('wantsCurrentPromo').checked;
    document.getElementById('summaryPromo').textContent = wantsPromo ? 'Sí, aplicar promoción' : 'No aplicar promoción';
    
    // Comprobante
    const hasPayment = uploadedRenewalFile ? 'Archivo subido' : 'Sin comprobante';
    document.getElementById('summaryPayment').textContent = hasPayment;
}

// Configurar upload de archivos
document.addEventListener('DOMContentLoaded', function() {
    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('paymentProof');
    
    if (uploadArea && fileInput) {
        // Click en área de upload
        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });
        
        // Drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = 'var(--primary-color)';
            uploadArea.style.backgroundColor = 'rgba(255, 255, 255, 0.02)';
        });
        
        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = 'rgba(255, 255, 255, 0.3)';
            uploadArea.style.backgroundColor = 'transparent';
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = 'rgba(255, 255, 255, 0.3)';
            uploadArea.style.backgroundColor = 'transparent';
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleRenewalFileUpload(files[0]);
            }
        });
        
        // Cambio en input de archivo
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleRenewalFileUpload(e.target.files[0]);
            }
        });
    }
});

// Manejar subida de archivo
function handleRenewalFileUpload(file) {
    if (!isValidFileType(file)) {
        showToast('Tipo de archivo no válido. Solo se permiten JPG, PNG y PDF', 'error');
        return;
    }
    
    if (file.size > 5 * 1024 * 1024) { // 5MB
        showToast('El archivo es demasiado grande. Máximo 5MB', 'error');
        return;
    }
    
    uploadedRenewalFile = file;
    
    // Mostrar preview
    document.getElementById('uploadArea').style.display = 'none';
    document.getElementById('uploadPreview').style.display = 'block';
    
    // Actualizar información del archivo
    document.querySelector('#uploadPreview .file-name').textContent = file.name;
    document.querySelector('#uploadPreview .file-size').textContent = formatFileSize(file.size);
    
    showToast('Archivo subido correctamente', 'success');
}

// Remover archivo subido
function removeUploadedFile() {
    uploadedRenewalFile = null;
    document.getElementById('uploadArea').style.display = 'block';
    document.getElementById('uploadPreview').style.display = 'none';
    document.getElementById('paymentProof').value = '';
}

// Enviar formulario de renovación
document.getElementById('renewalForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    if (!validateRenewalStep(4)) {
        return;
    }
    
    const termsAccepted = document.getElementById('renewalTerms').checked;
    if (!termsAccepted) {
        showToast('Debes aceptar los términos y condiciones', 'error');
        return;
    }
    
    const submitBtn = document.getElementById('submitRenewalBtn');
    const originalText = submitBtn.innerHTML;
    
    try {
        // Mostrar loading
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Enviando...';
        submitBtn.disabled = true;
        
        // Preparar datos del formulario
        const formData = new FormData();
        
        // Datos básicos (usando nombres correctos que espera el servidor)
        formData.append('service_type', 'renewal');
        formData.append('device_type', 'android'); // Valor por defecto para renovaciones
        formData.append('firstName', document.getElementById('renewalFirstName').value);
        formData.append('lastName', document.getElementById('renewalLastName').value);
        formData.append('email', document.getElementById('renewalEmail').value);
        formData.append('phone', document.getElementById('renewalPhone').value);
        formData.append('platformId', document.getElementById('platformId').value);

        // Plan de renovación
        const selectedPlan = document.querySelector('input[name="renewalPeriod"]:checked');
        if (selectedPlan) {
            formData.append('renewalPeriod', selectedPlan.value);
        }

        // Promoción
        const wantsPromo = document.getElementById('wantsCurrentPromo').checked;
        formData.append('wantsCurrentPromo', wantsPromo ? '1' : '0');

        // Archivo de comprobante
        if (uploadedRenewalFile) {
            console.log('📎 Adjuntando archivo:', uploadedRenewalFile.name, uploadedRenewalFile.size, 'bytes');
            formData.append('paymentProof', uploadedRenewalFile);
        } else {
            console.log('⚠️ No hay archivo de comprobante');
        }

        // Términos
        formData.append('acceptTerms', 'on');
        
        // Debug: Mostrar datos que se van a enviar
        console.log('📤 Enviando datos de renovación:');
        for (let [key, value] of formData.entries()) {
            if (value instanceof File) {
                console.log(`  ${key}: ${value.name} (${value.size} bytes, ${value.type})`);
            } else {
                console.log(`  ${key}: ${value}`);
            }
        }

        // Enviar solicitud
        console.log('🚀 Enviando petición a submit_service_request.php...');
        const response = await fetch('submit_service_request.php', {
            method: 'POST',
            body: formData
        });

        console.log('📥 Respuesta recibida:', response.status, response.statusText);

        // Obtener texto de respuesta primero para debug
        const responseText = await response.text();
        console.log('📄 Texto de respuesta:', responseText);

        // Intentar parsear como JSON
        let result;
        try {
            result = JSON.parse(responseText);
            console.log('✅ JSON parseado:', result);
        } catch (parseError) {
            console.error('❌ Error parseando JSON:', parseError);
            console.error('📄 Respuesta cruda:', responseText);
            throw new Error('Respuesta del servidor no es JSON válido: ' + responseText.substring(0, 100));
        }
        
        if (result.success) {
            showToast('¡Solicitud de renovación enviada exitosamente!', 'success');
            closeRenewalModal();
            
            // Mostrar confirmación
            setTimeout(() => {
                if (confirm('¿Deseas ver los detalles de tu solicitud de renovación?')) {
                    window.location.href = 'service_request_confirmation.php?id=' + result.request_id;
                }
            }, 1000);
        } else {
            showToast(result.message || 'Error al enviar la solicitud de renovación', 'error');
        }
        
    } catch (error) {
        console.error('Error:', error);
        showToast('Error de conexión. Por favor intenta nuevamente.', 'error');
    } finally {
        // Restaurar botón
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }
});

// Funciones auxiliares
function isValidFileType(file) {
    const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
    return validTypes.includes(file.type);
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Cerrar modal al hacer clic fuera
document.addEventListener('click', function(e) {
    if (e.target.id === 'renewalModal') {
        closeRenewalModal();
    }
});

// Cerrar modal con tecla Escape
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape' && document.getElementById('renewalModal').style.display === 'flex') {
        closeRenewalModal();
    }
});
