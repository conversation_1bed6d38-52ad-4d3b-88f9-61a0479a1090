<?php
session_start();
require_once 'config.php';

// Verificar si el usuario está logueado como admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

// Obtener estadísticas de soporte (datos reales)
try {
    // Estadísticas de tickets
    $stmt = $pdo->query("SELECT COUNT(*) FROM support_tickets WHERE status = 'open'");
    $tickets_open = $stmt->fetchColumn() ?: 0;

    $stmt = $pdo->query("SELECT COUNT(*) FROM support_tickets WHERE status = 'in_progress'");
    $tickets_in_progress = $stmt->fetchColumn() ?: 0;

    $stmt = $pdo->query("SELECT COUNT(*) FROM support_tickets WHERE status = 'resolved'");
    $tickets_resolved = $stmt->fetchColumn() ?: 0;

    $stmt = $pdo->query("SELECT COUNT(*) FROM support_tickets");
    $tickets_total = $stmt->fetchColumn() ?: 0;

    $tickets_stats = [
        'open' => $tickets_open,
        'in_progress' => $tickets_in_progress,
        'resolved' => $tickets_resolved,
        'total' => $tickets_total
    ];

    // Estadísticas de chat
    $stmt = $pdo->query("SELECT COUNT(*) FROM chat_sessions WHERE status = 'active'");
    $chat_active = $stmt->fetchColumn() ?: 0;

    $stmt = $pdo->query("SELECT COUNT(*) FROM chat_sessions WHERE status = 'waiting'");
    $chat_waiting = $stmt->fetchColumn() ?: 0;

    $stmt = $pdo->query("SELECT COUNT(*) FROM chat_sessions WHERE DATE(started_at) = CURDATE()");
    $chat_today = $stmt->fetchColumn() ?: 0;

    $chat_stats = [
        'active_sessions' => $chat_active,
        'waiting' => $chat_waiting,
        'today_sessions' => $chat_today
    ];

    // Estadísticas de aplicaciones
    $app_stats = [
        'downloads_today' => 0, // Esto requeriría una tabla de descargas
        'total_downloads' => 0
    ];

    // Estadísticas de canales
    $stmt = $pdo->query("SELECT COUNT(*) FROM channel_requests WHERE status = 'pending'");
    $channels_pending = $stmt->fetchColumn() ?: 0;

    $stmt = $pdo->query("SELECT COUNT(*) FROM channel_requests WHERE status = 'approved' AND DATE(updated_at) = CURDATE()");
    $channels_approved_today = $stmt->fetchColumn() ?: 0;

    $channel_stats = [
        'pending_requests' => $channels_pending,
        'approved_today' => $channels_approved_today
    ];

    // Estadísticas de activaciones
    $stmt = $pdo->query("SELECT COUNT(*) FROM activation_codes WHERE status = 'active'");
    $activations_pending = $stmt->fetchColumn() ?: 0;

    $stmt = $pdo->query("SELECT COUNT(*) FROM user_activations WHERE DATE(activated_at) = CURDATE()");
    $activations_today = $stmt->fetchColumn() ?: 0;

    $activation_stats = [
        'pending' => $activations_pending,
        'completed_today' => $activations_today
    ];

} catch (Exception $e) {
    $error_message = "Error al obtener estadísticas: " . $e->getMessage();
    // Fallback a datos vacíos
    $tickets_stats = ['open' => 0, 'in_progress' => 0, 'resolved' => 0, 'total' => 0];
    $chat_stats = ['active_sessions' => 0, 'waiting' => 0, 'today_sessions' => 0];
    $app_stats = ['downloads_today' => 0, 'total_downloads' => 0];
    $channel_stats = ['pending_requests' => 0, 'approved_today' => 0];
    $activation_stats = ['pending' => 0, 'completed_today' => 0];
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎧 Admin Soporte - RGS TOOL</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            /* Paleta de colores negro y rojo */
            --primary-color: #dc2626;
            --primary-dark: #b91c1c;
            --secondary-color: #1f1f1f;
            --dark-bg: #000000;
            --darker-bg: #0a0a0a;
            --text-primary: #ffffff;
            --text-secondary: #d1d5db;
            --accent-color: #dc2626;
            --accent-dark: #b91c1c;
            --warning-color: #f59e0b;
            --error-color: #dc2626;
            --success-color: #dc2626;
            --info-color: #dc2626;
            --support-color: #dc2626;
            --support-dark: #b91c1c;
            --border-color: #3a3a3a;
            --border-light: #475569;
            --gradient-primary: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            --gradient-secondary: linear-gradient(135deg, #10b981 0%, #059669 100%);
            --gradient-support: linear-gradient(135deg, #e91e63 0%, #c2185b 100%);
            --gradient-dark: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
            --gradient-bg: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            --shadow-light: 0 2px 8px rgba(0,0,0,0.3);
            --shadow-medium: 0 4px 16px rgba(0,0,0,0.4);
            --shadow-heavy: 0 8px 32px rgba(0,0,0,0.6);
            --border-radius: 12px;
            --border-radius-lg: 16px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gradient-bg);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
        }

        /* Header */
        .header {
            background: var(--secondary-color);
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
            backdrop-filter: blur(10px);
        }

        .header-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--support-color);
            text-decoration: none;
        }

        .logo i {
            font-size: 1.8rem;
        }

        .nav-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .nav-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            background: transparent;
            color: var(--text-secondary);
            text-decoration: none;
            border-radius: var(--border-radius);
            transition: var(--transition);
            font-weight: 500;
            border: 1px solid transparent;
        }

        .nav-btn:hover {
            background: rgba(255,255,255,0.1);
            color: var(--text-primary);
            transform: translateY(-2px);
        }

        .nav-btn.back-btn {
            background: var(--gradient-primary);
            color: white;
            border: 1px solid var(--primary-color);
        }

        .nav-btn.back-btn:hover {
            background: var(--primary-dark);
            box-shadow: var(--shadow-light);
        }

        /* Main Content */
        .main-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem 1.5rem;
        }

        .page-header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .page-title {
            font-size: 2.5rem;
            font-weight: 700;
            background: var(--gradient-support);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 0.5rem;
        }

        .page-subtitle {
            color: var(--text-secondary);
            font-size: 1.1rem;
        }

        /* Stats Section */
        .stats-section {
            margin-bottom: 3rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--secondary-color);
            padding: 1.5rem;
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-support);
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-medium);
        }

        .stat-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;
        }

        .stat-icon {
            width: 50px;
            height: 50px;
            border-radius: var(--border-radius);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            background: rgba(233, 30, 99, 0.2);
            color: var(--support-color);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-primary);
            line-height: 1;
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin-top: 0.5rem;
        }

        /* Services Grid */
        .services-section {
            margin-bottom: 3rem;
        }

        .section-title {
            font-size: 1.8rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .service-card {
            background: var(--secondary-color);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
            overflow: hidden;
            transition: var(--transition);
            position: relative;
        }

        .service-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-heavy);
        }

        .service-header {
            padding: 2rem;
            background: var(--gradient-dark);
            text-align: center;
        }

        .service-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            margin: 0 auto 1rem;
            background: var(--gradient-support);
            color: white;
        }

        .service-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .service-description {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .service-content {
            padding: 2rem;
        }

        .service-actions {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .service-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 1rem;
            background: var(--gradient-primary);
            color: white;
            text-decoration: none;
            border-radius: var(--border-radius);
            font-weight: 500;
            transition: var(--transition);
            border: none;
            cursor: pointer;
        }

        .service-btn:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: var(--shadow-light);
        }

        .service-btn.secondary {
            background: transparent;
            color: var(--text-secondary);
            border: 1px solid var(--border-color);
        }

        .service-btn.secondary:hover {
            background: rgba(255,255,255,0.1);
            color: var(--text-primary);
        }

        /* Animaciones para notificaciones */
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        @keyframes slideOut {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }

        .notification-badge {
            animation: pulse 2s infinite;
            box-shadow: 0 2px 8px rgba(239, 68, 68, 0.4);
        }

        .service-card {
            transition: all 0.3s ease;
        }

        .service-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.6);
        }

        .stat-card {
            transition: all 0.3s ease;
        }

        .stat-number {
            transition: all 0.3s ease;
        }

        /* Indicadores de estado */
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }

        .status-online { background: #10b981; animation: pulse 2s infinite; }
        .status-busy { background: #f59e0b; }
        .status-offline { background: #6b7280; }

        /* Responsive */
        @media (max-width: 768px) {
            .header-container {
                flex-direction: column;
                gap: 1rem;
            }

            .nav-buttons {
                width: 100%;
                justify-content: center;
            }

            .services-grid {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-container">
            <a href="admin2.php" class="logo">
                <i class="fas fa-headset"></i>
                <span>Admin Soporte</span>
            </a>
            
            <div class="nav-buttons">
                <a href="admin.php" class="nav-btn back-btn">
                    <i class="fas fa-arrow-left"></i>
                    <span>Volver a Admin</span>
                </a>
                <a href="index.php" class="nav-btn">
                    <i class="fas fa-home"></i>
                    <span>Inicio</span>
                </a>
                <a href="admin_logout.php" class="nav-btn">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Salir</span>
                </a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-headset"></i>
                Panel de Administración de Soporte
            </h1>
            <p class="page-subtitle">
                Gestiona tickets, chats, aplicaciones, canales y activaciones de listas
            </p>

            <!-- Aviso de migración -->
            <div style="background: rgba(233, 30, 99, 0.1); border: 1px solid #e91e63; border-radius: 12px; padding: 1.5rem; margin: 2rem 0; text-align: center;">
                <h3 style="color: #e91e63; margin-bottom: 1rem;">
                    <i class="fas fa-info-circle"></i>
                    Panel Integrado Disponible
                </h3>
                <p style="color: var(--text-secondary); margin-bottom: 1.5rem;">
                    Las funcionalidades de soporte ahora están integradas directamente en el panel principal de administración para una mejor experiencia.
                    Este panel seguirá funcionando, pero recomendamos usar el panel integrado.
                </p>
                <a href="admin.php" style="display: inline-flex; align-items: center; gap: 0.5rem; padding: 1rem 2rem; background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%); color: white; text-decoration: none; border-radius: 8px; font-weight: 500; transition: all 0.3s ease; margin-right: 1rem;">
                    <i class="fas fa-arrow-left"></i>
                    Ir al Panel Principal Integrado
                </a>
                <button onclick="this.parentElement.style.display='none'" style="display: inline-flex; align-items: center; gap: 0.5rem; padding: 1rem 2rem; background: transparent; color: var(--text-secondary); border: 1px solid var(--border-color); border-radius: 8px; font-weight: 500; cursor: pointer; transition: all 0.3s ease;">
                    <i class="fas fa-times"></i>
                    Cerrar Aviso
                </button>
            </div>
        </div>

        <!-- Stats Section -->
        <section class="stats-section">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-icon">
                            <i class="fas fa-ticket-alt"></i>
                        </div>
                    </div>
                    <div class="stat-number"><?php echo $tickets_stats['open']; ?></div>
                    <div class="stat-label">Tickets Abiertos</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-icon">
                            <i class="fas fa-comments"></i>
                        </div>
                    </div>
                    <div class="stat-number"><?php echo $chat_stats['active_sessions']; ?></div>
                    <div class="stat-label">Chats Activos</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-icon">
                            <i class="fas fa-download"></i>
                        </div>
                    </div>
                    <div class="stat-number"><?php echo $app_stats['downloads_today']; ?></div>
                    <div class="stat-label">Descargas Hoy</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-icon">
                            <i class="fas fa-tv"></i>
                        </div>
                    </div>
                    <div class="stat-number"><?php echo $channel_stats['pending_requests']; ?></div>
                    <div class="stat-label">Canales Pendientes</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-icon">
                            <i class="fas fa-key"></i>
                        </div>
                    </div>
                    <div class="stat-number"><?php echo $activation_stats['pending']; ?></div>
                    <div class="stat-label">Activaciones Pendientes</div>
                </div>
            </div>
        </section>

        <!-- Services Section -->
        <section class="services-section">
            <h2 class="section-title">
                <i class="fas fa-tools"></i>
                Servicios de Soporte
            </h2>

            <div class="services-grid">
                <!-- Gestión de Tickets -->
                <div class="service-card">
                    <div class="service-header">
                        <div class="service-icon">
                            <i class="fas fa-ticket-alt"></i>
                        </div>
                        <h3 class="service-title">Gestión de Tickets</h3>
                        <p class="service-description">
                            Administra tickets de soporte, responde consultas y gestiona el estado de los casos
                        </p>
                    </div>
                    <div class="service-content">
                        <div class="service-actions">
                            <a href="tickets_admin.php" class="service-btn">
                                <i class="fas fa-list"></i>
                                Ver Todos los Tickets
                            </a>
                            <a href="tickets_admin.php?status=open" class="service-btn secondary">
                                <i class="fas fa-exclamation-circle"></i>
                                Tickets Abiertos (<?php echo $tickets_stats['open']; ?>)
                            </a>
                            <a href="tickets_admin.php?status=in_progress" class="service-btn secondary">
                                <i class="fas fa-clock"></i>
                                En Progreso (<?php echo $tickets_stats['in_progress']; ?>)
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Chat en Vivo -->
                <div class="service-card">
                    <div class="service-header">
                        <div class="service-icon">
                            <i class="fas fa-comments"></i>
                        </div>
                        <h3 class="service-title">Chat en Vivo</h3>
                        <p class="service-description">
                            Gestiona conversaciones en tiempo real con los usuarios del sistema
                        </p>
                    </div>
                    <div class="service-content">
                        <div class="service-actions">
                            <a href="admin_chat_real.php" class="service-btn">
                                <i class="fas fa-comment-dots"></i>
                                Chat en Tiempo Real
                            </a>
                            <a href="admin_chat_real.php" class="service-btn secondary">
                                <i class="fas fa-circle" style="color: #10b981;"></i>
                                Sesiones Activas (<?php echo $chat_stats['active_sessions']; ?>)
                            </a>
                            <a href="admin_chat_real.php" class="service-btn secondary">
                                <i class="fas fa-hourglass-half"></i>
                                En Espera (<?php echo $chat_stats['waiting']; ?>)
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Gestión de Aplicaciones -->
                <div class="service-card">
                    <div class="service-header">
                        <div class="service-icon">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <h3 class="service-title">Aplicaciones</h3>
                        <p class="service-description">
                            Administra aplicaciones disponibles para descarga y sus versiones
                        </p>
                    </div>
                    <div class="service-content">
                        <div class="service-actions">
                            <a href="apps_admin.php" class="service-btn">
                                <i class="fas fa-upload"></i>
                                Gestionar Aplicaciones
                            </a>
                            <a href="apps_admin.php?view=stats" class="service-btn secondary">
                                <i class="fas fa-chart-bar"></i>
                                Estadísticas de Descarga
                            </a>
                            <a href="apps_admin.php?action=upload" class="service-btn secondary">
                                <i class="fas fa-plus"></i>
                                Subir Nueva App
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Centro de Ayuda -->
                <div class="service-card">
                    <div class="service-header">
                        <div class="service-icon">
                            <i class="fas fa-question-circle"></i>
                        </div>
                        <h3 class="service-title">Centro de Ayuda</h3>
                        <p class="service-description">
                            Gestiona documentación, tutoriales y preguntas frecuentes
                        </p>
                    </div>
                    <div class="service-content">
                        <div class="service-actions">
                            <a href="help_admin.php" class="service-btn">
                                <i class="fas fa-book"></i>
                                Gestionar Contenido
                            </a>
                            <a href="help_admin.php?section=faq" class="service-btn secondary">
                                <i class="fas fa-question"></i>
                                Preguntas Frecuentes
                            </a>
                            <a href="help_admin.php?section=tutorials" class="service-btn secondary">
                                <i class="fas fa-play-circle"></i>
                                Tutoriales
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Solicitar Canales -->
                <div class="service-card">
                    <div class="service-header">
                        <div class="service-icon">
                            <i class="fas fa-tv"></i>
                        </div>
                        <h3 class="service-title">Solicitudes de Canales</h3>
                        <p class="service-description">
                            Gestiona solicitudes de nuevos canales y su aprobación
                        </p>
                    </div>
                    <div class="service-content">
                        <div class="service-actions">
                            <a href="admin_channels_real.php" class="service-btn">
                                <i class="fas fa-list-ul"></i>
                                Ver Solicitudes
                            </a>
                            <a href="channels_admin.php?status=pending" class="service-btn secondary">
                                <i class="fas fa-clock"></i>
                                Pendientes (<?php echo $channel_stats['pending_requests']; ?>)
                            </a>
                            <a href="channels_admin.php?action=add" class="service-btn secondary">
                                <i class="fas fa-plus"></i>
                                Agregar Canal
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Activación de Listas -->
                <div class="service-card">
                    <div class="service-header">
                        <div class="service-icon">
                            <i class="fas fa-key"></i>
                        </div>
                        <h3 class="service-title">Activación de Listas</h3>
                        <p class="service-description">
                            Gestiona activaciones de listas M3U y códigos de acceso
                        </p>
                    </div>
                    <div class="service-content">
                        <div class="service-actions">
                            <a href="activations_admin.php" class="service-btn">
                                <i class="fas fa-unlock"></i>
                                Gestionar Activaciones
                            </a>
                            <a href="activations_admin.php?status=pending" class="service-btn secondary">
                                <i class="fas fa-hourglass-half"></i>
                                Pendientes (<?php echo $activation_stats['pending']; ?>)
                            </a>
                            <a href="activations_admin.php?action=generate" class="service-btn secondary">
                                <i class="fas fa-key"></i>
                                Generar Códigos
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Quick Actions -->
        <section class="services-section">
            <h2 class="section-title">
                <i class="fas fa-bolt"></i>
                Acciones Rápidas
            </h2>

            <div class="services-grid" style="grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));">
                <div class="service-card" style="background: rgba(16, 185, 129, 0.1); border-color: var(--accent-color);">
                    <div class="service-content" style="padding: 1.5rem; text-align: center;">
                        <div class="service-icon" style="margin: 0 auto 1rem; background: var(--gradient-secondary);">
                            <i class="fas fa-broadcast-tower"></i>
                        </div>
                        <h3 class="service-title" style="margin-bottom: 1rem;">Estado del Sistema</h3>
                        <div class="service-actions">
                            <a href="system_status.php" class="service-btn">
                                <i class="fas fa-heartbeat"></i>
                                Ver Estado
                            </a>
                        </div>
                    </div>
                </div>

                <div class="service-card" style="background: rgba(245, 158, 11, 0.1); border-color: var(--warning-color);">
                    <div class="service-content" style="padding: 1.5rem; text-align: center;">
                        <div class="service-icon" style="margin: 0 auto 1rem; background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);">
                            <i class="fas fa-bell"></i>
                        </div>
                        <h3 class="service-title" style="margin-bottom: 1rem;">Notificaciones</h3>
                        <div class="service-actions">
                            <a href="notifications_admin.php" class="service-btn">
                                <i class="fas fa-envelope"></i>
                                Gestionar
                            </a>
                        </div>
                    </div>
                </div>

                <div class="service-card" style="background: rgba(239, 68, 68, 0.1); border-color: var(--error-color);">
                    <div class="service-content" style="padding: 1.5rem; text-align: center;">
                        <div class="service-icon" style="margin: 0 auto 1rem; background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h3 class="service-title" style="margin-bottom: 1rem;">Seguridad</h3>
                        <div class="service-actions">
                            <a href="security_admin.php" class="service-btn">
                                <i class="fas fa-lock"></i>
                                Panel
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <script>
        let lastNotificationCount = 0;

        // Auto-refresh stats every 10 seconds para mejor responsividad
        async function updateStats() {
            try {
                const response = await fetch('api_support_stats.php');
                const data = await response.json();

                if (data.success) {
                    // Update stat numbers con animación
                    const statElements = document.querySelectorAll('.stat-number');
                    const stats = [
                        data.tickets_open,
                        data.chat_active,
                        data.downloads_today,
                        data.channels_pending,
                        data.activations_pending
                    ];

                    statElements.forEach((element, index) => {
                        if (stats[index] !== undefined) {
                            const currentValue = parseInt(element.textContent) || 0;
                            const newValue = stats[index];

                            if (currentValue !== newValue) {
                                // Animación de cambio
                                element.style.transform = 'scale(1.1)';
                                element.style.color = newValue > currentValue ? '#10b981' : '#ef4444';

                                setTimeout(() => {
                                    element.textContent = newValue;
                                    element.style.transform = 'scale(1)';
                                    element.style.color = 'var(--text-primary)';
                                }, 200);
                            }
                        }
                    });

                    // Actualizar contadores en botones secundarios
                    updateServiceCounters(data);

                    // Mostrar notificaciones si hay nuevas
                    if (data.stats && data.stats.notifications_unread > lastNotificationCount) {
                        showNotificationAlert(data.stats.notifications_unread - lastNotificationCount);
                        lastNotificationCount = data.stats.notifications_unread;
                    }

                    // Actualizar badges de notificaciones
                    updateNotificationBadges(data.stats);
                }
            } catch (error) {
                console.log('Stats update failed:', error);
            }
        }

        function updateServiceCounters(data) {
            // Actualizar contadores en los botones de servicios
            const ticketsOpenBtn = document.querySelector('a[href="tickets_admin.php?status=open"]');
            if (ticketsOpenBtn) {
                ticketsOpenBtn.innerHTML = `
                    <i class="fas fa-exclamation-circle"></i>
                    Tickets Abiertos (${data.tickets_open})
                `;
            }

            const ticketsProgressBtn = document.querySelector('a[href="tickets_admin.php?status=in_progress"]');
            if (ticketsProgressBtn) {
                ticketsProgressBtn.innerHTML = `
                    <i class="fas fa-clock"></i>
                    En Progreso (${data.stats?.tickets_in_progress || 0})
                `;
            }

            const chatActiveBtn = document.querySelector('a[href="admin_chat_real.php"] .service-btn.secondary');
            if (chatActiveBtn) {
                chatActiveBtn.innerHTML = `
                    <i class="fas fa-circle" style="color: #10b981;"></i>
                    Sesiones Activas (${data.chat_active})
                `;
            }

            const channelsPendingBtn = document.querySelector('a[href="channels_admin.php?status=pending"]');
            if (channelsPendingBtn) {
                channelsPendingBtn.innerHTML = `
                    <i class="fas fa-clock"></i>
                    Pendientes (${data.channels_pending})
                `;
            }
        }

        function updateNotificationBadges(stats) {
            if (!stats || !stats.notification_counters) return;

            // Agregar badges a las tarjetas de servicios
            const serviceCards = document.querySelectorAll('.service-card');
            serviceCards.forEach(card => {
                const title = card.querySelector('.service-title');
                if (!title) return;

                // Remover badge existente
                const existingBadge = card.querySelector('.notification-badge');
                if (existingBadge) existingBadge.remove();

                let count = 0;
                const titleText = title.textContent.toLowerCase();

                if (titleText.includes('ticket') && stats.notification_counters.ticket) {
                    count = stats.notification_counters.ticket;
                } else if (titleText.includes('chat') && stats.notification_counters.chat) {
                    count = stats.notification_counters.chat;
                } else if (titleText.includes('aplicaciones') && stats.notification_counters.app_download) {
                    count = stats.notification_counters.app_download;
                } else if (titleText.includes('canales') && stats.notification_counters.channel_request) {
                    count = stats.notification_counters.channel_request;
                }

                if (count > 0) {
                    const badge = document.createElement('span');
                    badge.className = 'notification-badge';
                    badge.textContent = count;
                    badge.style.cssText = `
                        position: absolute;
                        top: 10px;
                        right: 10px;
                        background: #ef4444;
                        color: white;
                        border-radius: 50%;
                        width: 24px;
                        height: 24px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-size: 0.8rem;
                        font-weight: bold;
                        animation: pulse 2s infinite;
                    `;
                    card.style.position = 'relative';
                    card.appendChild(badge);
                }
            });
        }

        function showNotificationAlert(newCount) {
            // Crear notificación temporal
            const alert = document.createElement('div');
            alert.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #10b981;
                color: white;
                padding: 1rem 1.5rem;
                border-radius: 8px;
                box-shadow: 0 4px 16px rgba(0,0,0,0.4);
                z-index: 10000;
                animation: slideIn 0.3s ease;
            `;
            alert.innerHTML = `
                <div style="display: flex; align-items: center; gap: 0.5rem;">
                    <i class="fas fa-bell"></i>
                    <span>${newCount} nueva${newCount > 1 ? 's' : ''} notificación${newCount > 1 ? 'es' : ''}</span>
                </div>
            `;

            document.body.appendChild(alert);

            setTimeout(() => {
                alert.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => alert.remove(), 300);
            }, 3000);
        }

        // Inicializar
        updateStats();
        setInterval(updateStats, 10000); // Cada 10 segundos

        // Add loading states to buttons
        document.querySelectorAll('.service-btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                if (!this.classList.contains('secondary')) {
                    const icon = this.querySelector('i');
                    const originalClass = icon.className;
                    icon.className = 'fas fa-spinner fa-spin';

                    setTimeout(() => {
                        icon.className = originalClass;
                    }, 1000);
                }
            });
        });
    </script>
</body>
</html>
