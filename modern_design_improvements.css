/* 🎨 MEJORAS MODERNAS DE DISEÑO PARA INDEX.PHP
   Mejoras visuales user-friendly y modernas */

/* ===== VARIABLES MEJORADAS ===== */
:root {
    /* Paleta de colores moderna y accesible */
    --primary-color: #6366f1;
    --primary-dark: #4f46e5;
    --primary-light: #a5b4fc;
    --secondary-color: #1e293b;
    --dark-bg: #0f172a;
    --darker-bg: #020617;
    --surface: #334155;
    --surface-light: #475569;
    
    /* Colores de texto mejorados */
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-muted: #94a3b8;
    
    /* Colores de acento modernos */
    --accent-color: #10b981;
    --accent-secondary: #06b6d4;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --success-color: #22c55e;
    
    /* Gradientes modernos */
    --gradient-primary: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    --gradient-secondary: linear-gradient(135deg, #10b981 0%, #06b6d4 100%);
    --gradient-surface: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    --gradient-hero: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
    
    /* Sombras mejoradas */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-glow: 0 0 20px rgba(99, 102, 241, 0.3);
    
    /* Espaciado consistente */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-2xl: 3rem;
    
    /* Border radius moderno */
    --radius-sm: 6px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
    --radius-full: 9999px;
    
    /* Transiciones suaves */
    --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===== MEJORAS GENERALES ===== */
body {
    background: var(--gradient-hero);
    background-attachment: fixed;
    font-feature-settings: "kern" 1, "liga" 1;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* ===== HEADER MEJORADO ===== */
.header {
    background: rgba(30, 41, 59, 0.8);
    backdrop-filter: blur(20px) saturate(180%);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: var(--shadow-lg);
}

.header-container {
    max-width: 1200px;
    padding: 0 var(--space-lg);
}

.logo {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 800;
    letter-spacing: -0.025em;
}

/* ===== BÚSQUEDA MEJORADA ===== */
.search-container {
    position: relative;
}

.search-form {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-full);
    backdrop-filter: blur(10px);
    transition: var(--transition-normal);
}

.search-form:focus-within {
    background: rgba(255, 255, 255, 0.08);
    border-color: var(--primary-color);
    box-shadow: var(--shadow-glow);
    transform: translateY(-1px);
}

.search-input {
    padding: var(--space-md) var(--space-lg);
    font-size: 0.95rem;
    font-weight: 500;
}

.search-btn {
    background: var(--gradient-primary);
    padding: var(--space-md) var(--space-xl);
    font-weight: 600;
    border-radius: var(--radius-full);
    box-shadow: var(--shadow-md);
}

.search-btn:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

/* ===== NAVEGACIÓN MEJORADA ===== */
.nav-btn {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    backdrop-filter: blur(10px);
    font-weight: 500;
    transition: var(--transition-normal);
}

.nav-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* ===== NOTIFICACIONES MEJORADAS ===== */
.notification-badge {
    background: var(--gradient-secondary);
    box-shadow: var(--shadow-md);
    animation: pulse 2s ease-in-out infinite;
}

.notification-modal {
    backdrop-filter: blur(8px) saturate(180%);
}

.notification-content {
    background: var(--gradient-surface);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
}

.notification-item {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: var(--radius-lg);
    border-left: 3px solid var(--primary-color);
    transition: var(--transition-normal);
}

.notification-item:hover {
    background: rgba(255, 255, 255, 0.06);
    transform: translateX(4px);
    box-shadow: var(--shadow-md);
}

/* ===== HERO SECTION MEJORADA ===== */
.hero {
    background: var(--gradient-hero);
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 20%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, rgba(16, 185, 129, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.welcome-content h1 {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 800;
    letter-spacing: -0.025em;
}

.service-badge {
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    box-shadow: var(--shadow-md);
    font-weight: 600;
}

.status-indicator {
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.2);
    border-radius: var(--radius-lg);
    backdrop-filter: blur(10px);
}

/* ===== TARJETAS DE SERVICIO MEJORADAS ===== */
.service-card {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: var(--radius-xl);
    backdrop-filter: blur(10px);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: var(--transition-normal);
}

.service-card:hover {
    background: rgba(255, 255, 255, 0.06);
    border-color: var(--primary-color);
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.service-card:hover::before {
    transform: scaleX(1);
}

.service-icon {
    background: var(--gradient-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
}

/* ===== TABS MEJORADAS ===== */
.nav-tabs {
    background: rgba(15, 23, 42, 0.8);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.tab-btn {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: var(--radius-lg);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.tab-btn::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: var(--transition-normal);
}

.tab-btn:hover {
    background: rgba(255, 255, 255, 0.06);
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.tab-btn.active {
    background: rgba(99, 102, 241, 0.1);
    border-color: var(--primary-color);
    color: var(--primary-light);
    box-shadow: var(--shadow-md);
}

.tab-btn.active::before {
    transform: scaleX(1);
}

/* ===== GRID DE PELÍCULAS MEJORADO ===== */
.movies-grid {
    gap: var(--space-xl);
}

.movie-card {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: var(--radius-xl);
    backdrop-filter: blur(10px);
    transition: var(--transition-normal);
    overflow: hidden;
}

.movie-card:hover {
    background: rgba(255, 255, 255, 0.06);
    border-color: var(--primary-color);
    transform: translateY(-12px) scale(1.02);
    box-shadow: var(--shadow-xl);
}

.movie-overlay {
    background: linear-gradient(
        to bottom,
        transparent 0%,
        rgba(0, 0, 0, 0.3) 40%,
        rgba(0, 0, 0, 0.8) 100%
    );
    backdrop-filter: blur(2px);
}

.movie-card:hover .movie-overlay {
    backdrop-filter: blur(4px);
}

/* ===== BOTONES MEJORADOS ===== */
.btn-order {
    background: var(--gradient-primary);
    border-radius: var(--radius-lg);
    font-weight: 600;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
}

.btn-order:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.btn-details {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-lg);
    backdrop-filter: blur(10px);
    transition: var(--transition-normal);
}

.btn-details:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
}

/* ===== ANIMACIONES MEJORADAS ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.8;
    }
}

/* ===== RESPONSIVE MEJORADO ===== */
@media (max-width: 768px) {
    .header-container {
        flex-direction: column;
        gap: var(--space-md);
        padding: var(--space-md);
    }
    
    .search-container {
        order: -1;
        width: 100%;
    }
    
    .movies-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: var(--space-md);
    }
    
    .services-grid {
        grid-template-columns: 1fr;
        gap: var(--space-md);
    }
}

/* ===== EFECTOS DE CARGA ===== */
.loading-skeleton {
    background: linear-gradient(90deg, 
        rgba(255, 255, 255, 0.03) 25%, 
        rgba(255, 255, 255, 0.06) 50%, 
        rgba(255, 255, 255, 0.03) 75%
    );
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* ===== MEJORAS DE ACCESIBILIDAD ===== */
.focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}
