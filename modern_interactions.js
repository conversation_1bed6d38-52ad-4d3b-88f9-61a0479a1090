// 🚀 MEJORAS MODERNAS DE INTERACTIVIDAD PARA INDEX.PHP
// Funcionalidades user-friendly y animaciones suaves

document.addEventListener('DOMContentLoaded', function() {
    initModernFeatures();
});

function initModernFeatures() {
    // Inicializar todas las mejoras modernas
    initSmoothScrolling();
    initLazyLoading();
    initAnimationsOnScroll();
    initAdvancedSearch();
    initKeyboardNavigation();
    initTooltips();
    initProgressIndicators();
    initThemeToggle();
    initAdvancedNotifications();
}

// ===== SMOOTH SCROLLING MEJORADO =====
function initSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// ===== LAZY LOADING AVANZADO =====
function initLazyLoading() {
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                const src = img.dataset.src;
                
                if (src) {
                    // Crear imagen temporal para precargar
                    const tempImg = new Image();
                    tempImg.onload = () => {
                        img.src = src;
                        img.classList.add('loaded');
                        img.removeAttribute('data-src');
                    };
                    tempImg.src = src;
                }
                
                observer.unobserve(img);
            }
        });
    }, {
        rootMargin: '50px 0px',
        threshold: 0.1
    });

    document.querySelectorAll('img[data-src]').forEach(img => {
        imageObserver.observe(img);
    });
}

// ===== ANIMACIONES AL HACER SCROLL =====
function initAnimationsOnScroll() {
    const animationObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });

    // Agregar clases de animación a elementos
    document.querySelectorAll('.movie-card, .service-card, .section-title').forEach(el => {
        el.classList.add('animate-on-scroll');
        animationObserver.observe(el);
    });
}

// ===== BÚSQUEDA AVANZADA =====
function initAdvancedSearch() {
    const searchInput = document.querySelector('.search-input');
    const searchForm = document.querySelector('.search-form');
    let searchTimeout;

    if (searchInput) {
        // Búsqueda en tiempo real (debounced)
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const query = this.value.trim();
            
            if (query.length >= 2) {
                searchTimeout = setTimeout(() => {
                    showSearchSuggestions(query);
                }, 300);
            } else {
                hideSearchSuggestions();
            }
        });

        // Mejorar UX del formulario de búsqueda
        searchForm.addEventListener('submit', function(e) {
            const query = searchInput.value.trim();
            if (query.length < 2) {
                e.preventDefault();
                showToast('Por favor ingresa al menos 2 caracteres', 'warning');
                searchInput.focus();
            } else {
                showLoadingState();
            }
        });
    }
}

// ===== NAVEGACIÓN POR TECLADO =====
function initKeyboardNavigation() {
    document.addEventListener('keydown', function(e) {
        // Esc para cerrar modales
        if (e.key === 'Escape') {
            closeAllModals();
        }
        
        // Ctrl/Cmd + K para enfocar búsqueda
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            const searchInput = document.querySelector('.search-input');
            if (searchInput) {
                searchInput.focus();
                searchInput.select();
            }
        }
        
        // Navegación con flechas en grids
        if (e.target.classList.contains('movie-card')) {
            handleGridNavigation(e);
        }
    });
}

// ===== TOOLTIPS MODERNOS =====
function initTooltips() {
    const tooltipElements = document.querySelectorAll('[data-tooltip]');
    
    tooltipElements.forEach(element => {
        let tooltip;
        
        element.addEventListener('mouseenter', function() {
            const text = this.dataset.tooltip;
            tooltip = createTooltip(text);
            document.body.appendChild(tooltip);
            positionTooltip(tooltip, this);
        });
        
        element.addEventListener('mouseleave', function() {
            if (tooltip) {
                tooltip.remove();
                tooltip = null;
            }
        });
    });
}

// ===== INDICADORES DE PROGRESO =====
function initProgressIndicators() {
    // Indicador de scroll
    const scrollIndicator = document.createElement('div');
    scrollIndicator.className = 'scroll-indicator';
    scrollIndicator.innerHTML = '<div class="scroll-progress"></div>';
    document.body.appendChild(scrollIndicator);
    
    window.addEventListener('scroll', updateScrollProgress);
    
    // Indicadores de carga para formularios
    document.querySelectorAll('form').forEach(form => {
        form.addEventListener('submit', function() {
            showFormLoading(this);
        });
    });
}

// ===== TOGGLE DE TEMA (OPCIONAL) =====
function initThemeToggle() {
    const themeToggle = document.createElement('button');
    themeToggle.className = 'theme-toggle';
    themeToggle.innerHTML = '<i class="fas fa-palette"></i>';
    themeToggle.setAttribute('data-tooltip', 'Cambiar tema');
    
    // Agregar al header
    const userMenu = document.querySelector('.user-menu');
    if (userMenu) {
        userMenu.appendChild(themeToggle);
    }
    
    themeToggle.addEventListener('click', toggleTheme);
}

// ===== NOTIFICACIONES AVANZADAS =====
function initAdvancedNotifications() {
    // Mejorar el sistema de notificaciones existente
    const notificationBtn = document.querySelector('.notification-btn');
    
    if (notificationBtn) {
        // Agregar sonido de notificación (opcional)
        notificationBtn.addEventListener('click', function() {
            playNotificationSound();
        });
        
        // Actualización automática más suave
        setInterval(checkNotificationsSmooth, 30000); // Cada 30 segundos
    }
}

// ===== FUNCIONES AUXILIARES =====

function showSearchSuggestions(query) {
    // Implementar sugerencias de búsqueda
    console.log('Mostrando sugerencias para:', query);
}

function hideSearchSuggestions() {
    const suggestions = document.querySelector('.search-suggestions');
    if (suggestions) {
        suggestions.remove();
    }
}

function showLoadingState() {
    const searchBtn = document.querySelector('.search-btn');
    if (searchBtn) {
        searchBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        searchBtn.disabled = true;
    }
}

function closeAllModals() {
    document.querySelectorAll('.modal, .notification-modal').forEach(modal => {
        modal.classList.remove('show');
    });
}

function handleGridNavigation(e) {
    const cards = Array.from(document.querySelectorAll('.movie-card'));
    const currentIndex = cards.indexOf(e.target);
    let newIndex;
    
    switch(e.key) {
        case 'ArrowRight':
            newIndex = Math.min(currentIndex + 1, cards.length - 1);
            break;
        case 'ArrowLeft':
            newIndex = Math.max(currentIndex - 1, 0);
            break;
        case 'ArrowDown':
            // Calcular siguiente fila
            const cardsPerRow = Math.floor(window.innerWidth / 220);
            newIndex = Math.min(currentIndex + cardsPerRow, cards.length - 1);
            break;
        case 'ArrowUp':
            // Calcular fila anterior
            const cardsPerRowUp = Math.floor(window.innerWidth / 220);
            newIndex = Math.max(currentIndex - cardsPerRowUp, 0);
            break;
        default:
            return;
    }
    
    if (newIndex !== undefined && cards[newIndex]) {
        e.preventDefault();
        cards[newIndex].focus();
    }
}

function createTooltip(text) {
    const tooltip = document.createElement('div');
    tooltip.className = 'modern-tooltip';
    tooltip.textContent = text;
    return tooltip;
}

function positionTooltip(tooltip, element) {
    const rect = element.getBoundingClientRect();
    const tooltipRect = tooltip.getBoundingClientRect();
    
    tooltip.style.left = rect.left + (rect.width / 2) - (tooltipRect.width / 2) + 'px';
    tooltip.style.top = rect.top - tooltipRect.height - 10 + 'px';
}

function updateScrollProgress() {
    const scrolled = window.pageYOffset;
    const maxHeight = document.documentElement.scrollHeight - window.innerHeight;
    const progress = (scrolled / maxHeight) * 100;
    
    const progressBar = document.querySelector('.scroll-progress');
    if (progressBar) {
        progressBar.style.width = progress + '%';
    }
}

function showFormLoading(form) {
    const submitBtn = form.querySelector('button[type="submit"]');
    if (submitBtn) {
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Procesando...';
        submitBtn.disabled = true;
        
        // Restaurar después de 5 segundos como fallback
        setTimeout(() => {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }, 5000);
    }
}

function toggleTheme() {
    document.body.classList.toggle('light-theme');
    const isLight = document.body.classList.contains('light-theme');
    localStorage.setItem('theme', isLight ? 'light' : 'dark');
    showToast(isLight ? 'Tema claro activado' : 'Tema oscuro activado', 'info');
}

function playNotificationSound() {
    // Sonido sutil de notificación
    const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
    audio.volume = 0.1;
    audio.play().catch(() => {}); // Ignorar errores de autoplay
}

function checkNotificationsSmooth() {
    // Versión más suave de la verificación de notificaciones
    fetch('api_notifications.php?action=check_user_notifications')
        .then(response => response.json())
        .then(data => {
            updateNotificationBadge(data);
        })
        .catch(error => {
            console.log('Error verificando notificaciones:', error);
        });
}

function updateNotificationBadge(data) {
    const badge = document.querySelector('.notification-badge');
    if (badge) {
        if (data.has_notifications && data.count > 0) {
            badge.textContent = data.count;
            badge.style.display = 'flex';
            
            // Animación sutil si hay nuevas notificaciones
            if (data.count > parseInt(badge.dataset.lastCount || '0')) {
                badge.classList.add('new-notification');
                setTimeout(() => badge.classList.remove('new-notification'), 1000);
            }
            
            badge.dataset.lastCount = data.count;
        } else {
            badge.style.display = 'none';
        }
    }
}

function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.innerHTML = `
        <div class="toast-content">
            <i class="fas fa-${getToastIcon(type)}"></i>
            <span>${message}</span>
        </div>
    `;
    
    document.body.appendChild(toast);
    
    // Mostrar toast
    setTimeout(() => toast.classList.add('show'), 100);
    
    // Ocultar toast
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => toast.remove(), 300);
    }, 3000);
}

function getToastIcon(type) {
    const icons = {
        'success': 'check-circle',
        'error': 'exclamation-circle',
        'warning': 'exclamation-triangle',
        'info': 'info-circle'
    };
    return icons[type] || 'info-circle';
}

// ===== ESTILOS CSS ADICIONALES PARA LAS MEJORAS =====
const additionalStyles = `
<style>
.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-on-scroll.animate-in {
    opacity: 1;
    transform: translateY(0);
}

.scroll-indicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: rgba(255, 255, 255, 0.1);
    z-index: 9999;
}

.scroll-progress {
    height: 100%;
    background: var(--gradient-primary);
    width: 0%;
    transition: width 0.3s ease;
}

.modern-tooltip {
    position: absolute;
    background: var(--secondary-color);
    color: var(--text-primary);
    padding: 0.5rem 1rem;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    box-shadow: var(--shadow-lg);
    z-index: 1000;
    pointer-events: none;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.theme-toggle {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    padding: 0.5rem;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition-normal);
}

.theme-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.new-notification {
    animation: bounce 0.6s ease;
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: scale(1);
    }
    40%, 43% {
        transform: scale(1.2);
    }
    70% {
        transform: scale(1.1);
    }
    90% {
        transform: scale(1.05);
    }
}

.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--secondary-color);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    padding: 1rem;
    box-shadow: var(--shadow-xl);
    z-index: 10000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    backdrop-filter: blur(10px);
}

.toast.show {
    transform: translateX(0);
}

.toast-content {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-primary);
}

.toast-success { border-left: 3px solid var(--success-color); }
.toast-error { border-left: 3px solid var(--danger-color); }
.toast-warning { border-left: 3px solid var(--warning-color); }
.toast-info { border-left: 3px solid var(--info-color); }

img.loaded {
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}
</style>
`;

// Agregar estilos al documento
document.head.insertAdjacentHTML('beforeend', additionalStyles);
