<?php
// Script para actualizar el ENUM de service_type para soportar renovaciones

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>🔧 Actualizando ENUM de service_type</h2>";
    echo "<div style='font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px;'>";
    
    // Verificar estado actual
    echo "<h3>📋 Estado actual de la tabla:</h3>";
    $stmt = $pdo->query("DESCRIBE service_requests");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($columns as $column) {
        if ($column['Field'] === 'service_type') {
            echo "<p><strong>service_type:</strong> {$column['Type']}</p>";
            break;
        }
    }
    
    // Actualizar ENUM
    echo "<h3>🔄 Actualizando ENUM...</h3>";
    try {
        $pdo->exec("ALTER TABLE service_requests MODIFY COLUMN service_type ENUM('trial', 'direct_purchase', 'renewal', 'reseller_activation', 'reseller_renewal') NOT NULL");
        echo "<p style='color: green;'>✅ ENUM actualizado correctamente</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error actualizando ENUM: " . $e->getMessage() . "</p>";
    }
    
    // Verificar estado después de la actualización
    echo "<h3>📋 Estado después de la actualización:</h3>";
    $stmt = $pdo->query("DESCRIBE service_requests");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($columns as $column) {
        if ($column['Field'] === 'service_type') {
            echo "<p><strong>service_type:</strong> {$column['Type']}</p>";
            break;
        }
    }
    
    // Verificar registros existentes
    echo "<h3>📊 Registros existentes:</h3>";
    $stmt = $pdo->query("SELECT service_type, COUNT(*) as count FROM service_requests GROUP BY service_type");
    $stats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($stats)) {
        echo "<p>No hay registros en la tabla</p>";
    } else {
        echo "<ul>";
        foreach ($stats as $stat) {
            echo "<li><strong>{$stat['service_type']}:</strong> {$stat['count']} registros</li>";
        }
        echo "</ul>";
    }
    
    // Probar inserción de renovación
    echo "<h3>🧪 Probando inserción de renovación...</h3>";
    try {
        $test_data = [
            'first_name' => 'Test',
            'last_name' => 'Renewal',
            'email' => '<EMAIL>',
            'phone' => '+57 ************',
            'service_type' => 'renewal',
            'device_type' => 'android',
            'platform_id' => 'test_platform_123',
            'renewal_period' => '3_months',
            'wants_current_promo' => 1,
            'terms_accepted' => 1
        ];
        
        $stmt = $pdo->prepare("
            INSERT INTO service_requests (
                first_name, last_name, email, phone, service_type, device_type,
                platform_id, renewal_period, wants_current_promo, terms_accepted
            ) VALUES (
                :first_name, :last_name, :email, :phone, :service_type, :device_type,
                :platform_id, :renewal_period, :wants_current_promo, :terms_accepted
            )
        ");
        
        $stmt->execute($test_data);
        $test_id = $pdo->lastInsertId();
        
        echo "<p style='color: green;'>✅ Inserción de prueba exitosa. ID: $test_id</p>";
        
        // Eliminar registro de prueba
        $pdo->exec("DELETE FROM service_requests WHERE id = $test_id");
        echo "<p style='color: blue;'>🗑️ Registro de prueba eliminado</p>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error en inserción de prueba: " . $e->getMessage() . "</p>";
    }
    
    echo "<div style='background: #f0fdf4; padding: 1rem; border-radius: 8px; border-left: 4px solid #10b981; margin: 1rem 0;'>";
    echo "<h4>✅ Actualización Completada</h4>";
    echo "<p>La tabla service_requests ahora soporta los siguientes tipos de servicio:</p>";
    echo "<ul>";
    echo "<li><strong>trial:</strong> Prueba gratuita</li>";
    echo "<li><strong>direct_purchase:</strong> Compra directa</li>";
    echo "<li><strong>renewal:</strong> Renovación de servicio ✨ NUEVO</li>";
    echo "<li><strong>reseller_activation:</strong> Activación como revendedor ✨ NUEVO</li>";
    echo "<li><strong>reseller_renewal:</strong> Renovación de revendedor ✨ NUEVO</li>";
    echo "</ul>";
    echo "<p><a href='service_options.php' style='color: #10b981; font-weight: 600;'>🎯 Probar Renovación de Servicio</a></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ Error de conexión:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
}

echo "</div>";
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Actualizar ENUM service_type</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        h2, h3 {
            color: #333;
        }
        
        p {
            margin: 0.5rem 0;
            padding: 0.5rem;
            border-radius: 4px;
        }
        
        a {
            color: #10b981;
            text-decoration: none;
            font-weight: 600;
        }
        
        a:hover {
            text-decoration: underline;
        }
        
        ul {
            padding-left: 1.5rem;
        }
        
        li {
            margin: 0.5rem 0;
        }
    </style>
</head>
<body>
    <!-- El contenido PHP se muestra aquí -->
</body>
</html>
