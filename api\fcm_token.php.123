<?php
/**
 * Endpoint FCM independiente
 * URL: /api/fcm_token.php
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// Manejar preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Solo permitir POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'error' => 'Method not allowed',
        'message' => 'Solo se permite POST'
    ]);
    exit;
}

// Configuración de base de datos
$db_config = [
    'host' => 'localhost',
    'name' => 'u170528143_php',
    'user' => 'u170528143_php',
    'pass' => '&T4v!$=i'
];

try {
    $pdo = new PDO(
        "mysql:host={$db_config['host']};dbname={$db_config['name']};charset=utf8mb4",
        $db_config['user'],
        $db_config['pass'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
} catch(PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Database connection failed',
        'message' => 'No se pudo conectar a la base de datos'
    ]);
    exit;
}

// Leer datos JSON
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['user_id']) || !isset($input['fcm_token'])) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => 'Missing data',
        'message' => 'user_id y fcm_token requeridos'
    ]);
    exit;
}

// Convertir username a user_id si es necesario
$user_id = $input['user_id'];
if (!is_numeric($user_id)) {
    $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ?");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch();
    
    if (!$user) {
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'error' => 'User not found',
            'message' => "Usuario no encontrado: $user_id"
        ]);
        exit;
    }
    
    $user_id = $user['id'];
}

try {
    // Crear tabla FCM tokens si no existe
    $pdo->exec("CREATE TABLE IF NOT EXISTS fcm_tokens (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        fcm_token TEXT NOT NULL,
        device_type VARCHAR(32) DEFAULT 'android',
        app_version VARCHAR(16),
        is_active TINYINT(1) DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_user_token (user_id, fcm_token(255))
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");
    
    // Insertar o actualizar token
    $stmt = $pdo->prepare("
        INSERT INTO fcm_tokens (user_id, fcm_token, device_type, app_version) 
        VALUES (?, ?, ?, ?) 
        ON DUPLICATE KEY UPDATE 
            device_type = VALUES(device_type),
            app_version = VALUES(app_version),
            is_active = 1,
            updated_at = CURRENT_TIMESTAMP
    ");
    
    $result = $stmt->execute([
        $user_id,
        $input['fcm_token'],
        $input['device_type'] ?? 'android',
        $input['app_version'] ?? '1.0.0'
    ]);
    
    if ($result) {
        echo json_encode([
            'success' => true,
            'message' => 'Token FCM registrado exitosamente',
            'data' => [
                'user_id' => $user_id,
                'username' => $input['user_id'],
                'token_registered' => true,
                'endpoint' => 'fcm_token.php',
                'timestamp' => date('Y-m-d H:i:s')
            ]
        ]);
    } else {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'Insert failed',
            'message' => 'No se pudo registrar el token FCM'
        ]);
    }
    
} catch(PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Database error',
        'message' => 'Error en la base de datos: ' . $e->getMessage()
    ]);
}
?>
