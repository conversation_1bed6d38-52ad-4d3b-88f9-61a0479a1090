<?php
// Script para verificar el estado del sistema de solicitudes de servicio
// Muestra todos los registros y qué falta por implementar

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Error de conexión: " . $e->getMessage());
}

// Función para verificar si una tabla existe
function tableExists($pdo, $tableName) {
    try {
        $stmt = $pdo->query("SELECT 1 FROM $tableName LIMIT 1");
        return true;
    } catch (Exception $e) {
        return false;
    }
}

// Función para verificar si una columna existe
function columnExists($pdo, $tableName, $columnName) {
    try {
        $stmt = $pdo->query("SHOW COLUMNS FROM $tableName LIKE '$columnName'");
        return $stmt->rowCount() > 0;
    } catch (Exception $e) {
        return false;
    }
}

// Lista de tablas que deberían existir
$requiredTables = [
    'service_requests' => 'Tabla principal de solicitudes de servicio',
    'service_communications' => 'Comunicaciones bidireccionales',
    'available_apps' => 'Aplicaciones disponibles por dispositivo',
    'client_notifications' => 'Notificaciones para clientes',
    'app_management' => 'Gestión de aplicaciones por admin',
    'current_promotions' => 'Promociones activas',
    'uploaded_files' => 'Archivos subidos (comprobantes)',
    'users' => 'Usuarios del sistema (debería existir)',
    'tickets' => 'Sistema de tickets (debería existir)'
];

// Columnas nuevas que deberían existir en service_requests
$requiredColumns = [
    'platform_id' => 'ID de la plataforma del cliente',
    'renewal_period' => 'Período de renovación',
    'wants_current_promo' => 'Si quiere promoción actual',
    'payment_proof_url' => 'URL del comprobante de pago',
    'is_reseller' => 'Si es revendedor',
    'reseller_type' => 'Tipo de revendedor',
    'reseller_info' => 'Información adicional del revendedor'
];

// Archivos que deberían existir
$requiredFiles = [
    'service_options.php' => 'Página principal de opciones de servicio',
    'renewal_modal.php' => 'Modal de renovación de servicio',
    'reseller_modal.php' => 'Modal de revendedor',
    'renewal_script.js' => 'JavaScript para renovaciones',
    'reseller_script.js' => 'JavaScript para revendedores',
    'get_client_notifications.php' => 'API para obtener notificaciones',
    'mark_notification_read.php' => 'API para marcar notificaciones como leídas',
    'apps_management_admin.php' => 'Panel de gestión de aplicaciones',
    'service_requests_admin.php' => 'Panel de administración de solicitudes',
    'submit_service_request.php' => 'Procesamiento de solicitudes',
    'get_available_apps.php' => 'API para obtener aplicaciones',
    'service_request_confirmation.php' => 'Página de confirmación'
];
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Estado del Sistema de Solicitudes</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
            background: #f8fafc;
            color: #334155;
            line-height: 1.6;
        }
        
        .header {
            text-align: center;
            margin-bottom: 3rem;
            padding: 2rem;
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: 800;
        }
        
        .header p {
            margin: 0.5rem 0 0 0;
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .section {
            background: white;
            margin-bottom: 2rem;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .section-header {
            background: #1f2937;
            color: white;
            padding: 1rem 1.5rem;
            font-weight: 600;
            font-size: 1.1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .section-content {
            padding: 1.5rem;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .status-card {
            padding: 1rem;
            border-radius: 8px;
            border-left: 4px solid;
        }
        
        .status-card.success {
            background: #f0fdf4;
            border-color: #10b981;
            color: #065f46;
        }
        
        .status-card.warning {
            background: #fffbeb;
            border-color: #f59e0b;
            color: #92400e;
        }
        
        .status-card.error {
            background: #fef2f2;
            border-color: #ef4444;
            color: #991b1b;
        }
        
        .status-icon {
            font-size: 1.2rem;
            margin-right: 0.5rem;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        
        th, td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }
        
        th {
            background: #f8fafc;
            font-weight: 600;
            color: #475569;
        }
        
        .badge {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .badge.success {
            background: #dcfce7;
            color: #166534;
        }
        
        .badge.error {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .badge.warning {
            background: #fef3c7;
            color: #92400e;
        }
        
        .todo-list {
            list-style: none;
            padding: 0;
        }
        
        .todo-item {
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            background: #f8fafc;
            border-radius: 6px;
            border-left: 3px solid #f59e0b;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .priority-high {
            border-left-color: #ef4444;
        }
        
        .priority-medium {
            border-left-color: #f59e0b;
        }
        
        .priority-low {
            border-left-color: #10b981;
        }
        
        .code-block {
            background: #1f2937;
            color: #f8fafc;
            padding: 1rem;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            overflow-x: auto;
            margin: 1rem 0;
        }
        
        .stats-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-item {
            text-align: center;
            padding: 1rem;
            background: #f8fafc;
            border-radius: 8px;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 800;
            color: #10b981;
        }
        
        .stat-label {
            font-size: 0.9rem;
            color: #64748b;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 Estado del Sistema de Solicitudes</h1>
        <p>Verificación completa del sistema expandido de solicitudes de servicio IPTV</p>
    </div>

    <!-- Resumen de Estado -->
    <div class="section">
        <div class="section-header">
            <i class="fas fa-chart-bar"></i>
            Resumen General del Sistema
        </div>
        <div class="section-content">
            <div class="stats-summary">
                <?php
                $tablesExist = 0;
                $totalTables = count($requiredTables);
                foreach ($requiredTables as $table => $description) {
                    if (tableExists($pdo, $table)) {
                        $tablesExist++;
                    }
                }
                
                $filesExist = 0;
                $totalFiles = count($requiredFiles);
                foreach ($requiredFiles as $file => $description) {
                    if (file_exists($file)) {
                        $filesExist++;
                    }
                }
                
                $columnsExist = 0;
                $totalColumns = count($requiredColumns);
                if (tableExists($pdo, 'service_requests')) {
                    foreach ($requiredColumns as $column => $description) {
                        if (columnExists($pdo, 'service_requests', $column)) {
                            $columnsExist++;
                        }
                    }
                }
                ?>
                
                <div class="stat-item">
                    <div class="stat-number"><?php echo $tablesExist; ?>/<?php echo $totalTables; ?></div>
                    <div class="stat-label">Tablas</div>
                </div>
                
                <div class="stat-item">
                    <div class="stat-number"><?php echo $filesExist; ?>/<?php echo $totalFiles; ?></div>
                    <div class="stat-label">Archivos</div>
                </div>
                
                <div class="stat-item">
                    <div class="stat-number"><?php echo $columnsExist; ?>/<?php echo $totalColumns; ?></div>
                    <div class="stat-label">Columnas</div>
                </div>
                
                <div class="stat-item">
                    <div class="stat-number"><?php echo round((($tablesExist + $filesExist + $columnsExist) / ($totalTables + $totalFiles + $totalColumns)) * 100); ?>%</div>
                    <div class="stat-label">Completado</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Estado de Tablas -->
    <div class="section">
        <div class="section-header">
            <i class="fas fa-database"></i>
            Estado de Tablas de Base de Datos
        </div>
        <div class="section-content">
            <table>
                <thead>
                    <tr>
                        <th>Tabla</th>
                        <th>Estado</th>
                        <th>Registros</th>
                        <th>Descripción</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($requiredTables as $table => $description): ?>
                    <tr>
                        <td><strong><?php echo $table; ?></strong></td>
                        <td>
                            <?php if (tableExists($pdo, $table)): ?>
                                <span class="badge success">✓ Existe</span>
                            <?php else: ?>
                                <span class="badge error">✗ Falta</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php 
                            if (tableExists($pdo, $table)) {
                                try {
                                    $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
                                    echo number_format($stmt->fetchColumn());
                                } catch (Exception $e) {
                                    echo "Error";
                                }
                            } else {
                                echo "-";
                            }
                            ?>
                        </td>
                        <td><?php echo $description; ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Estado de Columnas -->
    <div class="section">
        <div class="section-header">
            <i class="fas fa-columns"></i>
            Nuevas Columnas en service_requests
        </div>
        <div class="section-content">
            <?php if (tableExists($pdo, 'service_requests')): ?>
            <table>
                <thead>
                    <tr>
                        <th>Columna</th>
                        <th>Estado</th>
                        <th>Descripción</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($requiredColumns as $column => $description): ?>
                    <tr>
                        <td><strong><?php echo $column; ?></strong></td>
                        <td>
                            <?php if (columnExists($pdo, 'service_requests', $column)): ?>
                                <span class="badge success">✓ Existe</span>
                            <?php else: ?>
                                <span class="badge error">✗ Falta</span>
                            <?php endif; ?>
                        </td>
                        <td><?php echo $description; ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            <?php else: ?>
            <div class="status-card error">
                <span class="status-icon">⚠️</span>
                La tabla service_requests no existe. Ejecuta el script de configuración primero.
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Estado de Archivos -->
    <div class="section">
        <div class="section-header">
            <i class="fas fa-file-code"></i>
            Estado de Archivos del Sistema
        </div>
        <div class="section-content">
            <table>
                <thead>
                    <tr>
                        <th>Archivo</th>
                        <th>Estado</th>
                        <th>Tamaño</th>
                        <th>Descripción</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($requiredFiles as $file => $description): ?>
                    <tr>
                        <td><strong><?php echo $file; ?></strong></td>
                        <td>
                            <?php if (file_exists($file)): ?>
                                <span class="badge success">✓ Existe</span>
                            <?php else: ?>
                                <span class="badge error">✗ Falta</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php 
                            if (file_exists($file)) {
                                echo number_format(filesize($file) / 1024, 1) . " KB";
                            } else {
                                echo "-";
                            }
                            ?>
                        </td>
                        <td><?php echo $description; ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Tareas Pendientes -->
    <div class="section">
        <div class="section-header">
            <i class="fas fa-tasks"></i>
            Tareas Pendientes para Completar el Sistema
        </div>
        <div class="section-content">
            <ul class="todo-list">
                <?php if (!tableExists($pdo, 'service_requests')): ?>
                <li class="todo-item priority-high">
                    <span class="status-icon">🔴</span>
                    <strong>CRÍTICO:</strong> Ejecutar script de configuración de base de datos
                    <div class="code-block">Visita: http://<?php echo $_SERVER['HTTP_HOST']; ?>/setup_service_database.php</div>
                </li>
                <?php endif; ?>

                <?php if (!tableExists($pdo, 'client_notifications')): ?>
                <li class="todo-item priority-high">
                    <span class="status-icon">🔴</span>
                    <strong>CRÍTICO:</strong> Crear tablas extendidas del sistema
                    <div class="code-block">Ejecutar: create_extended_service_tables.sql en phpMyAdmin</div>
                </li>
                <?php endif; ?>

                <?php if (!file_exists('service_options.php')): ?>
                <li class="todo-item priority-high">
                    <span class="status-icon">🔴</span>
                    <strong>CRÍTICO:</strong> Subir archivo service_options.php al servidor
                </li>
                <?php endif; ?>

                <?php if (!file_exists('renewal_modal.php')): ?>
                <li class="todo-item priority-medium">
                    <span class="status-icon">🟡</span>
                    <strong>IMPORTANTE:</strong> Subir modal de renovación (renewal_modal.php)
                </li>
                <?php endif; ?>

                <?php if (!file_exists('reseller_modal.php')): ?>
                <li class="todo-item priority-medium">
                    <span class="status-icon">🟡</span>
                    <strong>IMPORTANTE:</strong> Subir modal de revendedor (reseller_modal.php)
                </li>
                <?php endif; ?>

                <?php if (!file_exists('apps_management_admin.php')): ?>
                <li class="todo-item priority-medium">
                    <span class="status-icon">🟡</span>
                    <strong>IMPORTANTE:</strong> Subir panel de gestión de aplicaciones
                </li>
                <?php endif; ?>

                <?php
                // Verificar si las notificaciones están integradas
                $index_content = file_exists('index.php') ? file_get_contents('index.php') : '';
                $notifications_integrated = strpos($index_content, 'get_client_notifications.php') !== false;

                // Verificar si submit_service_request.php está actualizado
                $submit_content = file_exists('submit_service_request.php') ? file_get_contents('submit_service_request.php') : '';
                $submit_updated = strpos($submit_content, 'platform_id') !== false && strpos($submit_content, 'renewal_period') !== false;
                ?>

                <?php if (!$notifications_integrated): ?>
                <li class="todo-item priority-medium">
                    <span class="status-icon">🟡</span>
                    <strong>PENDIENTE:</strong> Integrar notificaciones en index.php
                    <div class="code-block">
// Agregar en index.php:
// 1. Llamada AJAX a get_client_notifications.php
// 2. Mostrar badge con número de notificaciones no leídas
// 3. Modal o dropdown para mostrar notificaciones
                    </div>
                </li>
                <?php else: ?>
                <li class="todo-item priority-low">
                    <span class="status-icon">✅</span>
                    <strong>COMPLETADO:</strong> Notificaciones integradas en index.php
                    <div class="code-block">✅ Sistema de notificaciones funcionando correctamente</div>
                </li>
                <?php endif; ?>

                <?php if (!$submit_updated): ?>
                <li class="todo-item priority-medium">
                    <span class="status-icon">🟡</span>
                    <strong>PENDIENTE:</strong> Actualizar submit_service_request.php para nuevos tipos
                    <div class="code-block">
// Agregar soporte para:
// - renewal (renovaciones)
// - reseller_activation (nuevos revendedores)
// - reseller_renewal (renovación revendedores)
// - Subida de archivos de comprobantes
                    </div>
                </li>
                <?php else: ?>
                <li class="todo-item priority-low">
                    <span class="status-icon">✅</span>
                    <strong>COMPLETADO:</strong> submit_service_request.php actualizado
                    <div class="code-block">✅ Soporte completo para renovaciones, revendedores y subida de archivos</div>
                </li>
                <?php endif; ?>

                <li class="todo-item priority-low">
                    <span class="status-icon">🟢</span>
                    <strong>OPCIONAL:</strong> Crear sistema de promociones dinámicas
                </li>

                <li class="todo-item priority-low">
                    <span class="status-icon">🟢</span>
                    <strong>OPCIONAL:</strong> Implementar notificaciones push en tiempo real
                </li>
            </ul>
        </div>
    </div>

    <!-- Scripts de Configuración -->
    <div class="section">
        <div class="section-header">
            <i class="fas fa-terminal"></i>
            Scripts de Configuración Rápida
        </div>
        <div class="section-content">
            <h4>1. Configuración Automática de Base de Datos:</h4>
            <div class="code-block">
# Visita en tu navegador:
http://<?php echo $_SERVER['HTTP_HOST']; ?>/setup_service_database.php

# Esto creará automáticamente:
# - Tabla service_requests
# - Tabla service_communications
# - Tabla available_apps (con datos de ejemplo)
# - Tabla tv_channels (si no existe)
            </div>

            <h4>2. Configuración de Tablas Extendidas:</h4>
            <div class="code-block">
# Ejecutar en phpMyAdmin o MySQL:
# - Abrir archivo: create_extended_service_tables.sql
# - Ejecutar todo el contenido
# - Verificar que se crearon las nuevas tablas y columnas
            </div>

            <h4>3. Probar el Sistema:</h4>
            <div class="code-block">
# 1. Ve a: http://<?php echo $_SERVER['HTTP_HOST']; ?>/service_options.php
# 2. Prueba cada tipo de solicitud
# 3. Verifica en admin: http://<?php echo $_SERVER['HTTP_HOST']; ?>/service_requests_admin.php
# 4. Comprueba notificaciones en index.php
            </div>
        </div>
    </div>

    <div style="text-align: center; margin-top: 3rem; padding: 2rem; background: #f8fafc; border-radius: 12px;">
        <h3 style="color: #10b981; margin-bottom: 1rem;">🎉 ¡Sistema de Solicitudes de Servicio!</h3>
        <p style="color: #64748b; margin-bottom: 1rem;">
            Una vez completadas las tareas pendientes, tendrás un sistema completo de solicitudes con:
        </p>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-top: 1rem;">
            <div style="padding: 1rem; background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <strong>✅ Nuevos Servicios</strong><br>
                <small>Pruebas y compras directas</small>
            </div>
            <div style="padding: 1rem; background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <strong>🔄 Renovaciones</strong><br>
                <small>Con promociones y descuentos</small>
            </div>
            <div style="padding: 1rem; background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <strong>👥 Revendedores</strong><br>
                <small>Programa completo de partners</small>
            </div>
            <div style="padding: 1rem; background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <strong>🔔 Notificaciones</strong><br>
                <small>Sistema bidireccional</small>
            </div>
        </div>
    </div>

</body>
</html>
