<?php
session_start();
header('Content-Type: application/json');

// Configuración MySQL
$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    echo json_encode(['success' => false, 'error' => 'Error de base de datos']);
    exit;
}

// Verificar si el usuario está logueado
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'error' => 'Usuario no autenticado']);
    exit;
}

$user_id = $_SESSION['user_id'];

try {
    // Obtener email del usuario
    $stmt = $pdo->prepare("SELECT email FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        echo json_encode(['success' => false, 'error' => 'Usuario no encontrado']);
        exit;
    }
    
    $user_email = $user['email'];
    
    // Obtener notificaciones del cliente
    $stmt = $pdo->prepare("
        SELECT cn.*, sr.id as request_id, sr.service_type, sr.status as request_status
        FROM client_notifications cn
        LEFT JOIN service_requests sr ON cn.request_id = sr.id
        WHERE cn.client_email = ?
        ORDER BY cn.created_at DESC
        LIMIT 10
    ");
    $stmt->execute([$user_email]);
    $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Contar notificaciones no leídas
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM client_notifications WHERE client_email = ? AND is_read = FALSE");
    $stmt->execute([$user_email]);
    $unread_count = $stmt->fetchColumn();
    
    // Formatear notificaciones para el frontend
    $formatted_notifications = [];
    foreach ($notifications as $notification) {
        $formatted_notification = [
            'id' => $notification['id'],
            'type' => $notification['notification_type'],
            'title' => $notification['title'],
            'message' => $notification['message'],
            'is_read' => (bool)$notification['is_read'],
            'created_at' => $notification['created_at'],
            'request_id' => $notification['request_id'],
            'service_type' => $notification['service_type'],
            'request_status' => $notification['request_status']
        ];
        
        // Decodificar información adicional si existe
        if ($notification['credentials_info']) {
            $formatted_notification['credentials'] = json_decode($notification['credentials_info'], true);
        }
        
        if ($notification['service_info']) {
            $formatted_notification['service_info'] = json_decode($notification['service_info'], true);
        }
        
        $formatted_notifications[] = $formatted_notification;
    }
    
    echo json_encode([
        'success' => true,
        'notifications' => $formatted_notifications,
        'unread_count' => $unread_count
    ]);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'error' => 'Error al obtener notificaciones']);
}
?>
