/**
 * Ejemplo de integración Android con RGS TOOL API
 * Código <PERSON>tlin para consumir la API REST
 */

// build.gradle (Module: app)
/*
dependencies {
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.9.3'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4'
}
*/

// 1. Modelos de datos
data class ApiResponse<T>(
    val success: Boolean,
    val data: T? = null,
    val message: String? = null,
    val error: ErrorInfo? = null,
    val timestamp: String
)

data class ErrorInfo(
    val code: Int,
    val type: String,
    val message: String
)

data class LoginRequest(
    val username: String,
    val password: String
)

data class LoginResponse(
    val user_id: Int,
    val username: String,
    val token: String,
    val services: UserServices
)

data class UserServices(
    val is_cliente_actual: Boolean,
    val is_mavistv: <PERSON><PERSON>an,
    val is_tvdigital: <PERSON><PERSON>an,
    val is_limites507: <PERSON>olean,
    val is_worldtv: Boolean,
    val is_infest84: Boolean,
    val is_rogsmediatv: Boolean,
    val is_saul: Boolean
)

data class TrendingContent(
    val movies: List<Movie>,
    val tv_shows: List<TvShow>
)

data class Movie(
    val id: Int,
    val title: String,
    val poster_path: String?,
    val release_date: String,
    val vote_average: Double,
    val overview: String
)

data class TvShow(
    val id: Int,
    val name: String,
    val poster_path: String?,
    val first_air_date: String,
    val vote_average: Double,
    val overview: String
)

data class SearchResponse(
    val query: String,
    val results: List<SearchResult>,
    val total_results: Int
)

data class SearchResult(
    val id: Int,
    val title: String? = null,
    val name: String? = null,
    val media_type: String,
    val poster_path: String?,
    val release_date: String? = null,
    val first_air_date: String? = null,
    val vote_average: Double,
    val overview: String
)

data class CreateOrderRequest(
    val user_id: Int,
    val tmdb_id: Int,
    val title: String,
    val media_type: String,
    val year: String? = null
)

data class CreateOrderResponse(
    val order_id: Int,
    val status: String
)

data class Order(
    val id: Int,
    val tmdb_id: Int,
    val title: String,
    val media_type: String,
    val year: String?,
    val status: String,
    val country: String,
    val city: String,
    val created_at: String,
    val updated_at: String
)

data class UserOrdersResponse(
    val user_id: String,
    val orders: List<Order>,
    val total_orders: Int
)

// 2. Interface de la API
interface RgsToolApiService {
    
    @POST("auth/login")
    suspend fun login(@Body request: LoginRequest): ApiResponse<LoginResponse>
    
    @GET("content/trending")
    suspend fun getTrendingContent(): ApiResponse<TrendingContent>
    
    @GET("content/search")
    suspend fun searchContent(@Query("q") query: String): ApiResponse<SearchResponse>
    
    @POST("orders/create")
    suspend fun createOrder(@Body request: CreateOrderRequest): ApiResponse<CreateOrderResponse>
    
    @GET("orders/user/{userId}")
    suspend fun getUserOrders(@Path("userId") userId: Int): ApiResponse<UserOrdersResponse>
    
    @GET("stats")
    suspend fun getStats(): ApiResponse<Any>
    
    @GET("user/profile/{userId}")
    suspend fun getUserProfile(@Path("userId") userId: Int): ApiResponse<Any>
}

// 3. Cliente API
class RgsToolApiClient {
    companion object {
        private const val BASE_URL = "http://tu-dominio.com/api/"
        
        private val loggingInterceptor = HttpLoggingInterceptor().apply {
            level = HttpLoggingInterceptor.Level.BODY
        }
        
        private val okHttpClient = OkHttpClient.Builder()
            .addInterceptor(loggingInterceptor)
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .build()
        
        private val retrofit = Retrofit.Builder()
            .baseUrl(BASE_URL)
            .client(okHttpClient)
            .addConverterFactory(GsonConverterFactory.create())
            .build()
        
        val apiService: RgsToolApiService = retrofit.create(RgsToolApiService::class.java)
    }
}

// 4. Repository
class RgsToolRepository {
    private val apiService = RgsToolApiClient.apiService
    
    suspend fun login(username: String, password: String): Result<LoginResponse> {
        return try {
            val response = apiService.login(LoginRequest(username, password))
            if (response.success && response.data != null) {
                Result.success(response.data)
            } else {
                Result.failure(Exception(response.error?.message ?: "Login failed"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun getTrendingContent(): Result<TrendingContent> {
        return try {
            val response = apiService.getTrendingContent()
            if (response.success && response.data != null) {
                Result.success(response.data)
            } else {
                Result.failure(Exception(response.error?.message ?: "Failed to get trending content"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun searchContent(query: String): Result<SearchResponse> {
        return try {
            val response = apiService.searchContent(query)
            if (response.success && response.data != null) {
                Result.success(response.data)
            } else {
                Result.failure(Exception(response.error?.message ?: "Search failed"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun createOrder(
        userId: Int,
        tmdbId: Int,
        title: String,
        mediaType: String,
        year: String? = null
    ): Result<CreateOrderResponse> {
        return try {
            val request = CreateOrderRequest(userId, tmdbId, title, mediaType, year)
            val response = apiService.createOrder(request)
            if (response.success && response.data != null) {
                Result.success(response.data)
            } else {
                Result.failure(Exception(response.error?.message ?: "Failed to create order"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun getUserOrders(userId: Int): Result<UserOrdersResponse> {
        return try {
            val response = apiService.getUserOrders(userId)
            if (response.success && response.data != null) {
                Result.success(response.data)
            } else {
                Result.failure(Exception(response.error?.message ?: "Failed to get user orders"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}

// 5. ViewModel (ejemplo)
class MainViewModel : ViewModel() {
    private val repository = RgsToolRepository()
    
    private val _loginState = MutableLiveData<UiState<LoginResponse>>()
    val loginState: LiveData<UiState<LoginResponse>> = _loginState
    
    private val _trendingContent = MutableLiveData<UiState<TrendingContent>>()
    val trendingContent: LiveData<UiState<TrendingContent>> = _trendingContent
    
    private val _searchResults = MutableLiveData<UiState<SearchResponse>>()
    val searchResults: LiveData<UiState<SearchResponse>> = _searchResults
    
    fun login(username: String, password: String) {
        viewModelScope.launch {
            _loginState.value = UiState.Loading
            repository.login(username, password)
                .onSuccess { _loginState.value = UiState.Success(it) }
                .onFailure { _loginState.value = UiState.Error(it.message ?: "Unknown error") }
        }
    }
    
    fun loadTrendingContent() {
        viewModelScope.launch {
            _trendingContent.value = UiState.Loading
            repository.getTrendingContent()
                .onSuccess { _trendingContent.value = UiState.Success(it) }
                .onFailure { _trendingContent.value = UiState.Error(it.message ?: "Unknown error") }
        }
    }
    
    fun searchContent(query: String) {
        viewModelScope.launch {
            _searchResults.value = UiState.Loading
            repository.searchContent(query)
                .onSuccess { _searchResults.value = UiState.Success(it) }
                .onFailure { _searchResults.value = UiState.Error(it.message ?: "Unknown error") }
        }
    }
    
    fun createOrder(userId: Int, tmdbId: Int, title: String, mediaType: String, year: String? = null) {
        viewModelScope.launch {
            repository.createOrder(userId, tmdbId, title, mediaType, year)
                .onSuccess { 
                    // Manejar éxito
                    Log.d("MainViewModel", "Order created: ${it.order_id}")
                }
                .onFailure { 
                    // Manejar error
                    Log.e("MainViewModel", "Failed to create order: ${it.message}")
                }
        }
    }
}

// 6. Estados UI
sealed class UiState<out T> {
    object Loading : UiState<Nothing>()
    data class Success<T>(val data: T) : UiState<T>()
    data class Error(val message: String) : UiState<Nothing>()
}

// 7. Ejemplo de uso en Activity/Fragment
class MainActivity : AppCompatActivity() {
    private lateinit var viewModel: MainViewModel
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)
        
        viewModel = ViewModelProvider(this)[MainViewModel::class.java]
        
        // Observar estados
        viewModel.loginState.observe(this) { state ->
            when (state) {
                is UiState.Loading -> {
                    // Mostrar loading
                }
                is UiState.Success -> {
                    // Login exitoso
                    val loginData = state.data
                    // Guardar token, navegar, etc.
                }
                is UiState.Error -> {
                    // Mostrar error
                    Toast.makeText(this, state.message, Toast.LENGTH_LONG).show()
                }
            }
        }
        
        viewModel.trendingContent.observe(this) { state ->
            when (state) {
                is UiState.Loading -> {
                    // Mostrar loading
                }
                is UiState.Success -> {
                    // Mostrar contenido trending
                    val content = state.data
                    // Actualizar UI con movies y tv_shows
                }
                is UiState.Error -> {
                    // Mostrar error
                }
            }
        }
        
        // Cargar contenido inicial
        viewModel.loadTrendingContent()
    }
    
    private fun performLogin() {
        viewModel.login("username", "password")
    }
    
    private fun searchContent(query: String) {
        viewModel.searchContent(query)
    }
    
    private fun createOrder(tmdbId: Int, title: String, mediaType: String) {
        val userId = 123 // Obtener del usuario logueado
        viewModel.createOrder(userId, tmdbId, title, mediaType)
    }
}
