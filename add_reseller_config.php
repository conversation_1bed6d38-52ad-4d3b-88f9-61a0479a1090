<?php
// Script para agregar configuración de revendedores a la tabla service_config

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

echo "<!DOCTYPE html>
<html lang='es'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Agregar Configuración de Revendedores</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; background: #f5f5f5; }
        .container { background: white; padding: 2rem; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #10b981; background: #d1fae5; padding: 1rem; border-radius: 6px; margin: 1rem 0; }
        .error { color: #ef4444; background: #fee2e2; padding: 1rem; border-radius: 6px; margin: 1rem 0; }
        .info { color: #3b82f6; background: #dbeafe; padding: 1rem; border-radius: 6px; margin: 1rem 0; }
        .btn { display: inline-block; padding: 0.75rem 1.5rem; background: #10b981; color: white; text-decoration: none; border-radius: 6px; margin: 0.5rem; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>🔧 Agregar Configuración de Revendedores</h1>";

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div class='success'>✅ Conexión a base de datos exitosa</div>";
    
    // Verificar si ya existe configuración de revendedores
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM service_config WHERE service_type = 'reseller'");
    $stmt->execute();
    $exists = $stmt->fetchColumn() > 0;
    
    if ($exists) {
        echo "<div class='info'>ℹ️ La configuración de revendedores ya existe. Verificando datos...</div>";
        
        // Mostrar configuración actual
        $stmt = $pdo->prepare("SELECT * FROM service_config WHERE service_type = 'reseller'");
        $stmt->execute();
        $config = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "<h3>Configuración actual de revendedores:</h3>";
        echo "<table style='width: 100%; border-collapse: collapse; margin: 1rem 0;'>";
        echo "<tr style='background: #f8fafc;'>";
        echo "<th style='border: 1px solid #e2e8f0; padding: 0.5rem; text-align: left;'>Campo</th>";
        echo "<th style='border: 1px solid #e2e8f0; padding: 0.5rem; text-align: left;'>Valor</th>";
        echo "</tr>";
        
        foreach ($config as $key => $value) {
            if ($key === 'features') {
                $features = json_decode($value, true);
                $value = is_array($features) ? implode(', ', $features) : $value;
            }
            echo "<tr>";
            echo "<td style='border: 1px solid #e2e8f0; padding: 0.5rem; font-weight: 600;'>" . ucfirst(str_replace('_', ' ', $key)) . "</td>";
            echo "<td style='border: 1px solid #e2e8f0; padding: 0.5rem;'>" . htmlspecialchars($value) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
    } else {
        echo "<div class='info'>📝 Insertando configuración de revendedores...</div>";
        
        $resellerConfig = [
            'service_type' => 'reseller',
            'title' => 'Ser Revendedor',
            'subtitle' => 'Únete a nuestro programa de revendedores',
            'description' => 'Obtén beneficios exclusivos y comisiones atractivas como revendedor oficial de nuestros servicios IPTV. Accede a precios especiales, herramientas de gestión y soporte prioritario.',
            'price' => 'Comisiones hasta 40%',
            'original_price' => null,
            'discount_percentage' => 0,
            'features' => json_encode([
                'Precios especiales para revendedores',
                'Panel de gestión de clientes',
                'Soporte técnico prioritario',
                'Comisiones atractivas',
                'Material promocional incluido',
                'Capacitación completa',
                'Herramientas de marketing',
                'Reportes de ventas detallados'
            ]),
            'button_text' => 'Solicitar Ser Revendedor',
            'button_color' => 'secondary',
            'is_active' => 1,
            'promotion_text' => '¡Oportunidad de negocio exclusiva!',
            'promotion_badge' => 'NEGOCIO'
        ];
        
        $stmt = $pdo->prepare("
            INSERT INTO service_config (
                service_type, title, subtitle, description, price, original_price, 
                discount_percentage, features, button_text, button_color, is_active,
                promotion_text, promotion_badge
            ) VALUES (
                :service_type, :title, :subtitle, :description, :price, :original_price,
                :discount_percentage, :features, :button_text, :button_color, :is_active,
                :promotion_text, :promotion_badge
            )
        ");
        
        if ($stmt->execute($resellerConfig)) {
            echo "<div class='success'>✅ Configuración de revendedores insertada exitosamente</div>";
        } else {
            echo "<div class='error'>❌ Error insertando configuración de revendedores</div>";
        }
    }
    
    // Mostrar todas las configuraciones actuales
    echo "<h3>📊 Todas las configuraciones de servicios:</h3>";
    $stmt = $pdo->query("SELECT service_type, title, price, is_active FROM service_config ORDER BY service_type");
    $configs = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table style='width: 100%; border-collapse: collapse; margin: 1rem 0;'>";
    echo "<tr style='background: #f8fafc;'>";
    echo "<th style='border: 1px solid #e2e8f0; padding: 0.5rem;'>Tipo</th>";
    echo "<th style='border: 1px solid #e2e8f0; padding: 0.5rem;'>Título</th>";
    echo "<th style='border: 1px solid #e2e8f0; padding: 0.5rem;'>Precio</th>";
    echo "<th style='border: 1px solid #e2e8f0; padding: 0.5rem;'>Estado</th>";
    echo "</tr>";
    
    foreach ($configs as $config) {
        $active = $config['is_active'] ? '✅ Activo' : '❌ Inactivo';
        echo "<tr>";
        echo "<td style='border: 1px solid #e2e8f0; padding: 0.5rem;'>" . ucfirst($config['service_type']) . "</td>";
        echo "<td style='border: 1px solid #e2e8f0; padding: 0.5rem;'>" . $config['title'] . "</td>";
        echo "<td style='border: 1px solid #e2e8f0; padding: 0.5rem;'>" . $config['price'] . "</td>";
        echo "<td style='border: 1px solid #e2e8f0; padding: 0.5rem;'>" . $active . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<div class='success'>🎉 ¡Configuración completada!</div>";
    
    echo "<h3>🎯 Próximos pasos:</h3>";
    echo "<a href='service_config_admin.php' class='btn'>🔧 Panel de Administración</a>";
    echo "<a href='service_options.php' class='btn'>👁️ Ver Servicios</a>";
    echo "<a href='test_service_config_api.php' class='btn'>🧪 Probar API</a>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Error: " . $e->getMessage() . "</div>";
    echo "<h3>🔍 Información de debug:</h3>";
    echo "<pre>";
    echo "Host: $db_host\n";
    echo "Database: $db_name\n";
    echo "User: $db_user\n";
    echo "Error: " . $e->getMessage() . "\n";
    echo "Code: " . $e->getCode() . "\n";
    echo "</pre>";
    
    echo "<h3>💡 Posibles soluciones:</h3>";
    echo "<ul>";
    echo "<li>Ejecutar primero <a href='create_service_config_table.php'>create_service_config_table.php</a></li>";
    echo "<li>Verificar que la tabla service_config exista</li>";
    echo "<li>Verificar permisos de la base de datos</li>";
    echo "</ul>";
}

echo "    </div>
</body>
</html>";
?>
