<?php
session_start();
header('Content-Type: application/json');

// Verificar si el usuario está logueado como admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    echo json_encode(['success' => false, 'error' => 'No autorizado']);
    exit;
}

// Configuración MySQL
$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    echo json_encode(['success' => false, 'error' => 'Error de base de datos']);
    exit;
}

if (!isset($_GET['id'])) {
    echo json_encode(['success' => false, 'error' => 'ID requerido']);
    exit;
}

$request_id = (int)$_GET['id'];

try {
    // Obtener información de la solicitud
    $stmt = $pdo->prepare("SELECT first_name, last_name FROM service_requests WHERE id = ?");
    $stmt->execute([$request_id]);
    $request = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$request) {
        echo json_encode(['success' => false, 'error' => 'Solicitud no encontrada']);
        exit;
    }
    
    // Obtener comunicaciones
    $stmt = $pdo->prepare("SELECT * FROM service_communications WHERE request_id = ? ORDER BY created_at ASC");
    $stmt->execute([$request_id]);
    $communications = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Marcar mensajes como leídos
    $pdo->prepare("UPDATE service_communications SET is_read = TRUE WHERE request_id = ? AND sender_type = 'client'")->execute([$request_id]);
    
    $client_name = $request['first_name'] . ' ' . $request['last_name'];
    
    // Generar HTML para la comunicación
    $html = '
    <div style="display: flex; flex-direction: column; height: 500px;">
        <div style="background: var(--gradient-primary); color: white; padding: 1rem; border-radius: 12px 12px 0 0; display: flex; align-items: center; gap: 0.5rem;">
            <i class="fas fa-user-circle" style="font-size: 1.5rem;"></i>
            <div>
                <h4 style="margin: 0; font-size: 1.1rem;">Comunicación con ' . htmlspecialchars($client_name) . '</h4>
                <p style="margin: 0; font-size: 0.9rem; opacity: 0.9;">Solicitud #' . $request_id . '</p>
            </div>
        </div>
        
        <div class="chat-messages" style="flex: 1; overflow-y: auto; padding: 1rem; background: rgba(255, 255, 255, 0.02); display: flex; flex-direction: column; gap: 1rem;">';
    
    if (empty($communications)) {
        $html .= '
            <div style="text-align: center; color: var(--text-secondary); padding: 2rem;">
                <i class="fas fa-comments" style="font-size: 2rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                <p>No hay mensajes aún. Inicia la conversación con el cliente.</p>
            </div>';
    } else {
        foreach ($communications as $comm) {
            $isAdmin = $comm['sender_type'] === 'admin';
            $messageClass = $isAdmin ? 'admin-message' : 'client-message';
            $alignClass = $isAdmin ? 'margin-left: auto; text-align: right;' : 'margin-right: auto; text-align: left;';
            $bgColor = $isAdmin ? 'var(--gradient-primary)' : 'var(--gradient-surface)';
            $textColor = $isAdmin ? 'white' : 'var(--text-primary)';
            
            $html .= '
            <div class="' . $messageClass . '" style="max-width: 70%; ' . $alignClass . '">
                <div style="background: ' . $bgColor . '; color: ' . $textColor . '; padding: 0.75rem 1rem; border-radius: 12px; box-shadow: var(--shadow-sm);">
                    <div style="font-weight: 600; margin-bottom: 0.25rem; font-size: 0.9rem; opacity: 0.9;">
                        ' . htmlspecialchars($comm['sender_name']) . '
                    </div>
                    <div style="line-height: 1.4;">
                        ' . nl2br(htmlspecialchars($comm['message'])) . '
                    </div>
                    <div style="font-size: 0.75rem; margin-top: 0.5rem; opacity: 0.7;">
                        ' . date('d/m/Y H:i', strtotime($comm['created_at'])) . '
                    </div>
                </div>
            </div>';
        }
    }
    
    $html .= '
        </div>
        
        <div style="background: rgba(255, 255, 255, 0.03); padding: 1rem; border-radius: 0 0 12px 12px; border-top: 1px solid rgba(255, 255, 255, 0.1);">
            <div style="display: flex; gap: 0.75rem; align-items: end;">
                <textarea id="messageInput" placeholder="Escribe tu mensaje al cliente..." style="flex: 1; padding: 0.75rem; background: var(--gradient-surface); border: 1px solid rgba(255, 255, 255, 0.15); border-radius: 8px; color: var(--text-primary); min-height: 60px; resize: vertical; font-family: inherit;" onkeydown="if(event.key===\'Enter\' && !event.shiftKey){event.preventDefault(); sendMessage();}"></textarea>
                <button onclick="sendMessage()" style="padding: 0.75rem 1.5rem; background: var(--gradient-primary); color: white; border: none; border-radius: 8px; cursor: pointer; font-weight: 600; display: flex; align-items: center; gap: 0.5rem; height: fit-content;">
                    <i class="fas fa-paper-plane"></i>
                    Enviar
                </button>
            </div>
            <div style="margin-top: 0.5rem; font-size: 0.8rem; color: var(--text-muted);">
                <i class="fas fa-info-circle"></i> Presiona Enter para enviar, Shift+Enter para nueva línea
            </div>
        </div>
    </div>
    
    <style>
        .chat-messages::-webkit-scrollbar {
            width: 6px;
        }
        
        .chat-messages::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 3px;
        }
        
        .chat-messages::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 3px;
        }
        
        .chat-messages::-webkit-scrollbar-thumb:hover {
            background: var(--primary-light);
        }
    </style>';
    
    echo json_encode(['success' => true, 'html' => $html]);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'error' => 'Error al obtener comunicación']);
}
?>
