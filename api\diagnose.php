<?php
/**
 * Diagnóstico completo de la API
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');

// Información del servidor
$server_info = [
    'php_version' => PHP_VERSION,
    'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
    'document_root' => $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown',
    'script_filename' => $_SERVER['SCRIPT_FILENAME'] ?? 'Unknown',
    'request_uri' => $_SERVER['REQUEST_URI'] ?? 'Unknown',
    'current_dir' => __DIR__,
    'timestamp' => date('Y-m-d H:i:s')
];

// Verificar archivos
$files_check = [
    'index.php' => file_exists(__DIR__ . '/index.php'),
    'debug.php' => file_exists(__DIR__ . '/debug.php'),
    'fcm_test_direct.php' => file_exists(__DIR__ . '/fcm_test_direct.php'),
    '.htaccess' => file_exists(__DIR__ . '/.htaccess')
];

// Leer contenido del index.php
$index_content = '';
$index_size = 0;
$has_fcm_case = false;
$has_fcm_function = false;

if (file_exists(__DIR__ . '/index.php')) {
    $index_content = file_get_contents(__DIR__ . '/index.php');
    $index_size = strlen($index_content);
    $has_fcm_case = strpos($index_content, "case 'fcm':") !== false;
    $has_fcm_function = strpos($index_content, "function handleFCM") !== false;
}

// Probar el endpoint directamente
$fcm_test_result = null;
try {
    // Simular una petición POST al endpoint FCM
    $test_data = [
        'user_id' => 'Casa122',
        'fcm_token' => 'test_token_123',
        'device_type' => 'android',
        'app_version' => '1.0.0'
    ];
    
    // Incluir el archivo index.php y capturar la salida
    ob_start();
    
    // Simular variables de entorno para POST /fcm/token
    $_SERVER['REQUEST_METHOD'] = 'POST';
    $_SERVER['REQUEST_URI'] = '/series/api/fcm/token';
    $_POST = [];
    
    // Simular el input JSON
    $json_input = json_encode($test_data);
    
    // Capturar cualquier salida
    $output = ob_get_clean();
    
    $fcm_test_result = [
        'attempted' => true,
        'output_length' => strlen($output),
        'output_preview' => substr($output, 0, 200)
    ];
    
} catch (Exception $e) {
    $fcm_test_result = [
        'attempted' => true,
        'error' => $e->getMessage()
    ];
}

// Respuesta completa
echo json_encode([
    'success' => true,
    'diagnosis' => [
        'server_info' => $server_info,
        'files_check' => $files_check,
        'index_analysis' => [
            'exists' => file_exists(__DIR__ . '/index.php'),
            'size_bytes' => $index_size,
            'has_fcm_case' => $has_fcm_case,
            'has_fcm_function' => $has_fcm_function,
            'readable' => is_readable(__DIR__ . '/index.php'),
            'writable' => is_writable(__DIR__ . '/index.php')
        ],
        'fcm_test' => $fcm_test_result,
        'cache_info' => [
            'opcache_enabled' => function_exists('opcache_get_status') ? opcache_get_status() : 'Not available',
            'apc_enabled' => function_exists('apc_cache_info') ? 'Yes' : 'No'
        ]
    ]
], JSON_PRETTY_PRINT);
?>
