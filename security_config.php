<?php
/**
 * Configuración de Seguridad Geográfica
 * Sistema de restricción de acceso por países
 */

// Lista de países permitidos (códigos ISO 3166-1 alpha-2)
$allowed_countries = [
    // Estados Unidos
    'US',
    
    // Latinoamérica
    'AR', // Argentina
    'BO', // Bolivia
    'BR', // Brasil
    'CL', // Chile
    'CO', // Colombia
    'CR', // Costa Rica
    'CU', // Cuba
    'DO', // República Dominicana
    'EC', // Ecuador
    'SV', // El Salvador
    'GT', // Guatemala
    'HN', // Honduras
    'MX', // México
    'NI', // Nicaragua
    'PA', // Panamá
    'PY', // Paraguay
    'PE', // <PERSON><PERSON>
    'PR', // Puerto Rico
    'UY', // Uruguay
    'VE', // Venezuela
];

/**
 * Obtiene la IP real del cliente
 */
function getRealIpAddress() {
    $ip_keys = ['HTTP_CF_CONNECTING_IP', 'HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_FORWARDED', 'HTTP_X_CLUSTER_CLIENT_IP', 'HTTP_FORWARDED_FOR', 'HTTP_FORWARDED', 'REMOTE_ADDR'];
    
    foreach ($ip_keys as $key) {
        if (array_key_exists($key, $_SERVER) === true) {
            foreach (explode(',', $_SERVER[$key]) as $ip) {
                $ip = trim($ip);
                
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                    return $ip;
                }
            }
        }
    }
    
    return $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
}

/**
 * Obtiene información de geolocalización de una IP
 */
function getCountryFromIP($ip) {
    // Si es IP local, permitir acceso (para desarrollo)
    if ($ip === '127.0.0.1' || $ip === '::1' || strpos($ip, '192.168.') === 0 || strpos($ip, '10.') === 0) {
        return 'US'; // Simular como US para desarrollo local
    }
    
    try {
        // Usar ip-api.com (gratuito, sin registro requerido)
        $url = "http://ip-api.com/json/{$ip}?fields=status,country,countryCode,message";
        
        $context = stream_context_create([
            'http' => [
                'timeout' => 5,
                'user_agent' => 'Mozilla/5.0 (compatible; SecurityBot/1.0)'
            ]
        ]);
        
        $response = @file_get_contents($url, false, $context);
        
        if ($response === false) {
            // Si falla la API, registrar y permitir acceso por defecto
            error_log("Geolocation API failed for IP: {$ip}");
            return 'US'; // Fallback seguro
        }
        
        $data = json_decode($response, true);
        
        if ($data && $data['status'] === 'success') {
            return $data['countryCode'];
        } else {
            error_log("Geolocation API error for IP {$ip}: " . ($data['message'] ?? 'Unknown error'));
            return 'US'; // Fallback seguro
        }
        
    } catch (Exception $e) {
        error_log("Geolocation exception for IP {$ip}: " . $e->getMessage());
        return 'US'; // Fallback seguro
    }
}

/**
 * Verifica si el país está permitido
 */
function isCountryAllowed($country_code) {
    global $allowed_countries;
    return in_array(strtoupper($country_code), $allowed_countries);
}

/**
 * Verifica si la IP actual tiene acceso permitido
 */
function checkGeographicAccess() {
    $ip = getRealIpAddress();
    $country = getCountryFromIP($ip);
    
    // Log del intento de acceso
    $log_entry = date('Y-m-d H:i:s') . " - IP: {$ip} - Country: {$country}";
    
    if (isCountryAllowed($country)) {
        $log_entry .= " - ACCESS GRANTED\n";
        error_log($log_entry, 3, 'access_log.txt');
        return true;
    } else {
        $log_entry .= " - ACCESS DENIED\n";
        error_log($log_entry, 3, 'access_log.txt');
        return false;
    }
}

/**
 * Bloquea el acceso y muestra mensaje de error
 */
function blockAccess() {
    $ip = getRealIpAddress();
    $country = getCountryFromIP($ip);
    
    // Log del bloqueo
    error_log("BLOCKED ACCESS - IP: {$ip} - Country: {$country} - " . date('Y-m-d H:i:s'), 3, 'blocked_access.txt');
    
    http_response_code(403);
    ?>
    <!DOCTYPE html>
    <html lang="es">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>🚫 Acceso Restringido</title>
        <style>
            body {
                font-family: 'Arial', sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0;
                padding: 20px;
            }
            .container {
                background: rgba(255,255,255,0.1);
                backdrop-filter: blur(10px);
                border-radius: 20px;
                padding: 40px;
                text-align: center;
                max-width: 500px;
                border: 1px solid rgba(255,255,255,0.2);
            }
            .icon {
                font-size: 4rem;
                margin-bottom: 20px;
            }
            h1 {
                margin-bottom: 20px;
                font-size: 2rem;
            }
            p {
                margin-bottom: 15px;
                line-height: 1.6;
                opacity: 0.9;
            }
            .info {
                background: rgba(255,255,255,0.1);
                padding: 20px;
                border-radius: 10px;
                margin-top: 20px;
                font-size: 0.9rem;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="icon">🚫</div>
            <h1>Acceso Restringido</h1>
            <p>Lo sentimos, el acceso a esta plataforma está restringido geográficamente.</p>
            <p>Solo se permite el acceso desde países de Latinoamérica y Estados Unidos.</p>
            <div class="info">
                <strong>Información técnica:</strong><br>
                IP: <?php echo htmlspecialchars($ip); ?><br>
                País detectado: <?php echo htmlspecialchars($country); ?><br>
                Fecha: <?php echo date('Y-m-d H:i:s'); ?>
            </div>
        </div>
    </body>
    </html>
    <?php
    exit;
}

/**
 * Función principal de verificación de seguridad geográfica
 */
function enforceGeographicSecurity() {
    if (!checkGeographicAccess()) {
        blockAccess();
    }
}
?>
