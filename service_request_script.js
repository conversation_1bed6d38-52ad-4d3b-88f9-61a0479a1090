// JavaScript para el Modal de Solicitud de Servicio

let currentStep = 1;
const totalSteps = 6;

// Abrir modal
function openServiceRequestModal() {
    const modal = document.getElementById('serviceRequestModal');
    modal.style.display = 'flex';
    document.body.style.overflow = 'hidden';
    
    // Reset form
    resetForm();
    showStep(1);
}

// Cerrar modal
function closeServiceRequestModal() {
    const modal = document.getElementById('serviceRequestModal');
    modal.style.display = 'none';
    document.body.style.overflow = 'auto';
}

// Reset formulario
function resetForm() {
    document.getElementById('serviceRequestForm').reset();
    currentStep = 1;
    updateProgressIndicator();
    
    // Reset visibility of conditional fields
    document.getElementById('customCredentialsFields').classList.remove('hidden');
    document.getElementById('deviceInfo').style.display = 'none';
    document.getElementById('smartTvFields').style.display = 'none';
    document.getElementById('availableApps').innerHTML = '';
}

// Mostrar paso específico
function showStep(step) {
    // Ocultar todos los pasos
    document.querySelectorAll('.form-step').forEach(s => {
        s.classList.remove('active');
    });
    
    // Mostrar paso actual
    document.getElementById(`step${step}`).classList.add('active');
    currentStep = step;
    updateProgressIndicator();
}

// Siguiente paso
function nextStep(step) {
    if (validateCurrentStep()) {
        showStep(step);
        
        // Cargar aplicaciones cuando llegue al paso 5
        if (step === 5) {
            loadAvailableApps();
        }
    }
}

// Paso anterior
function prevStep(step) {
    showStep(step);
}

// Validar paso actual
function validateCurrentStep() {
    const currentStepElement = document.getElementById(`step${currentStep}`);
    const requiredFields = currentStepElement.querySelectorAll('input[required], select[required], textarea[required]');
    
    let isValid = true;
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.style.borderColor = '#ef4444';
            isValid = false;
            
            // Remover el error después de 3 segundos
            setTimeout(() => {
                field.style.borderColor = '';
            }, 3000);
        }
    });
    
    // Validaciones específicas por paso
    switch (currentStep) {
        case 1:
            // Validar email
            const email = document.getElementById('email');
            if (email.value && !isValidEmail(email.value)) {
                email.style.borderColor = '#ef4444';
                showToast('Por favor ingresa un email válido', 'error');
                isValid = false;
            }
            
            // Validar teléfono
            const phone = document.getElementById('phone');
            if (phone.value && !isValidPhone(phone.value)) {
                phone.style.borderColor = '#ef4444';
                showToast('Por favor ingresa un teléfono válido', 'error');
                isValid = false;
            }
            break;
            
        case 4:
            // Validar que se haya seleccionado un dispositivo
            const deviceType = document.querySelector('input[name="deviceType"]:checked');
            if (!deviceType) {
                showToast('Por favor selecciona un tipo de dispositivo', 'error');
                isValid = false;
            }
            break;
            
        case 6:
            // Validar términos y condiciones
            const acceptTerms = document.getElementById('acceptTerms');
            if (!acceptTerms.checked) {
                showToast('Debes aceptar los términos y condiciones', 'error');
                isValid = false;
            }
            break;
    }
    
    if (!isValid) {
        showToast('Por favor completa todos los campos requeridos', 'error');
    }
    
    return isValid;
}

// Actualizar indicador de progreso
function updateProgressIndicator() {
    document.querySelectorAll('.progress-step').forEach((step, index) => {
        const stepNumber = index + 1;
        
        if (stepNumber < currentStep) {
            step.classList.add('completed');
            step.classList.remove('active');
        } else if (stepNumber === currentStep) {
            step.classList.add('active');
            step.classList.remove('completed');
        } else {
            step.classList.remove('active', 'completed');
        }
    });
}

// Manejar cambio de opción de credenciales
document.addEventListener('DOMContentLoaded', function() {
    const credentialOptions = document.querySelectorAll('input[name="credentialOption"]');
    const customFields = document.getElementById('customCredentialsFields');
    
    credentialOptions.forEach(option => {
        option.addEventListener('change', function() {
            if (this.value === 'custom') {
                customFields.classList.remove('hidden');
            } else {
                customFields.classList.add('hidden');
            }
        });
    });
    
    // Manejar cambio de tipo de dispositivo
    const deviceOptions = document.querySelectorAll('input[name="deviceType"]');
    const deviceInfo = document.getElementById('deviceInfo');
    const smartTvFields = document.getElementById('smartTvFields');
    
    deviceOptions.forEach(option => {
        option.addEventListener('change', function() {
            const deviceType = this.value;
            
            // Mostrar campos adicionales según el dispositivo
            if (deviceType === 'other') {
                deviceInfo.style.display = 'block';
                smartTvFields.style.display = 'none';
            } else if (deviceType.includes('smart_tv')) {
                deviceInfo.style.display = 'none';
                smartTvFields.style.display = 'block';
            } else {
                deviceInfo.style.display = 'none';
                smartTvFields.style.display = 'none';
            }
        });
    });
});

// Cargar aplicaciones disponibles
async function loadAvailableApps() {
    const deviceType = document.querySelector('input[name="deviceType"]:checked');
    if (!deviceType) return;
    
    const appsContainer = document.getElementById('availableApps');
    appsContainer.innerHTML = '<div class="loading">Cargando aplicaciones...</div>';
    
    try {
        const response = await fetch('get_available_apps.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                device_type: deviceType.value
            })
        });
        
        const apps = await response.json();
        
        if (apps.length === 0) {
            appsContainer.innerHTML = `
                <div style="text-align: center; padding: 2rem; color: var(--text-secondary);">
                    <i class="fas fa-mobile-alt" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                    <h4>No hay aplicaciones disponibles</h4>
                    <p>Para este tipo de dispositivo aún no tenemos aplicaciones configuradas.</p>
                </div>
            `;
            return;
        }
        
        appsContainer.innerHTML = apps.map(app => `
            <div class="app-card">
                <div class="app-header">
                    <div class="app-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <div class="app-info">
                        <h6>${app.name}</h6>
                        <div class="app-version">v${app.version || 'N/A'}</div>
                    </div>
                </div>
                <div class="app-description">
                    ${app.installation_guide || 'Aplicación recomendada para tu dispositivo'}
                </div>
                <div class="app-details">
                    ${app.requirements ? `<span class="app-tag">${app.requirements}</span>` : ''}
                    ${app.file_size ? `<span class="app-tag">${app.file_size}</span>` : ''}
                </div>
                <div class="app-actions">
                    ${app.download_url ? `
                        <a href="${app.download_url}" target="_blank" class="app-btn">
                            <i class="fas fa-download"></i> Descargar
                        </a>
                    ` : ''}
                    <button onclick="showAppGuide('${app.name}', '${app.installation_guide}')" class="app-btn secondary">
                        <i class="fas fa-info-circle"></i> Guía
                    </button>
                </div>
            </div>
        `).join('');
        
    } catch (error) {
        console.error('Error loading apps:', error);
        appsContainer.innerHTML = `
            <div style="text-align: center; padding: 2rem; color: var(--error-color);">
                <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 1rem;"></i>
                <h4>Error al cargar aplicaciones</h4>
                <p>No se pudieron cargar las aplicaciones disponibles.</p>
            </div>
        `;
    }
}

// Mostrar guía de aplicación
function showAppGuide(appName, guide) {
    showToast(`Guía para ${appName}: ${guide}`, 'info', 8000);
}

// Enviar formulario
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('serviceRequestForm');
    
    form.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        if (!validateCurrentStep()) {
            return;
        }
        
        const submitBtn = document.getElementById('submitBtn');
        const originalText = submitBtn.innerHTML;
        
        // Cambiar estado del botón
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Enviando...';
        submitBtn.disabled = true;
        
        try {
            // Recopilar datos del formulario
            const formData = new FormData(form);
            const data = Object.fromEntries(formData.entries());
            
            // Agregar datos adicionales
            data.device_type = document.querySelector('input[name="deviceType"]:checked')?.value;
            data.service_type = document.querySelector('input[name="serviceType"]:checked')?.value;
            data.credential_option = document.querySelector('input[name="credentialOption"]:checked')?.value;
            data.admin_chooses_credentials = data.credential_option === 'admin';
            
            const response = await fetch('submit_service_request.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });
            
            const result = await response.json();
            
            if (result.success) {
                showToast('¡Solicitud enviada exitosamente! Te contactaremos pronto.', 'success');
                closeServiceRequestModal();

                // Mostrar opción de ver confirmación
                setTimeout(() => {
                    if (confirm('¿Deseas ver los detalles de tu solicitud?')) {
                        window.location.href = 'service_request_confirmation.php?id=' + result.request_id;
                    }
                }, 1000);
            } else {
                showToast(result.message || 'Error al enviar la solicitud', 'error');
            }
            
        } catch (error) {
            console.error('Error submitting form:', error);
            showToast('Error al enviar la solicitud. Inténtalo de nuevo.', 'error');
        } finally {
            // Restaurar botón
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    });
});

// Funciones de validación
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function isValidPhone(phone) {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
}

// Función para mostrar notificaciones toast
function showToast(message, type = 'info', duration = 3000) {
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: var(--gradient-card);
        border: 1px solid rgba(255, 255, 255, 0.12);
        border-radius: var(--radius-lg);
        padding: 1rem 1.5rem;
        color: var(--text-primary);
        backdrop-filter: var(--blur-backdrop);
        box-shadow: var(--shadow-lg);
        z-index: 10000;
        transform: translateX(100%);
        transition: var(--transition-normal);
        max-width: 350px;
        word-wrap: break-word;
    `;
    
    const icons = {
        success: 'check-circle',
        error: 'exclamation-triangle',
        info: 'info-circle',
        warning: 'exclamation-circle'
    };
    
    toast.innerHTML = `
        <div style="display: flex; align-items: center; gap: 0.5rem;">
            <i class="fas fa-${icons[type] || 'info-circle'}"></i>
            <span>${message}</span>
        </div>
    `;
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
        toast.style.transform = 'translateX(0)';
    }, 100);
    
    setTimeout(() => {
        toast.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, duration);
}

// Cerrar modal al hacer clic fuera
document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('serviceRequestModal');
    
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeServiceRequestModal();
        }
    });
    
    // Cerrar con tecla Escape
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && modal.style.display === 'flex') {
            closeServiceRequestModal();
        }
    });
});
