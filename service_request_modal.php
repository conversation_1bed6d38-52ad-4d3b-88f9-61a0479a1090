<!-- Incluir estilos modernos de modales -->
<link rel="stylesheet" href="modal_styles.css">

<!-- Modal de Solicitud de Servicio -->
<div id="serviceRequestModal" class="modal" style="display: none;">
    <div class="modal-content service-request-modal">
        <div class="modal-header">
            <h3 class="modal-title">
                <i class="fas fa-hand-holding-heart"></i>
                Solicitar Servicio IPTV
            </h3>
            <button class="modal-close" onclick="closeServiceRequestModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <div class="modal-body">
        <form id="serviceRequestForm" class="service-request-form">
            <!-- Paso 1: Información Personal -->
            <div class="form-step active" id="step1">
                <div class="step-header">
                    <h4><i class="fas fa-user"></i> Información Personal</h4>
                    <p>Completa tus datos para procesar tu solicitud</p>
                </div>
                
                <div class="form-grid">
                    <div class="form-group">
                        <label for="firstName">Nombre *</label>
                        <input type="text" id="firstName" name="firstName" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="lastName">Apellido *</label>
                        <input type="text" id="lastName" name="lastName" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="email">Correo Electrónico *</label>
                        <input type="email" id="email" name="email" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="phone">Teléfono *</label>
                        <input type="tel" id="phone" name="phone" required placeholder="+57 ************">
                    </div>
                </div>
                
                <div class="form-actions">
                    <button type="button" class="btn btn-primary" onclick="nextStep(2)">
                        Siguiente <i class="fas fa-arrow-right"></i>
                    </button>
                </div>
            </div>
            
            <!-- Paso 2: Credenciales -->
            <div class="form-step" id="step2">
                <div class="step-header">
                    <h4><i class="fas fa-key"></i> Credenciales de Acceso</h4>
                    <p>Configura tu usuario y contraseña</p>
                </div>
                
                <div class="credential-options">
                    <div class="option-card">
                        <input type="radio" id="customCredentials" name="credentialOption" value="custom" checked>
                        <label for="customCredentials" class="option-label">
                            <i class="fas fa-edit"></i>
                            <span>Elegir mis credenciales</span>
                        </label>
                    </div>
                    
                    <div class="option-card">
                        <input type="radio" id="adminCredentials" name="credentialOption" value="admin">
                        <label for="adminCredentials" class="option-label">
                            <i class="fas fa-user-cog"></i>
                            <span>Que el administrador las elija</span>
                        </label>
                    </div>
                </div>
                
                <div id="customCredentialsFields" class="credentials-fields">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="preferredUsername">Usuario Deseado</label>
                            <input type="text" id="preferredUsername" name="preferredUsername" placeholder="mi_usuario">
                        </div>
                        
                        <div class="form-group">
                            <label for="preferredPassword">Contraseña Deseada</label>
                            <input type="password" id="preferredPassword" name="preferredPassword" placeholder="••••••••">
                        </div>
                    </div>
                </div>
                
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="prevStep(1)">
                        <i class="fas fa-arrow-left"></i> Anterior
                    </button>
                    <button type="button" class="btn btn-primary" onclick="nextStep(3)">
                        Siguiente <i class="fas fa-arrow-right"></i>
                    </button>
                </div>
            </div>
            
            <!-- Paso 3: Tipo de Servicio -->
            <div class="form-step" id="step3">
                <div class="step-header">
                    <h4><i class="fas fa-shopping-cart"></i> Tipo de Servicio</h4>
                    <p>Selecciona el tipo de servicio que deseas</p>
                </div>
                
                <div class="service-options">
                    <div class="service-card">
                        <input type="radio" id="trialService" name="serviceType" value="trial" checked>
                        <label for="trialService" class="service-label">
                            <div class="service-icon">
                                <i class="fas fa-play-circle"></i>
                            </div>
                            <h5>Prueba Gratuita</h5>
                            <p>Prueba nuestro servicio por tiempo limitado</p>
                            <div class="service-features">
                                <span><i class="fas fa-check"></i> 24-48 horas de prueba</span>
                                <span><i class="fas fa-check"></i> Acceso completo</span>
                                <span><i class="fas fa-check"></i> Sin compromiso</span>
                            </div>
                        </label>
                    </div>
                    
                    <div class="service-card">
                        <input type="radio" id="directPurchase" name="serviceType" value="direct_purchase">
                        <label for="directPurchase" class="service-label">
                            <div class="service-icon">
                                <i class="fas fa-credit-card"></i>
                            </div>
                            <h5>Compra Directa</h5>
                            <p>Adquiere el servicio completo inmediatamente</p>
                            <div class="service-features">
                                <span><i class="fas fa-check"></i> Activación inmediata</span>
                                <span><i class="fas fa-check"></i> Soporte prioritario</span>
                                <span><i class="fas fa-check"></i> Mejor precio</span>
                            </div>
                        </label>
                    </div>
                </div>
                
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="prevStep(2)">
                        <i class="fas fa-arrow-left"></i> Anterior
                    </button>
                    <button type="button" class="btn btn-primary" onclick="nextStep(4)">
                        Siguiente <i class="fas fa-arrow-right"></i>
                    </button>
                </div>
            </div>
            
            <!-- Paso 4: Dispositivo -->
            <div class="form-step" id="step4">
                <div class="step-header">
                    <h4><i class="fas fa-tv"></i> Selecciona tu Dispositivo</h4>
                    <p>Elige el dispositivo donde usarás el servicio</p>
                </div>
                
                <div class="device-grid">
                    <div class="device-card">
                        <input type="radio" id="android" name="deviceType" value="android">
                        <label for="android" class="device-label">
                            <i class="fab fa-android"></i>
                            <span>Android</span>
                        </label>
                    </div>
                    
                    <div class="device-card">
                        <input type="radio" id="ios" name="deviceType" value="ios">
                        <label for="ios" class="device-label">
                            <i class="fab fa-apple"></i>
                            <span>iPhone/iPad</span>
                        </label>
                    </div>
                    
                    <div class="device-card">
                        <input type="radio" id="smartTvSamsung" name="deviceType" value="smart_tv_samsung">
                        <label for="smartTvSamsung" class="device-label">
                            <i class="fas fa-tv"></i>
                            <span>Smart TV Samsung</span>
                        </label>
                    </div>
                    
                    <div class="device-card">
                        <input type="radio" id="smartTvLg" name="deviceType" value="smart_tv_lg">
                        <label for="smartTvLg" class="device-label">
                            <i class="fas fa-tv"></i>
                            <span>Smart TV LG</span>
                        </label>
                    </div>
                    
                    <div class="device-card">
                        <input type="radio" id="smartTvOther" name="deviceType" value="smart_tv_other">
                        <label for="smartTvOther" class="device-label">
                            <i class="fas fa-tv"></i>
                            <span>Otra Smart TV</span>
                        </label>
                    </div>
                    
                    <div class="device-card">
                        <input type="radio" id="pcWindows" name="deviceType" value="pc_windows">
                        <label for="pcWindows" class="device-label">
                            <i class="fab fa-windows"></i>
                            <span>PC Windows</span>
                        </label>
                    </div>
                    
                    <div class="device-card">
                        <input type="radio" id="pcMac" name="deviceType" value="pc_mac">
                        <label for="pcMac" class="device-label">
                            <i class="fab fa-apple"></i>
                            <span>Mac</span>
                        </label>
                    </div>
                    
                    <div class="device-card">
                        <input type="radio" id="other" name="deviceType" value="other">
                        <label for="other" class="device-label">
                            <i class="fas fa-question"></i>
                            <span>Otro</span>
                        </label>
                    </div>
                </div>
                
                <!-- Información adicional del dispositivo -->
                <div id="deviceInfo" class="device-info" style="display: none;">
                    <div class="form-group">
                        <label for="deviceDetails">Información del Dispositivo</label>
                        <textarea id="deviceDetails" name="deviceDetails" placeholder="Describe tu dispositivo (modelo, versión, etc.)"></textarea>
                    </div>
                </div>
                
                <!-- Campos para Smart TV -->
                <div id="smartTvFields" class="smart-tv-fields" style="display: none;">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="macAddress">Dirección MAC</label>
                            <input type="text" id="macAddress" name="macAddress" placeholder="00:11:22:33:44:55">
                            <small>Encuentra la MAC en Configuración > Red de tu TV</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="serialKey">Número de Serie</label>
                            <input type="text" id="serialKey" name="serialKey" placeholder="ABC123DEF456">
                            <small>Encuentra el serial en Configuración > Información del sistema</small>
                        </div>
                    </div>
                </div>
                
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="prevStep(3)">
                        <i class="fas fa-arrow-left"></i> Anterior
                    </button>
                    <button type="button" class="btn btn-primary" onclick="nextStep(5)">
                        Siguiente <i class="fas fa-arrow-right"></i>
                    </button>
                </div>
            </div>
            
            <!-- Paso 5: Aplicaciones Disponibles -->
            <div class="form-step" id="step5">
                <div class="step-header">
                    <h4><i class="fas fa-mobile-alt"></i> Aplicaciones Recomendadas</h4>
                    <p>Estas son las aplicaciones disponibles para tu dispositivo</p>
                </div>
                
                <div id="availableApps" class="apps-container">
                    <!-- Las aplicaciones se cargarán dinámicamente -->
                </div>
                
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="prevStep(4)">
                        <i class="fas fa-arrow-left"></i> Anterior
                    </button>
                    <button type="button" class="btn btn-primary" onclick="nextStep(6)">
                        Siguiente <i class="fas fa-arrow-right"></i>
                    </button>
                </div>
            </div>
            
            <!-- Paso 6: Términos y Confirmación -->
            <div class="form-step" id="step6">
                <div class="step-header">
                    <h4><i class="fas fa-file-contract"></i> Términos y Condiciones</h4>
                    <p>Revisa y acepta nuestros términos de servicio</p>
                </div>
                
                <div class="terms-container">
                    <div class="terms-content">
                        <h5>Términos de Servicio IPTV</h5>
                        <ul>
                            <li>El servicio es para uso personal únicamente</li>
                            <li>No está permitido compartir las credenciales</li>
                            <li>El contenido está sujeto a disponibilidad</li>
                            <li>Nos reservamos el derecho de suspender el servicio por mal uso</li>
                            <li>Los reembolsos están sujetos a nuestras políticas</li>
                            <li>El soporte técnico está disponible 24/7</li>
                        </ul>
                    </div>
                    
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="acceptTerms" name="acceptTerms" required>
                            <span class="checkmark"></span>
                            Acepto los términos y condiciones del servicio
                        </label>
                    </div>
                </div>
                
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="prevStep(5)">
                        <i class="fas fa-arrow-left"></i> Anterior
                    </button>
                    <button type="submit" class="btn btn-success" id="submitBtn">
                        <i class="fas fa-paper-plane"></i> Enviar Solicitud
                    </button>
                </div>
            </div>
        </form>
        </div> <!-- Cierre de modal-body -->

        <!-- Indicador de progreso -->
        <div class="progress-indicator">
            <div class="progress-step active" data-step="1">1</div>
            <div class="progress-step" data-step="2">2</div>
            <div class="progress-step" data-step="3">3</div>
            <div class="progress-step" data-step="4">4</div>
            <div class="progress-step" data-step="5">5</div>
            <div class="progress-step" data-step="6">6</div>
        </div>
    </div>
</div>
