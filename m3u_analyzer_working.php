<?php
// Analizador M3U que realmente funciona
error_reporting(E_ALL);
ini_set('display_errors', 1);
set_time_limit(300);

session_start();

if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Error de conexión: " . $e->getMessage());
}

$message = '';
$message_type = '';
$analysis_results = null;
$processing = false;

// Obtener ID de lista
$list_id = $_GET['list_id'] ?? $_POST['list_id'] ?? null;

// Procesar análisis
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['analyze']) && $list_id) {
    $processing = true;
    
    try {
        // Obtener datos de la lista
        $stmt = $pdo->prepare("SELECT * FROM m3u_lists WHERE id = ?");
        $stmt->execute([$list_id]);
        $list = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$list) {
            throw new Exception("Lista no encontrada");
        }
        
        echo "<div style='background:#1a1a1a;color:white;padding:20px;font-family:monospace;'>";
        echo "<h3>🔍 Analizando: " . htmlspecialchars($list['name']) . "</h3>";
        echo "<p>📡 URL: " . htmlspecialchars($list['url']) . "</p>";
        flush();
        
        // Descargar contenido
        echo "<p>⏳ Descargando lista M3U...</p>";
        flush();
        
        $context = stream_context_create([
            'http' => [
                'timeout' => 30,
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            ]
        ]);
        
        if ($list['username'] && $list['password']) {
            echo "<p>🔐 Usando autenticación...</p>";
            flush();
            $auth = base64_encode($list['username'] . ':' . $list['password']);
            $context = stream_context_create([
                'http' => [
                    'header' => "Authorization: Basic $auth\r\n",
                    'timeout' => 30,
                    'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                ]
            ]);
        }
        
        $content = @file_get_contents($list['url'], false, $context);
        
        if ($content === false) {
            throw new Exception("No se pudo descargar la lista M3U");
        }
        
        echo "<p>✅ Descarga exitosa - Tamaño: " . number_format(strlen($content)) . " bytes</p>";
        flush();
        
        // Verificar que sea M3U válido
        if (strpos($content, '#EXTM3U') === false && strpos($content, '#EXTINF') === false) {
            echo "<p>❌ El archivo no parece ser un M3U válido</p>";
            echo "<p>📄 Primeros 200 caracteres:</p>";
            echo "<pre style='background:#333;padding:10px;'>" . htmlspecialchars(substr($content, 0, 200)) . "</pre>";
            throw new Exception("El archivo no es un M3U válido");
        }
        
        echo "<p>✅ Archivo M3U válido detectado</p>";
        flush();
        
        // Procesar líneas
        $lines = explode("\n", $content);
        echo "<p>📊 Procesando " . number_format(count($lines)) . " líneas...</p>";
        flush();
        
        $items = [];
        $current_item = null;
        $processed = 0;
        
        foreach ($lines as $line) {
            $line = trim($line);
            
            if (strpos($line, '#EXTINF:') === 0) {
                $current_item = [];
                
                // Extraer título
                if (preg_match('/#EXTINF:[^,]*,(.*)/', $line, $matches)) {
                    $current_item['title'] = trim($matches[1]);
                } else {
                    $current_item['title'] = 'Sin título';
                }
                
            } elseif ($line && !empty($current_item) && strpos($line, '#') !== 0) {
                // URL del stream
                $current_item['url'] = $line;
                
                if (!empty($current_item['title'])) {
                    $title = $current_item['title'];
                    $clean_title = strtolower(preg_replace('/[^\w\s]/', '', $title));
                    
                    // Detectar tipo
                    $media_type = 'unknown';
                    if (preg_match('/s\d+e\d+|season|temporada|\d+x\d+/i', $title)) {
                        $media_type = 'tv';
                    } elseif (preg_match('/\(\d{4}\)|\d{4}|movie|film|película/i', $title)) {
                        $media_type = 'movie';
                    }
                    
                    // Extraer año
                    $year = null;
                    if (preg_match('/\((\d{4})\)/', $title, $matches)) {
                        $year = (int)$matches[1];
                    } elseif (preg_match('/(\d{4})/', $title, $matches)) {
                        $test_year = (int)$matches[1];
                        if ($test_year >= 1900 && $test_year <= date('Y') + 2) {
                            $year = $test_year;
                        }
                    }
                    
                    $items[] = [
                        'title' => $title,
                        'clean_title' => $clean_title,
                        'url' => $current_item['url'],
                        'media_type' => $media_type,
                        'year' => $year
                    ];
                    
                    $processed++;
                    if ($processed % 500 == 0) {
                        echo "<p>⏳ Procesados " . number_format($processed) . " elementos...</p>";
                        flush();
                    }
                }
                
                $current_item = null;
            }
        }
        
        echo "<p>✅ Análisis completado - Total elementos: " . number_format(count($items)) . "</p>";
        flush();
        
        // Limpiar contenido anterior
        echo "<p>🗑️ Limpiando contenido anterior...</p>";
        flush();
        
        $stmt = $pdo->prepare("DELETE FROM m3u_content WHERE list_id = ?");
        $stmt->execute([$list_id]);
        
        // Insertar nuevo contenido
        echo "<p>💾 Guardando en base de datos...</p>";
        flush();
        
        $stmt = $pdo->prepare("
            INSERT INTO m3u_content 
            (list_id, title, clean_title, media_type, year, season, episode, url) 
            VALUES (?, ?, ?, ?, ?, NULL, NULL, ?)
        ");
        
        $inserted = 0;
        foreach ($items as $item) {
            $stmt->execute([
                $list_id,
                $item['title'],
                $item['clean_title'],
                $item['media_type'],
                $item['year'],
                $item['url']
            ]);
            $inserted++;
            
            if ($inserted % 1000 == 0) {
                echo "<p>💾 Guardados " . number_format($inserted) . " elementos...</p>";
                flush();
            }
        }
        
        // Actualizar estadísticas
        $stmt = $pdo->prepare("
            UPDATE m3u_lists 
            SET last_scan = NOW(), total_items = ?, last_updated = NOW() 
            WHERE id = ?
        ");
        $stmt->execute([$inserted, $list_id]);
        
        $analysis_results = [
            'total_items' => $inserted,
            'movies' => count(array_filter($items, fn($i) => $i['media_type'] === 'movie')),
            'tv_shows' => count(array_filter($items, fn($i) => $i['media_type'] === 'tv')),
            'unknown' => count(array_filter($items, fn($i) => $i['media_type'] === 'unknown')),
            'with_year' => count(array_filter($items, fn($i) => $i['year'] !== null))
        ];
        
        echo "<p>🎉 ¡Análisis completado exitosamente!</p>";
        echo "<p>📊 Estadísticas:</p>";
        echo "<ul>";
        echo "<li>📺 Total: " . number_format($analysis_results['total_items']) . "</li>";
        echo "<li>🎬 Películas: " . number_format($analysis_results['movies']) . "</li>";
        echo "<li>📺 Series: " . number_format($analysis_results['tv_shows']) . "</li>";
        echo "<li>❓ Sin clasificar: " . number_format($analysis_results['unknown']) . "</li>";
        echo "<li>📅 Con año: " . number_format($analysis_results['with_year']) . "</li>";
        echo "</ul>";
        
        echo "<p style='margin-top:20px;'>";
        echo "<a href='m3u_search.php' style='background:#46d347;color:#1a1a1a;padding:10px 20px;text-decoration:none;border-radius:5px;font-weight:bold;'>🔍 Buscar Contenido</a> ";
        echo "<a href='m3u_manager.php' style='background:#17a2b8;color:white;padding:10px 20px;text-decoration:none;border-radius:5px;font-weight:bold;margin-left:10px;'>📡 Volver al Gestor</a>";
        echo "</p>";
        
        echo "</div>";
        
        $message = "Análisis completado exitosamente. Se procesaron $inserted elementos.";
        $message_type = "success";
        
    } catch (Exception $e) {
        echo "<p style='color:#dc3545;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
        $message = "Error durante el análisis: " . $e->getMessage();
        $message_type = "error";
    }
    
    exit; // Terminar aquí para mostrar solo el progreso
}

// Obtener listas disponibles
$stmt = $pdo->query("SELECT id, name, is_active, last_scan, total_items FROM m3u_lists ORDER BY name");
$all_lists = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Obtener información de la lista seleccionada
$selected_list = null;
if ($list_id) {
    $stmt = $pdo->prepare("SELECT * FROM m3u_lists WHERE id = ?");
    $stmt->execute([$list_id]);
    $selected_list = $stmt->fetch(PDO::FETCH_ASSOC);
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Analizador M3U - RogsMediaTV</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #141414; color: white; }
        .container { max-width: 800px; margin: 0 auto; }
        .header { text-align: center; margin-bottom: 2rem; }
        .header h1 { color: #46d347; }
        .section { background: #2d2d2d; padding: 2rem; border-radius: 10px; margin-bottom: 2rem; border: 1px solid #404040; }
        .form-group { margin: 15px 0; }
        .form-group label { display: block; margin-bottom: 5px; color: #b0b0b0; }
        .form-input { width: 100%; padding: 10px; border: 1px solid #404040; border-radius: 5px; background: #1a1a1a; color: white; }
        .btn { padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; font-weight: bold; text-decoration: none; display: inline-block; }
        .btn-primary { background: #46d347; color: #1a1a1a; }
        .btn-secondary { background: #6c757d; color: white; }
        .back-link { color: #46d347; text-decoration: none; margin-bottom: 2rem; display: inline-block; }
        .list-info { background: #1a1a1a; padding: 15px; border-radius: 8px; margin: 15px 0; }
        .success { background: rgba(40, 167, 69, 0.2); color: #28a745; padding: 15px; border-radius: 8px; margin: 15px 0; }
        .error { background: rgba(220, 53, 69, 0.2); color: #dc3545; padding: 15px; border-radius: 8px; margin: 15px 0; }
    </style>
</head>
<body>
    <div class="container">
        <a href="m3u_manager.php" class="back-link">← Volver al Gestor M3U</a>
        
        <div class="header">
            <h1>🔍 Analizador M3U</h1>
            <p>Analiza y extrae contenido de tus listas IPTV</p>
        </div>

        <?php if ($message): ?>
        <div class="<?php echo $message_type; ?>">
            <?php echo htmlspecialchars($message); ?>
        </div>
        <?php endif; ?>

        <div class="section">
            <h2>📋 Seleccionar Lista para Analizar</h2>
            
            <form method="GET">
                <div class="form-group">
                    <label for="list_id">Lista M3U:</label>
                    <select name="list_id" id="list_id" class="form-input" required onchange="this.form.submit()">
                        <option value="">Seleccionar lista...</option>
                        <?php foreach ($all_lists as $list): ?>
                        <option value="<?php echo $list['id']; ?>" <?php echo $list_id == $list['id'] ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($list['name']); ?>
                            (<?php echo number_format($list['total_items']); ?> elementos)
                            <?php if (!$list['is_active']): ?> - INACTIVA<?php endif; ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </form>

            <?php if ($selected_list): ?>
            <div class="list-info">
                <h3>📊 Información de la Lista</h3>
                <p><strong>Nombre:</strong> <?php echo htmlspecialchars($selected_list['name']); ?></p>
                <p><strong>Tipo:</strong> <?php echo $selected_list['list_type'] === 'xtream_codes' ? '🔐 Xtream Codes' : '📄 URL Directa'; ?></p>
                <p><strong>URL:</strong> <?php echo htmlspecialchars(substr($selected_list['url'], 0, 80)) . '...'; ?></p>
                <p><strong>Elementos actuales:</strong> <?php echo number_format($selected_list['total_items']); ?></p>
                <p><strong>Último análisis:</strong> <?php echo $selected_list['last_scan'] ? date('d/m/Y H:i', strtotime($selected_list['last_scan'])) : 'Nunca'; ?></p>
                <p><strong>Estado:</strong> <?php echo $selected_list['is_active'] ? '✅ Activa' : '❌ Inactiva'; ?></p>
            </div>

            <form method="POST">
                <input type="hidden" name="list_id" value="<?php echo $selected_list['id']; ?>">
                <button type="submit" name="analyze" class="btn btn-primary" onclick="return confirm('¿Iniciar análisis de la lista? Esto puede tomar varios minutos.')">
                    🔍 Iniciar Análisis
                </button>
                <a href="m3u_manager.php" class="btn btn-secondary">❌ Cancelar</a>
            </form>
            <?php endif; ?>
        </div>

        <?php if (empty($all_lists)): ?>
        <div class="section">
            <h3>📭 No hay listas configuradas</h3>
            <p>Primero debes agregar listas M3U para poder analizarlas.</p>
            <a href="m3u_manager.php" class="btn btn-primary">➕ Agregar Lista M3U</a>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>
