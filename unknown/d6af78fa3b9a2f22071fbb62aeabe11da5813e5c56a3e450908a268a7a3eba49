<?php
// Versión simple del gestor M3U (compatible con tabla básica)
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();

// Verificar si es admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

$message = '';
$message_type = '';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Error de conexión: " . $e->getMessage());
}

// Procesar formularios
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['add_list'])) {
        // Agregar nueva lista (versión básica)
        $name = trim($_POST['name']);
        $url = trim($_POST['url']);
        $username = trim($_POST['username']) ?: null;
        $password = trim($_POST['password']) ?: null;
        
        if ($name && $url) {
            try {
                $stmt = $pdo->prepare("INSERT INTO m3u_lists (name, url, username, password) VALUES (?, ?, ?, ?)");
                $stmt->execute([$name, $url, $username, $password]);
                $message = "Lista M3U agregada exitosamente";
                $message_type = "success";
            } catch (PDOException $e) {
                $message = "Error al agregar lista: " . $e->getMessage();
                $message_type = "error";
            }
        } else {
            $message = "Nombre y URL son requeridos";
            $message_type = "error";
        }
    }
    
    if (isset($_POST['delete_list'])) {
        // Eliminar lista
        $list_id = $_POST['list_id'];
        
        try {
            $stmt = $pdo->prepare("DELETE FROM m3u_lists WHERE id = ?");
            $stmt->execute([$list_id]);
            $message = "Lista eliminada exitosamente";
            $message_type = "success";
        } catch (PDOException $e) {
            $message = "Error al eliminar lista: " . $e->getMessage();
            $message_type = "error";
        }
    }
}

// Obtener todas las listas
$stmt = $pdo->query("SELECT * FROM m3u_lists ORDER BY created_at DESC");
$lists = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📡 Gestor M3U Simple - RogsMediaTV</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #141414; color: white; }
        .container { max-width: 1000px; margin: 0 auto; }
        .success { color: #28a745; background: rgba(40, 167, 69, 0.2); padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: rgba(220, 53, 69, 0.2); padding: 10px; border-radius: 5px; margin: 10px 0; }
        .section { background: #2d2d2d; padding: 20px; border-radius: 10px; margin: 20px 0; border: 1px solid #404040; }
        .form-group { margin: 15px 0; }
        .form-group label { display: block; margin-bottom: 5px; color: #b0b0b0; }
        .form-input { width: 100%; padding: 10px; border: 1px solid #404040; border-radius: 5px; background: #1a1a1a; color: white; }
        .btn { padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        .btn-primary { background: #46d347; color: #1a1a1a; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-info { background: #17a2b8; color: white; }
        .list-card { background: #1a1a1a; padding: 15px; margin: 10px 0; border-radius: 8px; border: 1px solid #404040; }
        .list-title { color: #46d347; font-size: 1.2em; margin-bottom: 10px; }
        .back-link { color: #46d347; text-decoration: none; margin-bottom: 20px; display: inline-block; }
        .warning { background: rgba(255, 193, 7, 0.2); color: #ffc107; padding: 15px; border-radius: 8px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <a href="admin.php" class="back-link">← Volver al Panel Admin</a>
        
        <h1>📡 Gestor M3U Simple</h1>
        
        <div class="warning">
            <strong>⚠️ Versión Básica:</strong> Esta es una versión simplificada. 
            Para funciones avanzadas (Xtream Codes, carpetas automáticas), 
            ejecuta primero: <a href="update_m3u_tables.php" style="color: #ffc107;"><strong>update_m3u_tables.php</strong></a>
        </div>

        <?php if ($message): ?>
        <div class="<?php echo $message_type; ?>">
            <?php echo htmlspecialchars($message); ?>
        </div>
        <?php endif; ?>

        <!-- Formulario básico -->
        <div class="section">
            <h2>➕ Agregar Lista M3U</h2>
            <form method="POST">
                <div class="form-group">
                    <label for="name">Nombre de la Lista *</label>
                    <input type="text" id="name" name="name" class="form-input" required placeholder="Ej: Mi Lista IPTV">
                </div>
                <div class="form-group">
                    <label for="url">URL de la Lista M3U *</label>
                    <input type="url" id="url" name="url" class="form-input" required placeholder="http://ejemplo.com/lista.m3u">
                </div>
                <div class="form-group">
                    <label for="username">Usuario (opcional)</label>
                    <input type="text" id="username" name="username" class="form-input" placeholder="Usuario para autenticación">
                </div>
                <div class="form-group">
                    <label for="password">Contraseña (opcional)</label>
                    <input type="password" id="password" name="password" class="form-input" placeholder="Contraseña para autenticación">
                </div>
                <button type="submit" name="add_list" class="btn btn-primary">➕ Agregar Lista</button>
            </form>
        </div>

        <!-- Lista de listas -->
        <div class="section">
            <h2>📋 Listas Configuradas (<?php echo count($lists); ?>)</h2>
            
            <?php if (empty($lists)): ?>
            <p style="text-align: center; color: #666; padding: 40px;">
                📭 No hay listas configuradas aún
            </p>
            <?php else: ?>
            <?php foreach ($lists as $list): ?>
            <div class="list-card">
                <div class="list-title"><?php echo htmlspecialchars($list['name']); ?></div>
                <p><strong>URL:</strong> <?php echo htmlspecialchars(substr($list['url'], 0, 80)) . '...'; ?></p>
                <p><strong>Usuario:</strong> <?php echo $list['username'] ? htmlspecialchars($list['username']) : 'N/A'; ?></p>
                <p><strong>Creada:</strong> <?php echo date('d/m/Y H:i', strtotime($list['created_at'])); ?></p>
                
                <div style="margin-top: 15px;">
                    <a href="m3u_analyzer.php?list_id=<?php echo $list['id']; ?>" class="btn btn-info">🔍 Analizar</a>
                    
                    <form method="POST" style="display: inline;" onsubmit="return confirm('¿Eliminar esta lista?')">
                        <input type="hidden" name="list_id" value="<?php echo $list['id']; ?>">
                        <button type="submit" name="delete_list" class="btn btn-danger">🗑️ Eliminar</button>
                    </form>
                </div>
            </div>
            <?php endforeach; ?>
            <?php endif; ?>
        </div>

        <div class="section">
            <h2>🚀 Próximos Pasos</h2>
            <p>Para acceder a todas las funciones avanzadas:</p>
            <ol>
                <li><a href="update_m3u_tables.php" style="color: #46d347;">🔧 Actualizar tablas M3U</a></li>
                <li><a href="m3u_manager.php" style="color: #46d347;">📡 Gestor M3U completo</a></li>
                <li><a href="m3u_downloader.php" style="color: #46d347;">📥 Descargador de listas</a></li>
            </ol>
        </div>
    </div>
</body>
</html>
