<?php
session_start();
require_once 'config.php';

// Verificar si el usuario está logueado como admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

// Procesar respuesta de admin
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['send_message'])) {
    try {
        $session_id = (int)$_POST['session_id'];
        $message = clean_input($_POST['message']);
        $admin_id = 2; // ID del admin (ajustar según necesidad)
        
        if (!empty($message)) {
            // Insertar mensaje del admin
            $stmt = $pdo->prepare("INSERT INTO chat_messages (session_id, sender_id, message, is_admin) VALUES (?, ?, ?, 1)");
            $stmt->execute([$session_id, $admin_id, $message]);
            
            // Actualizar estado de la sesión a activa
            $stmt = $pdo->prepare("UPDATE chat_sessions SET status = 'active' WHERE id = ?");
            $stmt->execute([$session_id]);
            
            $success_message = "Mensaje enviado correctamente.";
        }
    } catch (Exception $e) {
        $error_message = "Error al enviar mensaje: " . $e->getMessage();
    }
}

// Procesar finalización de chat
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['end_chat'])) {
    try {
        $session_id = (int)$_POST['session_id'];
        
        $stmt = $pdo->prepare("UPDATE chat_sessions SET status = 'ended', ended_at = NOW() WHERE id = ?");
        $stmt->execute([$session_id]);
        
        $success_message = "Chat finalizado correctamente.";
    } catch (Exception $e) {
        $error_message = "Error al finalizar chat: " . $e->getMessage();
    }
}

// Obtener sesiones de chat
try {
    $stmt = $pdo->prepare("
        SELECT cs.*, u.username 
        FROM chat_sessions cs 
        LEFT JOIN users u ON cs.user_id = u.id 
        ORDER BY 
            CASE cs.status 
                WHEN 'waiting' THEN 1 
                WHEN 'active' THEN 2 
                WHEN 'ended' THEN 3 
            END,
            cs.started_at DESC
    ");
    $stmt->execute();
    $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $sessions = [];
    $error_message = "Error al cargar sesiones: " . $e->getMessage();
}

// Estadísticas
try {
    $stmt = $pdo->query("SELECT COUNT(*) FROM chat_sessions WHERE status = 'waiting'");
    $waiting_count = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM chat_sessions WHERE status = 'active'");
    $active_count = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM chat_sessions WHERE DATE(started_at) = CURDATE()");
    $today_count = $stmt->fetchColumn();
} catch (Exception $e) {
    $waiting_count = $active_count = $today_count = 0;
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>💬 Gestión de Chat en Vivo - Admin RGS</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e293b;
            --dark-bg: #0f172a;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --accent-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --success-color: #10b981;
            --border-color: #334155;
            --gradient-bg: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            --border-radius: 12px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gradient-bg);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
        }

        .header {
            background: var(--secondary-color);
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--accent-color);
            text-decoration: none;
        }

        .nav-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .nav-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            background: transparent;
            color: var(--text-secondary);
            text-decoration: none;
            border-radius: var(--border-radius);
            transition: var(--transition);
            font-weight: 500;
        }

        .nav-btn:hover {
            background: rgba(255,255,255,0.1);
            color: var(--text-primary);
        }

        .nav-btn.back-btn {
            background: var(--primary-color);
            color: white;
        }

        .main-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem 1.5rem;
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 2rem;
        }

        .sidebar {
            background: var(--secondary-color);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            height: fit-content;
            position: sticky;
            top: 100px;
        }

        .sidebar-header {
            background: var(--dark-bg);
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
            border-radius: var(--border-radius) var(--border-radius) 0 0;
        }

        .sidebar-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 1rem;
            padding: 1.5rem;
        }

        .stat-item {
            text-align: center;
            padding: 1rem;
            background: rgba(255,255,255,0.05);
            border-radius: var(--border-radius);
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin-top: 0.25rem;
        }

        .sessions-list {
            padding: 1rem;
        }

        .session-item {
            padding: 1rem;
            background: rgba(255,255,255,0.05);
            border-radius: var(--border-radius);
            margin-bottom: 0.5rem;
            cursor: pointer;
            transition: var(--transition);
            border-left: 4px solid var(--border-color);
        }

        .session-item.waiting {
            border-left-color: var(--warning-color);
        }

        .session-item.active {
            border-left-color: var(--success-color);
        }

        .session-item.ended {
            border-left-color: var(--border-color);
            opacity: 0.6;
        }

        .session-item:hover {
            background: rgba(255,255,255,0.1);
        }

        .session-item.selected {
            background: var(--primary-color);
        }

        .session-user {
            font-weight: 600;
            color: var(--text-primary);
        }

        .session-status {
            font-size: 0.8rem;
            color: var(--text-secondary);
        }

        .chat-area {
            background: var(--secondary-color);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
            height: 600px;
        }

        .chat-header {
            background: var(--dark-bg);
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
            border-radius: var(--border-radius) var(--border-radius) 0 0;
        }

        .chat-messages {
            flex: 1;
            padding: 1rem;
            overflow-y: auto;
            background: var(--dark-bg);
        }

        .message {
            margin-bottom: 1rem;
            padding: 0.75rem;
            border-radius: var(--border-radius);
            max-width: 80%;
        }

        .message.user {
            background: rgba(37, 99, 235, 0.2);
            margin-left: auto;
            text-align: right;
        }

        .message.admin {
            background: rgba(16, 185, 129, 0.2);
            margin-right: auto;
        }

        .message-sender {
            font-size: 0.8rem;
            color: var(--text-secondary);
            margin-bottom: 0.25rem;
        }

        .message-text {
            color: var(--text-primary);
        }

        .message-time {
            font-size: 0.7rem;
            color: var(--text-secondary);
            margin-top: 0.25rem;
        }

        .chat-input {
            padding: 1rem;
            border-top: 1px solid var(--border-color);
            background: var(--secondary-color);
            border-radius: 0 0 var(--border-radius) var(--border-radius);
        }

        .input-form {
            display: flex;
            gap: 0.5rem;
        }

        .message-input {
            flex: 1;
            padding: 0.75rem;
            background: var(--dark-bg);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            resize: none;
        }

        .send-btn {
            padding: 0.75rem 1.5rem;
            background: var(--accent-color);
            color: white;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: var(--transition);
        }

        .send-btn:hover {
            background: var(--success-color);
        }

        .end-chat-btn {
            background: var(--error-color);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: var(--border-radius);
            cursor: pointer;
            margin-left: 0.5rem;
        }

        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: var(--text-secondary);
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-container">
            <a href="admin_chat_real.php" class="logo">
                <i class="fas fa-comments"></i>
                <span>Chat en Vivo</span>
            </a>
            
            <div class="nav-buttons">
                <a href="admin2.php" class="nav-btn back-btn">
                    <i class="fas fa-arrow-left"></i>
                    <span>Volver al Panel</span>
                </a>
            </div>
        </div>
    </header>

    <main class="main-content">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2 class="sidebar-title">
                    <i class="fas fa-chart-bar"></i>
                    Estadísticas
                </h2>
            </div>
            
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number"><?php echo $waiting_count; ?></div>
                    <div class="stat-label">En Espera</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><?php echo $active_count; ?></div>
                    <div class="stat-label">Activos</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><?php echo $today_count; ?></div>
                    <div class="stat-label">Hoy</div>
                </div>
            </div>
            
            <div class="sidebar-header">
                <h2 class="sidebar-title">
                    <i class="fas fa-list"></i>
                    Sesiones
                </h2>
            </div>
            
            <div class="sessions-list">
                <?php if (empty($sessions)): ?>
                <div style="text-align: center; padding: 2rem; color: var(--text-secondary);">
                    <i class="fas fa-comments"></i><br>
                    No hay sesiones
                </div>
                <?php else: ?>
                    <?php foreach ($sessions as $session): ?>
                    <div class="session-item <?php echo $session['status']; ?>" onclick="selectSession(<?php echo $session['id']; ?>)">
                        <div class="session-user"><?php echo htmlspecialchars($session['username'] ?? 'Usuario'); ?></div>
                        <div class="session-status">
                            <?php 
                            $status_labels = [
                                'waiting' => 'En espera',
                                'active' => 'Activo',
                                'ended' => 'Finalizado'
                            ];
                            echo $status_labels[$session['status']] ?? $session['status'];
                            ?>
                            • <?php echo date('H:i', strtotime($session['started_at'])); ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>

        <!-- Chat Area -->
        <div class="chat-area">
            <div class="chat-header">
                <h2 style="color: var(--text-primary);">
                    <i class="fas fa-comment-dots"></i>
                    Chat en Vivo
                </h2>
                <p style="color: var(--text-secondary); margin-top: 0.5rem;">
                    Selecciona una sesión para comenzar a chatear
                </p>
            </div>
            
            <div class="chat-messages" id="chatMessages">
                <div class="empty-state">
                    <i class="fas fa-comments"></i>
                    <h3>Selecciona una sesión de chat</h3>
                    <p>Elige una sesión de la lista para ver los mensajes</p>
                </div>
            </div>
            
            <div class="chat-input" id="chatInput" style="display: none;">
                <form method="POST" class="input-form">
                    <input type="hidden" name="session_id" id="sessionId">
                    <textarea name="message" class="message-input" placeholder="Escribe tu respuesta..." required></textarea>
                    <button type="submit" name="send_message" class="send-btn">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                    <button type="submit" name="end_chat" class="end-chat-btn" onclick="return confirm('¿Finalizar este chat?')">
                        <i class="fas fa-times"></i>
                    </button>
                </form>
            </div>
        </div>
    </main>

    <script>
        let currentSessionId = null;
        let messageInterval = null;

        function selectSession(sessionId) {
            currentSessionId = sessionId;
            document.getElementById('sessionId').value = sessionId;

            // Marcar sesión como seleccionada
            document.querySelectorAll('.session-item').forEach(item => {
                item.classList.remove('selected');
            });
            event.target.closest('.session-item').classList.add('selected');

            // Cargar mensajes
            loadMessages(sessionId);

            // Mostrar input de chat
            document.getElementById('chatInput').style.display = 'block';

            // Iniciar polling de mensajes
            startMessagePolling();
        }

        function loadMessages(sessionId) {
            if (!sessionId) return;

            fetch(`api_chat.php?action=get_messages&session_id=${sessionId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayMessages(data.messages);
                    } else {
                        console.error('Error loading messages:', data.error);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    document.getElementById('chatMessages').innerHTML = `
                        <div class="empty-state">
                            <i class="fas fa-exclamation-triangle"></i>
                            <h3>Error cargando mensajes</h3>
                        </div>
                    `;
                });
        }

        function displayMessages(messages) {
            const container = document.getElementById('chatMessages');

            if (messages.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-comments"></i>
                        <h3>No hay mensajes aún</h3>
                    </div>
                `;
                return;
            }

            container.innerHTML = '';
            messages.forEach(msg => {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${msg.is_admin == 1 ? 'admin' : 'user'}`;

                const time = new Date(msg.sent_at).toLocaleTimeString('es-ES', {
                    hour: '2-digit',
                    minute: '2-digit'
                });

                messageDiv.innerHTML = `
                    <div class="message-sender">${msg.is_admin == 1 ? 'Agente' : (msg.username || 'Usuario')}</div>
                    <div class="message-text">${msg.message.replace(/\n/g, '<br>')}</div>
                    <div class="message-time">${time}</div>
                `;

                container.appendChild(messageDiv);
            });

            // Scroll al final
            container.scrollTop = container.scrollHeight;
        }

        function sendMessage() {
            const messageInput = document.querySelector('textarea[name="message"]');
            const message = messageInput.value.trim();

            if (!message || !currentSessionId) return;

            const formData = new FormData();
            formData.append('session_id', currentSessionId);
            formData.append('message', message);

            fetch('api_chat.php?action=send_message', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    messageInput.value = '';
                    loadMessages(currentSessionId);
                } else {
                    alert('Error al enviar mensaje: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error de conexión');
            });
        }

        function startMessagePolling() {
            if (messageInterval) clearInterval(messageInterval);
            messageInterval = setInterval(() => {
                if (currentSessionId) {
                    loadMessages(currentSessionId);
                }
            }, 3000);
        }

        function stopMessagePolling() {
            if (messageInterval) {
                clearInterval(messageInterval);
                messageInterval = null;
            }
        }

        // Enviar mensaje con Enter
        document.addEventListener('DOMContentLoaded', function() {
            const messageInput = document.querySelector('textarea[name="message"]');
            if (messageInput) {
                messageInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        sendMessage();
                    }
                });
            }

            // Interceptar envío del formulario
            const form = document.querySelector('.input-form');
            if (form) {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();
                    sendMessage();
                });
            }
        });

        // Auto-refresh optimizado - solo actualizar datos, no recargar página
        let refreshInterval;

        function startAutoRefresh() {
            refreshInterval = setInterval(() => {
                updateSessionsList();
                if (currentSessionId) {
                    loadMessages();
                }
            }, 5000); // Cada 5 segundos en lugar de 15
        }

        function stopAutoRefresh() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
        }

        function updateSessionsList() {
            fetch('api_chat.php?action=get_sessions')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateSessionsDisplay(data.sessions);
                    }
                })
                .catch(error => console.error('Error updating sessions:', error));
        }

        function updateSessionsDisplay(sessions) {
            // Actualizar la lista de sesiones sin recargar la página
            const sessionsList = document.querySelector('.sessions-list');
            if (sessionsList && sessions) {
                // Aquí actualizarías la lista de sesiones
                // Por ahora mantenemos el comportamiento existente
            }
        }

        // Iniciar auto-refresh al cargar la página
        startAutoRefresh();

        // Limpiar intervals al cerrar
        window.addEventListener('beforeunload', function() {
            stopMessagePolling();
            stopAutoRefresh();
        });
    </script>
</body>
</html>
