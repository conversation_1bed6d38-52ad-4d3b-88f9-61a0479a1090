<?php
session_start();
require_once 'config.php';

// Verificar si el usuario está logueado como admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

// Procesar acciones
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['approve_request'])) {
        $request_id = (int)$_POST['request_id'];
        $admin_notes = clean_input($_POST['admin_notes'] ?? '');
        
        try {
            $stmt = $pdo->prepare("UPDATE channel_requests SET status = 'approved', admin_notes = ?, updated_at = NOW() WHERE id = ?");
            $stmt->execute([$admin_notes, $request_id]);
            $success_message = "Solicitud aprobada correctamente.";
        } catch (Exception $e) {
            $error_message = "Error al aprobar solicitud: " . $e->getMessage();
        }
    }
    
    if (isset($_POST['reject_request'])) {
        $request_id = (int)$_POST['request_id'];
        $admin_notes = clean_input($_POST['admin_notes'] ?? '');
        
        try {
            $stmt = $pdo->prepare("UPDATE channel_requests SET status = 'rejected', admin_notes = ?, updated_at = NOW() WHERE id = ?");
            $stmt->execute([$admin_notes, $request_id]);
            $success_message = "Solicitud rechazada.";
        } catch (Exception $e) {
            $error_message = "Error al rechazar solicitud: " . $e->getMessage();
        }
    }
}

// Obtener todas las solicitudes
try {
    $stmt = $pdo->prepare("
        SELECT cr.*, u.username 
        FROM channel_requests cr 
        LEFT JOIN users u ON cr.user_id = u.id 
        ORDER BY 
            CASE cr.status 
                WHEN 'pending' THEN 1 
                WHEN 'approved' THEN 2 
                WHEN 'rejected' THEN 3 
            END,
            cr.created_at DESC
    ");
    $stmt->execute();
    $requests = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $requests = [];
    $error_message = "Error al cargar solicitudes: " . $e->getMessage();
}

// Estadísticas
try {
    $stmt = $pdo->query("SELECT COUNT(*) FROM channel_requests WHERE status = 'pending'");
    $pending_count = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM channel_requests WHERE status = 'approved'");
    $approved_count = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM channel_requests WHERE status = 'rejected'");
    $rejected_count = $stmt->fetchColumn();
} catch (Exception $e) {
    $pending_count = $approved_count = $rejected_count = 0;
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📺 Gestión de Solicitudes de Canales - Admin RGS</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e293b;
            --dark-bg: #0f172a;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --accent-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --success-color: #10b981;
            --border-color: #334155;
            --gradient-bg: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            --border-radius: 12px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gradient-bg);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
        }

        .header {
            background: var(--secondary-color);
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
            text-decoration: none;
        }

        .nav-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .nav-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            background: transparent;
            color: var(--text-secondary);
            text-decoration: none;
            border-radius: var(--border-radius);
            transition: var(--transition);
            font-weight: 500;
        }

        .nav-btn:hover {
            background: rgba(255,255,255,0.1);
            color: var(--text-primary);
        }

        .nav-btn.back-btn {
            background: var(--primary-color);
            color: white;
        }

        .main-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem 1.5rem;
        }

        .page-header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .page-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 3rem;
        }

        .stat-card {
            background: var(--secondary-color);
            padding: 1.5rem;
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
        }

        .stat-label {
            color: var(--text-secondary);
            margin-top: 0.5rem;
        }

        .requests-section {
            background: var(--secondary-color);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            overflow: hidden;
        }

        .section-header {
            background: var(--dark-bg);
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .requests-list {
            padding: 1.5rem;
        }

        .request-item {
            background: rgba(255,255,255,0.05);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            margin-bottom: 1rem;
            border-left: 4px solid var(--border-color);
        }

        .request-item.status-pending {
            border-left-color: var(--warning-color);
        }

        .request-item.status-approved {
            border-left-color: var(--success-color);
        }

        .request-item.status-rejected {
            border-left-color: var(--error-color);
        }

        .request-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .request-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .status-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-pending { 
            background: rgba(245, 158, 11, 0.2); 
            color: #f59e0b; 
        }

        .status-approved { 
            background: rgba(16, 185, 129, 0.2); 
            color: #10b981; 
        }

        .status-rejected { 
            background: rgba(239, 68, 68, 0.2); 
            color: #ef4444; 
        }

        .request-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .detail-item {
            text-align: center;
            padding: 0.5rem;
            background: rgba(255,255,255,0.05);
            border-radius: var(--border-radius);
        }

        .detail-label {
            color: var(--text-secondary);
            font-size: 0.8rem;
            margin-bottom: 0.25rem;
        }

        .detail-value {
            color: var(--text-primary);
            font-weight: 500;
        }

        .admin-actions {
            display: flex;
            gap: 1rem;
            margin-top: 1rem;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            transition: var(--transition);
        }

        .btn-approve {
            background: var(--success-color);
            color: white;
        }

        .btn-reject {
            background: var(--error-color);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            opacity: 0.9;
        }

        .admin-notes {
            width: 100%;
            padding: 0.5rem;
            background: var(--dark-bg);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            margin-bottom: 0.5rem;
            resize: vertical;
            min-height: 60px;
        }

        .message {
            padding: 1rem;
            border-radius: var(--border-radius);
            margin-bottom: 1rem;
        }

        .success-message {
            background: rgba(16, 185, 129, 0.2);
            color: var(--success-color);
            border: 1px solid rgba(16, 185, 129, 0.3);
        }

        .error-message {
            background: rgba(239, 68, 68, 0.2);
            color: var(--error-color);
            border: 1px solid rgba(239, 68, 68, 0.3);
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-container">
            <a href="admin_channels_real.php" class="logo">
                <i class="fas fa-tv"></i>
                <span>Gestión de Canales</span>
            </a>
            
            <div class="nav-buttons">
                <a href="admin2.php" class="nav-btn back-btn">
                    <i class="fas fa-arrow-left"></i>
                    <span>Volver al Panel</span>
                </a>
            </div>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-tv" style="color: var(--primary-color);"></i>
                Gestión de Solicitudes de Canales
            </h1>
        </div>

        <?php if (isset($success_message)): ?>
        <div class="success-message message">
            <i class="fas fa-check-circle"></i>
            <?php echo htmlspecialchars($success_message); ?>
        </div>
        <?php endif; ?>

        <?php if (isset($error_message)): ?>
        <div class="error-message message">
            <i class="fas fa-exclamation-triangle"></i>
            <?php echo htmlspecialchars($error_message); ?>
        </div>
        <?php endif; ?>

        <!-- Estadísticas -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?php echo $pending_count; ?></div>
                <div class="stat-label">Pendientes</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $approved_count; ?></div>
                <div class="stat-label">Aprobadas</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $rejected_count; ?></div>
                <div class="stat-label">Rechazadas</div>
            </div>
        </div>

        <!-- Lista de Solicitudes -->
        <div class="requests-section">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-list"></i>
                    Solicitudes de Canales (<?php echo count($requests); ?>)
                </h2>
            </div>
            
            <div class="requests-list">
                <?php if (empty($requests)): ?>
                <div style="text-align: center; padding: 3rem; color: var(--text-secondary);">
                    <i class="fas fa-tv" style="font-size: 4rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                    <h3>No hay solicitudes de canales</h3>
                    <p>Las solicitudes aparecerán aquí cuando los usuarios las envíen</p>
                </div>
                <?php else: ?>
                    <?php foreach ($requests as $request): ?>
                    <div class="request-item status-<?php echo $request['status']; ?>">
                        <div class="request-header">
                            <div>
                                <div class="request-title">
                                    <?php echo htmlspecialchars($request['channel_name']); ?>
                                </div>
                                <div style="color: var(--text-secondary); font-size: 0.9rem;">
                                    Solicitado por: <?php echo htmlspecialchars($request['username'] ?? 'Usuario desconocido'); ?>
                                    • <?php echo date('d/m/Y H:i', strtotime($request['created_at'])); ?>
                                </div>
                            </div>
                            <span class="status-badge status-<?php echo $request['status']; ?>">
                                <?php 
                                $status_labels = [
                                    'pending' => 'Pendiente',
                                    'approved' => 'Aprobado',
                                    'rejected' => 'Rechazado'
                                ];
                                echo $status_labels[$request['status']] ?? $request['status'];
                                ?>
                            </span>
                        </div>
                        
                        <div class="request-details">
                            <div class="detail-item">
                                <div class="detail-label">País</div>
                                <div class="detail-value"><?php echo htmlspecialchars($request['country']); ?></div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Idioma</div>
                                <div class="detail-value"><?php echo ucfirst($request['language']); ?></div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Categoría</div>
                                <div class="detail-value"><?php echo ucfirst($request['category']); ?></div>
                            </div>
                            <?php if ($request['channel_url']): ?>
                            <div class="detail-item">
                                <div class="detail-label">URL</div>
                                <div class="detail-value">
                                    <a href="<?php echo htmlspecialchars($request['channel_url']); ?>" target="_blank" style="color: var(--primary-color);">
                                        <i class="fas fa-external-link-alt"></i> Ver
                                    </a>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                        
                        <?php if ($request['description']): ?>
                        <div style="margin: 1rem 0; padding: 1rem; background: rgba(255,255,255,0.05); border-radius: var(--border-radius);">
                            <strong>Descripción:</strong><br>
                            <?php echo nl2br(htmlspecialchars($request['description'])); ?>
                        </div>
                        <?php endif; ?>
                        
                        <?php if ($request['admin_notes']): ?>
                        <div style="margin: 1rem 0; padding: 1rem; background: rgba(37, 99, 235, 0.1); border-radius: var(--border-radius);">
                            <strong>Notas del administrador:</strong><br>
                            <?php echo nl2br(htmlspecialchars($request['admin_notes'])); ?>
                        </div>
                        <?php endif; ?>
                        
                        <?php if ($request['status'] === 'pending'): ?>
                        <form method="POST" style="margin-top: 1rem;">
                            <input type="hidden" name="request_id" value="<?php echo $request['id']; ?>">
                            <textarea name="admin_notes" class="admin-notes" placeholder="Notas del administrador (opcional)"></textarea>
                            <div class="admin-actions">
                                <button type="submit" name="approve_request" class="btn btn-approve">
                                    <i class="fas fa-check"></i> Aprobar
                                </button>
                                <button type="submit" name="reject_request" class="btn btn-reject">
                                    <i class="fas fa-times"></i> Rechazar
                                </button>
                            </div>
                        </form>
                        <?php endif; ?>
                    </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </main>

    <script>
        // Auto-refresh cada 30 segundos para ver nuevas solicitudes
        setTimeout(() => {
            location.reload();
        }, 30000);

        // Confirmación antes de aprobar/rechazar
        document.querySelectorAll('.btn-approve, .btn-reject').forEach(btn => {
            btn.addEventListener('click', function(e) {
                const action = this.classList.contains('btn-approve') ? 'aprobar' : 'rechazar';
                if (!confirm(`¿Estás seguro de que quieres ${action} esta solicitud?`)) {
                    e.preventDefault();
                }
            });
        });
    </script>
</body>
</html>
