<?php
// Descargador de listas M3U
error_reporting(E_ALL);
ini_set('display_errors', 1);
set_time_limit(300); // 5 minutos

session_start();

// Verificar si es admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Error de conexión: " . $e->getMessage());
}

// Función para descargar lista M3U
function downloadM3uList($list) {
    $context = null;
    
    // Configurar autenticación si es necesaria
    if ($list['username'] && $list['password'] && $list['list_type'] === 'direct_m3u') {
        $auth = base64_encode($list['username'] . ':' . $list['password']);
        $context = stream_context_create([
            'http' => [
                'header' => "Authorization: Basic $auth\r\n",
                'timeout' => 60
            ]
        ]);
    } else {
        $context = stream_context_create([
            'http' => [
                'timeout' => 60
            ]
        ]);
    }
    
    $content = @file_get_contents($list['url'], false, $context);
    
    if ($content === false) {
        throw new Exception("No se pudo descargar la lista desde: " . $list['url']);
    }
    
    return $content;
}

// Función para guardar archivo M3U
function saveM3uFile($content, $folderName, $listName) {
    $baseDir = 'uploads/m3u_files/' . $folderName;
    
    // Crear directorio si no existe
    if (!file_exists($baseDir)) {
        mkdir($baseDir, 0755, true);
    }
    
    // Nombre del archivo
    $fileName = preg_replace('/[^a-zA-Z0-9_-]/', '_', $listName) . '_' . date('Y-m-d_H-i-s') . '.m3u';
    $filePath = $baseDir . '/' . $fileName;
    
    // Guardar archivo
    $bytes = file_put_contents($filePath, $content);
    
    if ($bytes === false) {
        throw new Exception("No se pudo guardar el archivo M3U");
    }
    
    return [
        'file_path' => $filePath,
        'file_name' => $fileName,
        'file_size' => $bytes
    ];
}

$message = '';
$message_type = '';
$download_result = null;

// Procesar descarga
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['download'])) {
    $list_id = $_POST['list_id'];
    
    try {
        // Obtener datos de la lista
        $stmt = $pdo->prepare("SELECT * FROM m3u_lists WHERE id = ?");
        $stmt->execute([$list_id]);
        $list = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$list) {
            throw new Exception("Lista no encontrada");
        }
        
        // Descargar contenido
        $content = downloadM3uList($list);
        
        // Guardar archivo
        $fileInfo = saveM3uFile($content, $list['folder_name'], $list['name']);
        
        // Actualizar base de datos
        $stmt = $pdo->prepare("
            UPDATE m3u_lists 
            SET local_file_path = ?, last_download = NOW(), file_size = ?, last_updated = NOW()
            WHERE id = ?
        ");
        $stmt->execute([$fileInfo['file_path'], $fileInfo['file_size'], $list_id]);
        
        $download_result = [
            'list_name' => $list['name'],
            'file_name' => $fileInfo['file_name'],
            'file_size' => $fileInfo['file_size'],
            'file_path' => $fileInfo['file_path'],
            'lines_count' => substr_count($content, "\n"),
            'items_count' => substr_count($content, '#EXTINF:')
        ];
        
        $message = "Lista descargada exitosamente";
        $message_type = "success";
        
    } catch (Exception $e) {
        $message = "Error durante la descarga: " . $e->getMessage();
        $message_type = "error";
    }
}

// Obtener todas las listas activas
$stmt = $pdo->query("
    SELECT *, 
           CASE 
               WHEN local_file_path IS NOT NULL THEN 1 
               ELSE 0 
           END as has_local_file
    FROM m3u_lists 
    WHERE is_active = 1 
    ORDER BY name
");
$lists = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📥 Descargador M3U - RGS TOOL</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #1a1a1a;
            --secondary-color: #2d2d2d;
            --accent-color: #46d347;
            --text-primary: #ffffff;
            --text-secondary: #b0b0b0;
            --border-color: #404040;
            --success-color: #28a745;
            --error-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--primary-color);
            color: var(--text-primary);
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            background: linear-gradient(135deg, var(--accent-color), #28a745);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .message {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            text-align: center;
        }

        .message.success {
            background: rgba(40, 167, 69, 0.2);
            border: 1px solid var(--success-color);
            color: var(--success-color);
        }

        .message.error {
            background: rgba(220, 53, 69, 0.2);
            border: 1px solid var(--error-color);
            color: var(--error-color);
        }

        .section {
            background: var(--secondary-color);
            padding: 2rem;
            border-radius: 12px;
            margin-bottom: 2rem;
            border: 1px solid var(--border-color);
        }

        .section h2 {
            margin-bottom: 1.5rem;
            color: var(--accent-color);
        }

        .lists-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 1.5rem;
        }

        .list-card {
            background: var(--primary-color);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 1.5rem;
            transition: all 0.3s ease;
        }

        .list-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        .list-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--accent-color);
            margin-bottom: 1rem;
        }

        .list-info {
            margin-bottom: 1.5rem;
        }

        .list-info p {
            margin-bottom: 0.5rem;
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
        }

        .btn-primary {
            background: var(--accent-color);
            color: var(--primary-color);
        }

        .btn-primary:hover {
            background: #3bc73c;
            transform: translateY(-2px);
        }

        .btn-info {
            background: var(--info-color);
            color: white;
        }

        .btn-secondary {
            background: var(--border-color);
            color: var(--text-primary);
        }

        .back-link {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--accent-color);
            text-decoration: none;
            margin-bottom: 2rem;
            font-weight: 500;
        }

        .back-link:hover {
            color: #3bc73c;
        }

        .result-card {
            background: rgba(40, 167, 69, 0.1);
            border: 1px solid var(--success-color);
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .result-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .result-item {
            text-align: center;
        }

        .result-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--success-color);
        }

        .result-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-downloaded {
            background: rgba(40, 167, 69, 0.2);
            color: var(--success-color);
        }

        .status-not-downloaded {
            background: rgba(255, 193, 7, 0.2);
            color: var(--warning-color);
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="m3u_manager.php" class="back-link">
            <i class="fas fa-arrow-left"></i>
            Volver al Gestor M3U
        </a>

        <div class="header">
            <h1><i class="fas fa-download"></i> Descargador de Listas M3U</h1>
            <p>Descarga y almacena tus listas IPTV localmente</p>
        </div>

        <?php if ($message): ?>
        <div class="message <?php echo $message_type; ?>">
            <?php echo htmlspecialchars($message); ?>
        </div>
        <?php endif; ?>

        <?php if ($download_result): ?>
        <div class="result-card">
            <h3 style="color: var(--success-color); margin-bottom: 1rem;">
                <i class="fas fa-check-circle"></i>
                Descarga Completada: <?php echo htmlspecialchars($download_result['list_name']); ?>
            </h3>
            
            <div class="result-grid">
                <div class="result-item">
                    <div class="result-number"><?php echo number_format($download_result['file_size']); ?></div>
                    <div class="result-label">Bytes</div>
                </div>
                <div class="result-item">
                    <div class="result-number"><?php echo $download_result['lines_count']; ?></div>
                    <div class="result-label">Líneas</div>
                </div>
                <div class="result-item">
                    <div class="result-number"><?php echo $download_result['items_count']; ?></div>
                    <div class="result-label">Elementos</div>
                </div>
            </div>
            
            <p style="margin-top: 1rem; color: var(--text-secondary);">
                <i class="fas fa-file"></i>
                <strong>Archivo:</strong> <?php echo htmlspecialchars($download_result['file_name']); ?>
            </p>
        </div>
        <?php endif; ?>

        <div class="section">
            <h2><i class="fas fa-list"></i> Listas Disponibles para Descarga</h2>
            
            <?php if (empty($lists)): ?>
            <p style="text-align: center; color: var(--text-secondary); padding: 2rem;">
                <i class="fas fa-inbox" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i><br>
                No hay listas activas configuradas
            </p>
            <?php else: ?>
            <div class="lists-grid">
                <?php foreach ($lists as $list): ?>
                <div class="list-card">
                    <div class="list-title">
                        <?php echo htmlspecialchars($list['name']); ?>
                        <span class="status-badge <?php echo $list['has_local_file'] ? 'status-downloaded' : 'status-not-downloaded'; ?>">
                            <?php echo $list['has_local_file'] ? 'Descargada' : 'Sin descargar'; ?>
                        </span>
                    </div>
                    
                    <div class="list-info">
                        <p><i class="fas fa-tag"></i> <strong>Tipo:</strong> 
                            <?php echo $list['list_type'] === 'xtream_codes' ? '🔐 Xtream Codes' : '📄 URL Directa'; ?>
                        </p>
                        <p><i class="fas fa-folder"></i> <strong>Carpeta:</strong> <?php echo htmlspecialchars($list['folder_name'] ?? 'N/A'); ?></p>
                        <?php if ($list['last_download']): ?>
                        <p><i class="fas fa-clock"></i> <strong>Última descarga:</strong> <?php echo date('d/m/Y H:i', strtotime($list['last_download'])); ?></p>
                        <?php endif; ?>
                        <?php if ($list['file_size'] > 0): ?>
                        <p><i class="fas fa-hdd"></i> <strong>Tamaño:</strong> <?php echo number_format($list['file_size']); ?> bytes</p>
                        <?php endif; ?>
                    </div>
                    
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="list_id" value="<?php echo $list['id']; ?>">
                        <button type="submit" name="download" class="btn btn-primary">
                            <i class="fas fa-download"></i>
                            <?php echo $list['has_local_file'] ? 'Re-descargar' : 'Descargar'; ?>
                        </button>
                    </form>
                    
                    <a href="m3u_analyzer.php?list_id=<?php echo $list['id']; ?>" class="btn btn-info">
                        <i class="fas fa-search"></i>
                        Analizar
                    </a>
                </div>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
