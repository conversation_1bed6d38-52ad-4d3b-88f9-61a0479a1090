<?php
/**
 * Probar endpoint FCM
 */

header('Content-Type: application/json; charset=utf-8');

// Datos de prueba
$test_data = [
    'user_id' => 'Casa122',
    'fcm_token' => 'test_token_123456789',
    'device_type' => 'android',
    'app_version' => '1.0.0',
    'timestamp' => time()
];

// Hacer petición POST al endpoint FCM
$url = 'https://rogsmediatv.xyz/series/api/fcm/token';
$json_data = json_encode($test_data);

$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => [
            'Content-Type: application/json',
            'Content-Length: ' . strlen($json_data)
        ],
        'content' => $json_data
    ]
]);

$response = file_get_contents($url, false, $context);
$http_code = $http_response_header[0];

echo json_encode([
    'test_data' => $test_data,
    'response' => json_decode($response, true),
    'http_code' => $http_code,
    'timestamp' => date('Y-m-d H:i:s')
], JSON_PRETTY_PRINT);
?>
