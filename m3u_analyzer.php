<?php
// Analizador de contenido M3U - Versión mejorada
error_reporting(E_ALL);
ini_set('display_errors', 1);
set_time_limit(300); // 5 minutos para análisis

session_start();

// Verificar si es admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Error de conexión: " . $e->getMessage());
}

$message = '';
$message_type = '';
$analysis_results = null;
$debug_info = [];

// Función para log de debug
function debugLog($message) {
    global $debug_info;
    $debug_info[] = date('H:i:s') . " - " . $message;
    echo "<script>console.log('" . addslashes($message) . "');</script>";
    flush();
    ob_flush();
}

// Función simplificada para analizar M3U
function analyzeM3uSimple($url, $username = null, $password = null) {
    debugLog("Iniciando descarga de: $url");

    $context = stream_context_create([
        'http' => [
            'timeout' => 30,
            'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        ]
    ]);

    if ($username && $password) {
        debugLog("Usando autenticación básica");
        $auth = base64_encode("$username:$password");
        $context = stream_context_create([
            'http' => [
                'header' => "Authorization: Basic $auth\r\n",
                'timeout' => 30,
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            ]
        ]);
    }

    $content = @file_get_contents($url, false, $context);

    if ($content === false) {
        $error = error_get_last();
        debugLog("Error descargando: " . ($error['message'] ?? 'Error desconocido'));
        throw new Exception("No se pudo descargar la lista M3U. Error: " . ($error['message'] ?? 'Desconocido'));
    }

    debugLog("Descarga exitosa. Tamaño: " . strlen($content) . " bytes");

    if (strlen($content) < 10) {
        throw new Exception("El archivo descargado está vacío o es muy pequeño");
    }

    // Verificar que sea un archivo M3U válido
    if (strpos($content, '#EXTM3U') === false && strpos($content, '#EXTINF') === false) {
        debugLog("Contenido no parece ser M3U válido. Primeras 200 chars: " . substr($content, 0, 200));
        throw new Exception("El archivo no parece ser un M3U válido");
    }

    debugLog("Archivo M3U válido detectado");

    $lines = explode("\n", $content);
    $items = [];
    $current_item = null;
    $processed = 0;

    debugLog("Procesando " . count($lines) . " líneas");

    foreach ($lines as $line_num => $line) {
        $line = trim($line);

        if (strpos($line, '#EXTINF:') === 0) {
            $current_item = [];

            // Extraer título básico
            if (preg_match('/#EXTINF:[^,]*,(.*)/', $line, $matches)) {
                $current_item['title'] = trim($matches[1]);
            } else {
                $current_item['title'] = 'Sin título';
            }

            // Extraer duración
            if (preg_match('/#EXTINF:([^,]+)/', $line, $matches)) {
                $current_item['duration'] = (float)$matches[1];
            }

        } elseif ($line && !empty($current_item) && strpos($line, '#') !== 0) {
            // Esta es la URL del stream
            $current_item['url'] = $line;

            if (!empty($current_item['title'])) {
                $title = $current_item['title'];
                $clean_title = strtolower(preg_replace('/[^\w\s]/', '', $title));

                // Detectar tipo básico
                $media_type = 'unknown';
                if (preg_match('/s\d+e\d+|season|temporada|\d+x\d+/i', $title)) {
                    $media_type = 'tv';
                } elseif (preg_match('/\(\d{4}\)|\d{4}|movie|film|película/i', $title)) {
                    $media_type = 'movie';
                }

                // Extraer año
                $year = null;
                if (preg_match('/\((\d{4})\)/', $title, $matches)) {
                    $year = (int)$matches[1];
                } elseif (preg_match('/(\d{4})/', $title, $matches)) {
                    $test_year = (int)$matches[1];
                    if ($test_year >= 1900 && $test_year <= date('Y') + 2) {
                        $year = $test_year;
                    }
                }

                $items[] = [
                    'title' => $title,
                    'clean_title' => $clean_title,
                    'url' => $current_item['url'],
                    'media_type' => $media_type,
                    'year' => $year,
                    'season' => null,
                    'episode' => null,
                    'duration' => $current_item['duration'] ?? null
                ];

                $processed++;
                if ($processed % 100 == 0) {
                    debugLog("Procesados $processed elementos...");
                }
            }

            $current_item = null;
        }
    }

    debugLog("Análisis completado. Total elementos: " . count($items));
    return $items;
}

$message = '';
$message_type = '';
$analysis_results = null;

// Obtener lista específica si se proporciona ID
$list_id = $_GET['list_id'] ?? null;
$selected_list = null;

if ($list_id) {
    $stmt = $pdo->prepare("SELECT * FROM m3u_lists WHERE id = ?");
    $stmt->execute([$list_id]);
    $selected_list = $stmt->fetch(PDO::FETCH_ASSOC);
}

// Procesar análisis
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['analyze'])) {
    $list_id = $_POST['list_id'];

    debugLog("=== INICIANDO ANÁLISIS ===");

    try {
        // Obtener datos de la lista
        $stmt = $pdo->prepare("SELECT * FROM m3u_lists WHERE id = ?");
        $stmt->execute([$list_id]);
        $list = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$list) {
            throw new Exception("Lista no encontrada");
        }

        debugLog("Lista encontrada: " . $list['name']);
        debugLog("URL: " . $list['url']);

        // Analizar contenido
        $items = analyzeM3uSimple($list['url'], $list['username'], $list['password']);

        debugLog("Limpiando contenido anterior...");

        // Limpiar contenido anterior
        $stmt = $pdo->prepare("DELETE FROM m3u_content WHERE list_id = ?");
        $stmt->execute([$list_id]);

        debugLog("Insertando nuevo contenido...");

        // Insertar nuevo contenido
        $stmt = $pdo->prepare("
            INSERT INTO m3u_content
            (list_id, title, clean_title, media_type, year, season, episode, url, duration)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");

        $inserted = 0;
        foreach ($items as $item) {
            $stmt->execute([
                $list_id,
                $item['title'],
                $item['clean_title'],
                $item['media_type'],
                $item['year'],
                $item['season'],
                $item['episode'],
                $item['url'],
                $item['duration']
            ]);
            $inserted++;
        }

        debugLog("Insertados $inserted elementos");

        // Actualizar estadísticas
        $stmt = $pdo->prepare("
            UPDATE m3u_lists
            SET last_scan = NOW(), total_items = ?, last_updated = NOW()
            WHERE id = ?
        ");
        $stmt->execute([$inserted, $list_id]);

        debugLog("Estadísticas actualizadas");

        $analysis_results = [
            'total_items' => $inserted,
            'movies' => count(array_filter($items, fn($i) => $i['media_type'] === 'movie')),
            'tv_shows' => count(array_filter($items, fn($i) => $i['media_type'] === 'tv')),
            'unknown' => count(array_filter($items, fn($i) => $i['media_type'] === 'unknown')),
            'with_year' => count(array_filter($items, fn($i) => $i['year'] !== null))
        ];

        $message = "Análisis completado exitosamente. Se procesaron $inserted elementos.";
        $message_type = "success";

        debugLog("=== ANÁLISIS COMPLETADO ===");

    } catch (Exception $e) {
        debugLog("ERROR: " . $e->getMessage());
        $message = "Error durante el análisis: " . $e->getMessage();
        $message_type = "error";
    }
}

// Obtener todas las listas para el selector
$stmt = $pdo->query("SELECT id, name, is_active, last_scan, total_items FROM m3u_lists ORDER BY name");
$all_lists = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Analizador M3U - RGS TOOL</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            /* Paleta de colores negro y rojo */
            --primary-color: #dc2626;
            --primary-dark: #b91c1c;
            --secondary-color: #1f1f1f;
            --dark-bg: #000000;
            --darker-bg: #0a0a0a;
            --text-primary: #ffffff;
            --text-secondary: #d1d5db;
            --accent-color: #dc2626;
            --accent-dark: #059669;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --success-color: #10b981;
            --info-color: #06b6d4;
            --border-color: #334155;
            --border-light: #475569;
            --gradient-primary: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            --gradient-secondary: linear-gradient(135deg, #10b981 0%, #059669 100%);
            --gradient-dark: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
            --gradient-bg: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            --shadow-light: 0 2px 8px rgba(0,0,0,0.3);
            --shadow-medium: 0 4px 16px rgba(0,0,0,0.4);
            --shadow-heavy: 0 8px 32px rgba(0,0,0,0.6);
            --border-radius: 12px;
            --border-radius-lg: 16px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gradient-bg);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            background: linear-gradient(135deg, var(--accent-color), #28a745);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .message {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            text-align: center;
        }

        .message.success {
            background: rgba(40, 167, 69, 0.2);
            border: 1px solid var(--success-color);
            color: var(--success-color);
        }

        .message.error {
            background: rgba(220, 53, 69, 0.2);
            border: 1px solid var(--error-color);
            color: var(--error-color);
        }

        .form-section {
            background: var(--secondary-color);
            padding: 2rem;
            border-radius: 12px;
            margin-bottom: 2rem;
            border: 1px solid var(--border-color);
        }

        .form-section h2 {
            margin-bottom: 1.5rem;
            color: var(--accent-color);
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .form-select, .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background: var(--primary-color);
            color: var(--text-primary);
            font-size: 1rem;
        }

        .form-select:focus, .form-input:focus {
            outline: none;
            border-color: var(--accent-color);
            box-shadow: 0 0 0 2px rgba(70, 211, 71, 0.2);
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: var(--accent-color);
            color: var(--primary-color);
        }

        .btn-primary:hover {
            background: #3bc73c;
            transform: translateY(-2px);
        }

        .btn-primary:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }

        .results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .result-card {
            background: var(--secondary-color);
            padding: 1.5rem;
            border-radius: 12px;
            border: 1px solid var(--border-color);
            text-align: center;
        }

        .result-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--accent-color);
            margin-bottom: 0.5rem;
        }

        .result-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .back-link {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--accent-color);
            text-decoration: none;
            margin-bottom: 2rem;
            font-weight: 500;
        }

        .back-link:hover {
            color: #3bc73c;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 2rem;
            color: var(--text-secondary);
        }

        .loading.show {
            display: block;
        }

        .spinner {
            border: 3px solid var(--border-color);
            border-top: 3px solid var(--accent-color);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .list-info {
            background: var(--primary-color);
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }

        .list-info p {
            margin-bottom: 0.5rem;
            color: var(--text-secondary);
        }

        .list-info strong {
            color: var(--text-primary);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            .header {
                padding: 1rem;
            }

            .header h1 {
                font-size: 1.5rem;
            }

            .form-group {
                margin-bottom: 1rem;
            }

            .btn {
                min-height: 44px;
                padding: 0.6rem 1rem;
                width: 100%;
            }

            .results-grid {
                grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
                gap: 1rem;
            }

            .result-card {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="m3u_manager.php" class="back-link">
            <i class="fas fa-arrow-left"></i>
            Volver al Gestor M3U
        </a>

        <div class="header">
            <h1><i class="fas fa-search"></i> Analizador de Contenido M3U</h1>
            <p>Analiza y extrae contenido de tus listas IPTV</p>
        </div>

        <?php if ($message): ?>
        <div class="message <?php echo $message_type; ?>">
            <?php echo htmlspecialchars($message); ?>
        </div>
        <?php endif; ?>

        <!-- Formulario de análisis -->
        <div class="form-section">
            <h2><i class="fas fa-cog"></i> Configurar Análisis</h2>
            
            <form method="POST" id="analyzeForm">
                <div class="form-group">
                    <label for="list_id">Seleccionar Lista M3U</label>
                    <select name="list_id" id="list_id" class="form-select" required onchange="updateListInfo()">
                        <option value="">Seleccionar lista...</option>
                        <?php foreach ($all_lists as $list): ?>
                        <option value="<?php echo $list['id']; ?>" 
                                <?php echo ($selected_list && $selected_list['id'] == $list['id']) ? 'selected' : ''; ?>
                                data-last-scan="<?php echo $list['last_scan']; ?>"
                                data-total-items="<?php echo $list['total_items']; ?>"
                                data-active="<?php echo $list['is_active']; ?>">
                            <?php echo htmlspecialchars($list['name']); ?>
                            <?php if (!$list['is_active']): ?>(Inactiva)<?php endif; ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div id="listInfo" class="list-info" style="display: none;">
                    <p><strong>Último escaneo:</strong> <span id="lastScan">-</span></p>
                    <p><strong>Elementos actuales:</strong> <span id="totalItems">0</span></p>
                    <p><strong>Estado:</strong> <span id="listStatus">-</span></p>
                </div>

                <button type="submit" name="analyze" class="btn btn-primary" id="analyzeBtn">
                    <i class="fas fa-search"></i>
                    Iniciar Análisis
                </button>
            </form>

            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>Analizando contenido M3U...</p>
                <p><small>Este proceso puede tomar varios minutos dependiendo del tamaño de la lista</small></p>
            </div>
        </div>

        <!-- Log de Debug -->
        <?php if (!empty($debug_info)): ?>
        <div class="form-section">
            <h2><i class="fas fa-bug"></i> Log de Análisis</h2>
            <div style="background: #1a1a1a; padding: 15px; border-radius: 8px; max-height: 300px; overflow-y: auto; font-family: monospace; font-size: 12px;">
                <?php foreach ($debug_info as $log): ?>
                <div style="margin-bottom: 5px; color: #b0b0b0;"><?php echo htmlspecialchars($log); ?></div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Resultados del análisis -->
        <?php if ($analysis_results): ?>
        <div class="form-section">
            <h2><i class="fas fa-chart-bar"></i> Resultados del Análisis</h2>

            <div class="results-grid">
                <div class="result-card">
                    <div class="result-number"><?php echo $analysis_results['total_items']; ?></div>
                    <div class="result-label">Total de Elementos</div>
                </div>
                <div class="result-card">
                    <div class="result-number"><?php echo $analysis_results['movies']; ?></div>
                    <div class="result-label">Películas</div>
                </div>
                <div class="result-card">
                    <div class="result-number"><?php echo $analysis_results['tv_shows']; ?></div>
                    <div class="result-label">Series/TV</div>
                </div>
                <div class="result-card">
                    <div class="result-number"><?php echo $analysis_results['unknown']; ?></div>
                    <div class="result-label">Sin Clasificar</div>
                </div>
                <div class="result-card">
                    <div class="result-number"><?php echo $analysis_results['with_year']; ?></div>
                    <div class="result-label">Con Año</div>
                </div>
            </div>

            <p style="text-align: center; color: var(--text-secondary);">
                <i class="fas fa-info-circle"></i>
                El contenido ha sido indexado y está listo para comparar con los pedidos
            </p>

            <div style="text-align: center; margin-top: 1rem;">
                <a href="m3u_search.php" style="background: var(--accent-color); color: var(--primary-color); padding: 0.75rem 1.5rem; border-radius: 6px; text-decoration: none; font-weight: 500;">
                    <i class="fas fa-search"></i>
                    Buscar en el Contenido
                </a>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <script>
        function updateListInfo() {
            const select = document.getElementById('list_id');
            const info = document.getElementById('listInfo');
            const option = select.options[select.selectedIndex];
            
            if (option.value) {
                const lastScan = option.dataset.lastScan;
                const totalItems = option.dataset.totalItems;
                const isActive = option.dataset.active === '1';
                
                document.getElementById('lastScan').textContent = lastScan ? 
                    new Date(lastScan).toLocaleString('es-ES') : 'Nunca';
                document.getElementById('totalItems').textContent = totalItems;
                document.getElementById('listStatus').textContent = isActive ? 'Activa' : 'Inactiva';
                document.getElementById('listStatus').style.color = isActive ? 'var(--success-color)' : 'var(--error-color)';
                
                info.style.display = 'block';
            } else {
                info.style.display = 'none';
            }
        }

        document.getElementById('analyzeForm').addEventListener('submit', function() {
            document.getElementById('loading').classList.add('show');
            document.getElementById('analyzeBtn').disabled = true;
        });

        // Actualizar info si hay lista preseleccionada
        if (document.getElementById('list_id').value) {
            updateListInfo();
        }
    </script>
</body>
</html>
