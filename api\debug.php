<?php
/**
 * Debug API - Para diagnosticar problemas de routing
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');

// Información de debugging
$debug_info = [
    'REQUEST_URI' => $_SERVER['REQUEST_URI'] ?? 'N/A',
    'REQUEST_METHOD' => $_SERVER['REQUEST_METHOD'] ?? 'N/A',
    'SCRIPT_NAME' => $_SERVER['SCRIPT_NAME'] ?? 'N/A',
    'PATH_INFO' => $_SERVER['PATH_INFO'] ?? 'N/A',
    'QUERY_STRING' => $_SERVER['QUERY_STRING'] ?? 'N/A',
    'HTTP_HOST' => $_SERVER['HTTP_HOST'] ?? 'N/A',
    'SERVER_NAME' => $_SERVER['SERVER_NAME'] ?? 'N/A',
    'DOCUMENT_ROOT' => $_SERVER['DOCUMENT_ROOT'] ?? 'N/A',
    'SCRIPT_FILENAME' => $_SERVER['SCRIPT_FILENAME'] ?? 'N/A'
];

// Parsear la URL como lo hace la API principal
$request_uri = $_SERVER['REQUEST_URI'];
$path = parse_url($request_uri, PHP_URL_PATH);

$original_path = $path;

// Remover el script name si está presente
$script_name = $_SERVER['SCRIPT_NAME'] ?? '';
if (!empty($script_name) && strpos($path, $script_name) !== false) {
    $path = str_replace($script_name, '', $path);
}

// Manejar tanto /api/ como /series/api/
$path = str_replace('/series/api/', '', $path);
$path = str_replace('/api/', '', $path);

// Limpiar la ruta
$path = trim($path, '/');
$path_parts = empty($path) ? [] : explode('/', $path);

$endpoint = $path_parts[0] ?? '';
$action = $path_parts[1] ?? '';
$param = $path_parts[2] ?? '';

echo json_encode([
    'success' => true,
    'debug' => [
        'server_vars' => $debug_info,
        'url_parsing' => [
            'original_path' => $original_path,
            'processed_path' => $path,
            'path_parts' => $path_parts,
            'endpoint' => $endpoint,
            'action' => $action,
            'param' => $param
        ],
        'timestamp' => date('Y-m-d H:i:s')
    ]
], JSON_PRETTY_PRINT);
?>
