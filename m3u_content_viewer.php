<?php
// Visor de contenido M3U
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();

if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Error de conexión: " . $e->getMessage());
}

// Parámetros de filtrado
$list_id = $_GET['list_id'] ?? null;
$search = $_GET['search'] ?? '';
$media_type = $_GET['media_type'] ?? '';
$page = max(1, (int)($_GET['page'] ?? 1));
$per_page = 50;
$offset = ($page - 1) * $per_page;

// Obtener listas disponibles
$stmt = $pdo->query("SELECT id, name, total_items FROM m3u_lists WHERE total_items > 0 ORDER BY name");
$available_lists = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Si no hay list_id, usar la primera lista disponible
if (!$list_id && !empty($available_lists)) {
    $list_id = $available_lists[0]['id'];
}

$content = [];
$total_items = 0;
$list_info = null;

if ($list_id) {
    // Obtener información de la lista
    $stmt = $pdo->prepare("SELECT * FROM m3u_lists WHERE id = ?");
    $stmt->execute([$list_id]);
    $list_info = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Construir consulta con filtros
    $where_conditions = ["list_id = ?"];
    $params = [$list_id];
    
    if ($search) {
        $where_conditions[] = "(title LIKE ? OR clean_title LIKE ?)";
        $search_term = "%$search%";
        $params[] = $search_term;
        $params[] = $search_term;
    }
    
    if ($media_type) {
        $where_conditions[] = "media_type = ?";
        $params[] = $media_type;
    }
    
    $where_clause = implode(" AND ", $where_conditions);
    
    // Contar total
    $count_sql = "SELECT COUNT(*) FROM m3u_content WHERE $where_clause";
    $stmt = $pdo->prepare($count_sql);
    $stmt->execute($params);
    $total_items = $stmt->fetchColumn();
    
    // Obtener contenido paginado
    $content_sql = "
        SELECT * FROM m3u_content 
        WHERE $where_clause 
        ORDER BY title 
        LIMIT $per_page OFFSET $offset
    ";
    $stmt = $pdo->prepare($content_sql);
    $stmt->execute($params);
    $content = $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Calcular paginación
$total_pages = ceil($total_items / $per_page);
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📺 Contenido M3U - RGS TOOL</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #1a1a1a;
            --secondary-color: #2d2d2d;
            --accent-color: #46d347;
            --text-primary: #ffffff;
            --text-secondary: #b0b0b0;
            --border-color: #404040;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--primary-color);
            color: var(--text-primary);
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .header h1 {
            font-size: 2rem;
            background: linear-gradient(135deg, var(--accent-color), #28a745);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .back-link {
            color: var(--accent-color);
            text-decoration: none;
            font-weight: 500;
        }

        .filters {
            background: var(--secondary-color);
            padding: 1.5rem;
            border-radius: 12px;
            margin-bottom: 2rem;
            border: 1px solid var(--border-color);
        }

        .filter-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            align-items: end;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
        }

        .filter-group label {
            margin-bottom: 0.5rem;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .form-input, .form-select {
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background: var(--primary-color);
            color: var(--text-primary);
            font-size: 1rem;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
        }

        .btn-primary {
            background: var(--accent-color);
            color: var(--primary-color);
        }

        .btn-primary:hover {
            background: #3bc73c;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--secondary-color);
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
            border: 1px solid var(--border-color);
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--accent-color);
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .content-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1rem;
        }

        .content-item {
            background: var(--secondary-color);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            transition: all 0.3s ease;
        }

        .content-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        }

        .content-title {
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--accent-color);
            font-size: 0.9rem;
            line-height: 1.3;
        }

        .content-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
        }

        .meta-badge {
            padding: 0.2rem 0.5rem;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 500;
        }

        .badge-movie {
            background: rgba(220, 53, 69, 0.2);
            color: #dc3545;
        }

        .badge-tv {
            background: rgba(23, 162, 184, 0.2);
            color: #17a2b8;
        }

        .badge-unknown {
            background: rgba(108, 117, 125, 0.2);
            color: #6c757d;
        }

        .badge-year {
            background: rgba(255, 193, 7, 0.2);
            color: #ffc107;
        }

        .content-url {
            font-size: 0.7rem;
            color: var(--text-secondary);
            word-break: break-all;
            margin-top: 0.5rem;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.5rem;
            margin-top: 2rem;
        }

        .pagination a, .pagination span {
            padding: 0.5rem 1rem;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            text-decoration: none;
            color: var(--text-primary);
            background: var(--secondary-color);
        }

        .pagination a:hover {
            background: var(--accent-color);
            color: var(--primary-color);
        }

        .pagination .current {
            background: var(--accent-color);
            color: var(--primary-color);
        }

        .no-content {
            text-align: center;
            padding: 3rem;
            color: var(--text-secondary);
        }

        .no-content i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-tv"></i> Contenido M3U</h1>
            <a href="admin.php" class="back-link">
                <i class="fas fa-arrow-left"></i>
                Volver al Admin
            </a>
        </div>

        <!-- Filtros -->
        <div class="filters">
            <form method="GET" class="filter-grid">
                <div class="filter-group">
                    <label for="list_id">Lista M3U</label>
                    <select name="list_id" id="list_id" class="form-select" onchange="this.form.submit()">
                        <option value="">Seleccionar lista...</option>
                        <?php foreach ($available_lists as $list): ?>
                        <option value="<?php echo $list['id']; ?>" <?php echo $list_id == $list['id'] ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($list['name']); ?> (<?php echo number_format($list['total_items']); ?> elementos)
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="filter-group">
                    <label for="search">Buscar</label>
                    <input type="text" name="search" id="search" class="form-input" 
                           value="<?php echo htmlspecialchars($search); ?>" 
                           placeholder="Título de serie o película...">
                </div>

                <div class="filter-group">
                    <label for="media_type">Tipo</label>
                    <select name="media_type" id="media_type" class="form-select">
                        <option value="">Todos</option>
                        <option value="movie" <?php echo $media_type === 'movie' ? 'selected' : ''; ?>>Películas</option>
                        <option value="tv" <?php echo $media_type === 'tv' ? 'selected' : ''; ?>>Series</option>
                        <option value="unknown" <?php echo $media_type === 'unknown' ? 'selected' : ''; ?>>Sin clasificar</option>
                    </select>
                </div>

                <div class="filter-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i>
                        Filtrar
                    </button>
                </div>
            </form>
        </div>

        <?php if ($list_info): ?>
        <!-- Estadísticas -->
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($total_items); ?></div>
                <div class="stat-label">Resultados</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($list_info['total_items']); ?></div>
                <div class="stat-label">Total en Lista</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $page; ?>/<?php echo $total_pages; ?></div>
                <div class="stat-label">Página</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $list_info['last_scan'] ? date('d/m', strtotime($list_info['last_scan'])) : 'N/A'; ?></div>
                <div class="stat-label">Último Análisis</div>
            </div>
        </div>

        <!-- Contenido -->
        <?php if (empty($content)): ?>
        <div class="no-content">
            <i class="fas fa-search"></i>
            <h3>No se encontró contenido</h3>
            <p>Intenta cambiar los filtros de búsqueda</p>
        </div>
        <?php else: ?>
        <div class="content-grid">
            <?php foreach ($content as $item): ?>
            <div class="content-item">
                <div class="content-title"><?php echo htmlspecialchars($item['title']); ?></div>
                
                <div class="content-meta">
                    <span class="meta-badge badge-<?php echo $item['media_type']; ?>">
                        <?php 
                        echo $item['media_type'] === 'movie' ? '🎬 Película' : 
                             ($item['media_type'] === 'tv' ? '📺 Serie' : '❓ Desconocido'); 
                        ?>
                    </span>
                    
                    <?php if ($item['year']): ?>
                    <span class="meta-badge badge-year"><?php echo $item['year']; ?></span>
                    <?php endif; ?>
                    
                    <?php if ($item['season']): ?>
                    <span class="meta-badge badge-tv">T<?php echo $item['season']; ?></span>
                    <?php endif; ?>
                    
                    <?php if ($item['episode']): ?>
                    <span class="meta-badge badge-tv">E<?php echo $item['episode']; ?></span>
                    <?php endif; ?>
                </div>
                
                <div class="content-url">
                    <?php echo htmlspecialchars(substr($item['url'], 0, 80)) . '...'; ?>
                </div>
            </div>
            <?php endforeach; ?>
        </div>

        <!-- Paginación -->
        <?php if ($total_pages > 1): ?>
        <div class="pagination">
            <?php if ($page > 1): ?>
            <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">
                <i class="fas fa-chevron-left"></i>
            </a>
            <?php endif; ?>
            
            <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
            <?php if ($i == $page): ?>
            <span class="current"><?php echo $i; ?></span>
            <?php else: ?>
            <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>"><?php echo $i; ?></a>
            <?php endif; ?>
            <?php endfor; ?>
            
            <?php if ($page < $total_pages): ?>
            <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">
                <i class="fas fa-chevron-right"></i>
            </a>
            <?php endif; ?>
        </div>
        <?php endif; ?>
        <?php endif; ?>
        <?php endif; ?>
    </div>
</body>
</html>
