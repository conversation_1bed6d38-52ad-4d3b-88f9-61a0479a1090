// Script para el modal de revendedor

let currentResellerStep = 1;
let uploadedResellerFile = null;

// Abrir modal de revendedor
function openResellerModal() {
    document.getElementById('resellerModal').style.display = 'flex';
    currentResellerStep = 1;
    updateResellerProgressIndicator();
    document.body.style.overflow = 'hidden';
}

// Cerrar modal de revendedor
function closeResellerModal() {
    document.getElementById('resellerModal').style.display = 'none';
    document.body.style.overflow = 'auto';
    resetResellerForm();
}

// Resetear formulario
function resetResellerForm() {
    document.getElementById('resellerForm').reset();
    currentResellerStep = 1;
    uploadedResellerFile = null;
    
    // Ocultar todos los pasos
    document.querySelectorAll('#resellerModal .form-step').forEach(step => {
        step.classList.remove('active');
    });
    
    // Mostrar primer paso
    document.getElementById('resellerStep1').classList.add('active');
    
    // Resetear indicador de progreso
    updateResellerProgressIndicator();
    
    // Ocultar preview de archivo
    const uploadPreview = document.getElementById('resellerUploadPreview');
    if (uploadPreview) {
        uploadPreview.style.display = 'none';
    }
    
    const uploadArea = document.getElementById('resellerUploadArea');
    if (uploadArea) {
        uploadArea.style.display = 'block';
    }
    
    // Resetear visibilidad de secciones
    updateResellerTypeVisibility();
}

// Navegar al siguiente paso
function nextResellerStep(step) {
    if (validateResellerStep(currentResellerStep)) {
        // Ocultar paso actual
        document.getElementById(`resellerStep${currentResellerStep}`).classList.remove('active');
        
        // Mostrar siguiente paso
        currentResellerStep = step;
        document.getElementById(`resellerStep${currentResellerStep}`).classList.add('active');
        
        // Actualizar indicador de progreso
        updateResellerProgressIndicator();
        
        // Actualizar visibilidad según tipo de revendedor
        if (currentResellerStep === 4) {
            updateResellerPaymentSection();
        }
        
        // Actualizar resumen si es el último paso
        if (currentResellerStep === 5) {
            updateResellerSummary();
        }
    }
}

// Navegar al paso anterior
function prevResellerStep(step) {
    // Ocultar paso actual
    document.getElementById(`resellerStep${currentResellerStep}`).classList.remove('active');
    
    // Mostrar paso anterior
    currentResellerStep = step;
    document.getElementById(`resellerStep${currentResellerStep}`).classList.add('active');
    
    // Actualizar indicador de progreso
    updateResellerProgressIndicator();
}

// Validar paso actual
function validateResellerStep(step) {
    switch(step) {
        case 1:
            const resellerType = document.querySelector('input[name="resellerType"]:checked');
            if (!resellerType) {
                showToast('Por favor selecciona el tipo de solicitud', 'error');
                return false;
            }
            break;
            
        case 2:
            const firstName = document.getElementById('resellerFirstName').value.trim();
            const lastName = document.getElementById('resellerLastName').value.trim();
            const email = document.getElementById('resellerEmail').value.trim();
            const phone = document.getElementById('resellerPhone').value.trim();
            
            if (!firstName || !lastName || !email || !phone) {
                showToast('Por favor completa todos los campos obligatorios', 'error');
                return false;
            }
            
            if (!isValidEmail(email)) {
                showToast('Por favor ingresa un email válido', 'error');
                return false;
            }
            
            // Validar ID de revendedor si es renovación
            const isRenewal = document.querySelector('input[name="resellerType"]:checked').value === 'renewal';
            if (isRenewal) {
                const platformId = document.getElementById('resellerPlatformId').value.trim();
                if (!platformId) {
                    showToast('Por favor ingresa tu ID de revendedor actual', 'error');
                    return false;
                }
            }
            break;
            
        case 3:
            // Los campos del paso 3 son opcionales
            break;
            
        case 4:
            // Validar comprobante solo si es renovación
            const isRenewalPayment = document.querySelector('input[name="resellerType"]:checked').value === 'renewal';
            if (isRenewalPayment && uploadedResellerFile && !isValidFileType(uploadedResellerFile)) {
                showToast('Por favor sube un archivo válido (JPG, PNG, PDF)', 'error');
                return false;
            }
            break;
    }
    
    return true;
}

// Actualizar indicador de progreso
function updateResellerProgressIndicator() {
    document.querySelectorAll('#resellerModal .progress-step').forEach((step, index) => {
        if (index + 1 <= currentResellerStep) {
            step.classList.add('active');
        } else {
            step.classList.remove('active');
        }
    });
}

// Actualizar visibilidad según tipo de revendedor
function updateResellerTypeVisibility() {
    const resellerTypeInputs = document.querySelectorAll('input[name="resellerType"]');
    resellerTypeInputs.forEach(input => {
        input.addEventListener('change', function() {
            const existingResellerInfo = document.getElementById('existingResellerInfo');
            if (this.value === 'renewal') {
                existingResellerInfo.style.display = 'block';
                document.getElementById('resellerPlatformId').required = true;
            } else {
                existingResellerInfo.style.display = 'none';
                document.getElementById('resellerPlatformId').required = false;
            }
        });
    });
}

// Actualizar sección de pago según tipo
function updateResellerPaymentSection() {
    const resellerType = document.querySelector('input[name="resellerType"]:checked').value;
    const newResellerPayment = document.getElementById('newResellerPayment');
    const renewalResellerPayment = document.getElementById('renewalResellerPayment');
    const paymentDescription = document.getElementById('paymentStepDescription');
    
    if (resellerType === 'new') {
        newResellerPayment.style.display = 'block';
        renewalResellerPayment.style.display = 'none';
        paymentDescription.textContent = 'Información sobre el proceso de pago';
    } else {
        newResellerPayment.style.display = 'none';
        renewalResellerPayment.style.display = 'block';
        paymentDescription.textContent = 'Sube tu comprobante de pago para la renovación';
    }
}

// Actualizar resumen
function updateResellerSummary() {
    // Tipo de revendedor
    const resellerType = document.querySelector('input[name="resellerType"]:checked').value;
    const typeText = resellerType === 'new' ? 'Nuevo Revendedor' : 'Renovación de Revendedor';
    document.getElementById('summaryResellerType').textContent = typeText;
    
    // Nombre completo
    const firstName = document.getElementById('resellerFirstName').value;
    const lastName = document.getElementById('resellerLastName').value;
    document.getElementById('summaryResellerName').textContent = `${firstName} ${lastName}`;
    
    // Email
    const email = document.getElementById('resellerEmail').value;
    document.getElementById('summaryResellerEmail').textContent = email;
    
    // Experiencia
    const experience = document.getElementById('businessExperience').value;
    const experienceText = experience ? document.querySelector(`#businessExperience option[value="${experience}"]`).textContent : 'No especificada';
    document.getElementById('summaryExperience').textContent = experienceText;
    
    // Clientes esperados
    const clients = document.getElementById('expectedClients').value;
    const clientsText = clients ? document.querySelector(`#expectedClients option[value="${clients}"]`).textContent : 'No especificado';
    document.getElementById('summaryClients').textContent = clientsText;
}

// Configurar upload de archivos para revendedor
document.addEventListener('DOMContentLoaded', function() {
    const uploadArea = document.getElementById('resellerUploadArea');
    const fileInput = document.getElementById('resellerPaymentProof');
    
    if (uploadArea && fileInput) {
        // Click en área de upload
        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });
        
        // Drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = 'var(--primary-color)';
            uploadArea.style.backgroundColor = 'rgba(255, 255, 255, 0.02)';
        });
        
        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = 'rgba(255, 255, 255, 0.3)';
            uploadArea.style.backgroundColor = 'transparent';
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = 'rgba(255, 255, 255, 0.3)';
            uploadArea.style.backgroundColor = 'transparent';
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleResellerFileUpload(files[0]);
            }
        });
        
        // Cambio en input de archivo
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleResellerFileUpload(e.target.files[0]);
            }
        });
    }
    
    // Configurar eventos de tipo de revendedor
    updateResellerTypeVisibility();
});

// Manejar subida de archivo para revendedor
function handleResellerFileUpload(file) {
    if (!isValidFileType(file)) {
        showToast('Tipo de archivo no válido. Solo se permiten JPG, PNG y PDF', 'error');
        return;
    }
    
    if (file.size > 5 * 1024 * 1024) { // 5MB
        showToast('El archivo es demasiado grande. Máximo 5MB', 'error');
        return;
    }
    
    uploadedResellerFile = file;
    
    // Mostrar preview
    document.getElementById('resellerUploadArea').style.display = 'none';
    document.getElementById('resellerUploadPreview').style.display = 'block';
    
    // Actualizar información del archivo
    document.querySelector('#resellerUploadPreview .file-name').textContent = file.name;
    document.querySelector('#resellerUploadPreview .file-size').textContent = formatFileSize(file.size);
    
    showToast('Archivo subido correctamente', 'success');
}

// Remover archivo subido de revendedor
function removeResellerUploadedFile() {
    uploadedResellerFile = null;
    document.getElementById('resellerUploadArea').style.display = 'block';
    document.getElementById('resellerUploadPreview').style.display = 'none';
    document.getElementById('resellerPaymentProof').value = '';
}

// Enviar formulario de revendedor
document.getElementById('resellerForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    if (!validateResellerStep(5)) {
        return;
    }
    
    const termsAccepted = document.getElementById('resellerTerms').checked;
    const privacyAccepted = document.getElementById('resellerPrivacy').checked;
    
    if (!termsAccepted || !privacyAccepted) {
        showToast('Debes aceptar los términos y condiciones y la política de privacidad', 'error');
        return;
    }
    
    const submitBtn = document.getElementById('submitResellerBtn');
    const originalText = submitBtn.innerHTML;
    
    try {
        // Mostrar loading
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Enviando...';
        submitBtn.disabled = true;
        
        // Preparar datos del formulario
        const formData = new FormData();
        
        // Tipo de servicio
        const resellerType = document.querySelector('input[name="resellerType"]:checked').value;
        formData.append('serviceType', resellerType === 'new' ? 'reseller_activation' : 'reseller_renewal');
        formData.append('isReseller', true);
        formData.append('resellerType', resellerType);
        
        // Datos básicos
        formData.append('firstName', document.getElementById('resellerFirstName').value);
        formData.append('lastName', document.getElementById('resellerLastName').value);
        formData.append('email', document.getElementById('resellerEmail').value);
        formData.append('phone', document.getElementById('resellerPhone').value);
        
        // ID de plataforma si es renovación
        if (resellerType === 'renewal') {
            formData.append('platformId', document.getElementById('resellerPlatformId').value);
        }
        
        // Información del negocio
        formData.append('businessExperience', document.getElementById('businessExperience').value);
        formData.append('expectedClients', document.getElementById('expectedClients').value);
        formData.append('additionalInfo', document.getElementById('additionalInfo').value);
        
        // Canales de marketing
        const marketingChannels = [];
        document.querySelectorAll('input[name="marketingChannels[]"]:checked').forEach(checkbox => {
            marketingChannels.push(checkbox.value);
        });
        formData.append('marketingChannels', JSON.stringify(marketingChannels));
        
        // Archivo de comprobante si existe
        if (uploadedResellerFile) {
            formData.append('paymentProof', uploadedResellerFile);
        }
        
        // Términos
        formData.append('acceptTerms', true);
        formData.append('acceptPrivacy', true);
        
        // Enviar solicitud
        const response = await fetch('submit_service_request.php', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            showToast('¡Solicitud de revendedor enviada exitosamente!', 'success');
            closeResellerModal();
            
            // Mostrar confirmación
            setTimeout(() => {
                if (confirm('¿Deseas ver los detalles de tu solicitud de revendedor?')) {
                    window.location.href = 'service_request_confirmation.php?id=' + result.request_id;
                }
            }, 1000);
        } else {
            showToast(result.message || 'Error al enviar la solicitud de revendedor', 'error');
        }
        
    } catch (error) {
        console.error('Error:', error);
        showToast('Error de conexión. Por favor intenta nuevamente.', 'error');
    } finally {
        // Restaurar botón
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }
});

// Cerrar modal al hacer clic fuera
document.addEventListener('click', function(e) {
    if (e.target.id === 'resellerModal') {
        closeResellerModal();
    }
});

// Cerrar modal con tecla Escape
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape' && document.getElementById('resellerModal').style.display === 'flex') {
        closeResellerModal();
    }
});
