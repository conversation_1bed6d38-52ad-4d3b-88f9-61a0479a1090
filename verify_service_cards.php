<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Verificar Tarjetas de Servicios</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .header {
            text-align: center;
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            border-radius: 8px;
        }
        
        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }
        
        .service-card {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            border: 1px solid rgba(255, 255, 255, 0.12);
            border-radius: 12px;
            overflow: hidden;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            position: relative;
        }
        
        .service-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.4);
        }
        
        .service-header {
            padding: 2rem;
            text-align: center;
            position: relative;
        }
        
        .service-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 2rem;
            color: white;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }
        
        .service-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: white;
            margin-bottom: 0.5rem;
        }
        
        .service-description {
            color: #cbd5e1;
            margin-bottom: 1rem;
        }
        
        .service-features {
            padding: 0 2rem 2rem;
        }
        
        .features-list {
            list-style: none;
            margin: 0;
            padding: 0;
        }
        
        .features-list li {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.5rem 0;
            color: #cbd5e1;
        }
        
        .features-list li::before {
            content: '✓';
            color: #10b981;
            font-weight: bold;
            font-size: 1.1rem;
        }
        
        .service-action {
            padding: 0 2rem 2rem;
        }
        
        .btn {
            width: 100%;
            padding: 1rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            text-decoration: none;
            font-size: 1rem;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(107, 114, 128, 0.3);
        }
        
        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(107, 114, 128, 0.4);
        }
        
        .promo-badge {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            box-shadow: 0 2px 8px rgba(220, 38, 38, 0.3);
        }
        
        .status {
            padding: 0.5rem 1rem;
            border-radius: 6px;
            margin: 0.5rem 0;
            font-weight: 600;
        }
        
        .status.success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }
        
        .status.error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fecaca;
        }
        
        .status.info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #93c5fd;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Verificar Tarjetas de Servicios</h1>
            <p>Verificación visual de las 3 tarjetas de servicios</p>
        </div>

        <div class="status info">
            ℹ️ Esta página muestra exactamente las mismas tarjetas que deberían aparecer en service_options.php
        </div>

        <div class="services-grid">
            <!-- Nuevo Servicio -->
            <div class="service-card">
                <div class="service-header">
                    <div class="service-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <h2 class="service-title">Nuevo Servicio</h2>
                    <p class="service-description">
                        Solicita tu primer servicio IPTV con prueba gratuita o activación directa
                    </p>
                </div>
                <div class="service-features">
                    <ul class="features-list">
                        <li>Prueba gratuita de 24-48 horas</li>
                        <li>Más de 5000 canales HD/4K</li>
                        <li>Compatible con todos los dispositivos</li>
                        <li>Soporte técnico incluido</li>
                        <li>Sin compromisos iniciales</li>
                    </ul>
                </div>
                <div class="service-action">
                    <button onclick="testModal('nuevo')" class="btn btn-primary">
                        <i class="fas fa-rocket"></i>
                        Solicitar Nuevo Servicio
                    </button>
                </div>
            </div>

            <!-- Renovar Servicio -->
            <div class="service-card">
                <div class="promo-badge">¡Promoción!</div>
                <div class="service-header">
                    <div class="service-icon">
                        <i class="fas fa-refresh"></i>
                    </div>
                    <h2 class="service-title">Renovar Servicio</h2>
                    <p class="service-description">
                        Renueva tu servicio existente con descuentos especiales
                    </p>
                </div>
                <div class="service-features">
                    <ul class="features-list">
                        <li>Descuentos por renovación</li>
                        <li>Planes flexibles (1, 3, 6 meses, 1 año)</li>
                        <li>Subida de comprobante de pago</li>
                        <li>Renovación sin interrupciones</li>
                        <li>Soporte prioritario</li>
                    </ul>
                </div>
                <div class="service-action">
                    <button onclick="testModal('renovacion')" class="btn btn-primary">
                        <i class="fas fa-sync-alt"></i>
                        Renovar Mi Servicio
                    </button>
                </div>
            </div>

            <!-- Ser Revendedor -->
            <div class="service-card">
                <div class="service-header">
                    <div class="service-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h2 class="service-title">Ser Revendedor</h2>
                    <p class="service-description">
                        Únete a nuestro programa de revendedores y obtén beneficios exclusivos
                    </p>
                </div>
                <div class="service-features">
                    <ul class="features-list">
                        <li>Precios especiales para revendedores</li>
                        <li>Panel de gestión de clientes</li>
                        <li>Soporte técnico prioritario</li>
                        <li>Comisiones atractivas</li>
                        <li>Material promocional incluido</li>
                    </ul>
                </div>
                <div class="service-action">
                    <button onclick="testModal('revendedor')" class="btn btn-secondary">
                        <i class="fas fa-handshake"></i>
                        Solicitar Ser Revendedor
                    </button>
                </div>
            </div>
        </div>

        <div style="text-align: center; margin-top: 3rem;">
            <div class="status success">
                ✅ Si puedes ver las 3 tarjetas arriba, entonces el problema está en service_options.php
            </div>
            <div class="status info">
                ℹ️ Si no puedes ver alguna tarjeta, puede ser un problema de CSS o JavaScript
            </div>
            
            <div style="margin-top: 2rem;">
                <a href="service_options.php" class="btn btn-primary" style="display: inline-flex; width: auto;">
                    🎯 Ir a service_options.php
                </a>
                <button onclick="checkServiceOptions()" class="btn btn-secondary" style="display: inline-flex; width: auto; margin-left: 1rem;">
                    🔍 Verificar service_options.php
                </button>
            </div>
        </div>
    </div>

    <!-- Font Awesome para iconos -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <script>
        function testModal(tipo) {
            alert(`🧪 Test: Intentando abrir modal de ${tipo}\n\nEn service_options.php esto debería abrir el modal correspondiente.`);
        }

        async function checkServiceOptions() {
            try {
                console.log('🔍 Verificando service_options.php...');
                
                const response = await fetch('service_options.php');
                const html = await response.text();
                
                // Verificar si contiene las 3 tarjetas
                const checks = {
                    'Nuevo Servicio': html.includes('Solicitar Nuevo Servicio'),
                    'Renovar Servicio': html.includes('Renovar Mi Servicio'),
                    'Ser Revendedor': html.includes('Solicitar Ser Revendedor')
                };
                
                let message = '🔍 Verificación de service_options.php:\n\n';
                let allFound = true;
                
                for (const [service, found] of Object.entries(checks)) {
                    const status = found ? '✅' : '❌';
                    message += `${status} ${service}: ${found ? 'Encontrado' : 'NO encontrado'}\n`;
                    if (!found) allFound = false;
                }
                
                if (allFound) {
                    message += '\n✅ Todas las tarjetas están en el HTML';
                    message += '\n💡 Si no las ves, puede ser un problema de CSS o cache del navegador';
                    message += '\n🔄 Intenta refrescar la página con Ctrl+F5';
                } else {
                    message += '\n❌ Faltan algunas tarjetas en el HTML';
                    message += '\n🔧 Necesitas verificar el código de service_options.php';
                }
                
                alert(message);
                
            } catch (error) {
                alert('❌ Error verificando service_options.php: ' + error.message);
            }
        }

        // Verificar automáticamente al cargar
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎯 Página de verificación cargada');
            console.log('📊 Tarjetas visibles:', document.querySelectorAll('.service-card').length);
            
            // Mostrar información en consola
            const cards = document.querySelectorAll('.service-card');
            cards.forEach((card, index) => {
                const title = card.querySelector('.service-title').textContent;
                console.log(`✅ Tarjeta ${index + 1}: ${title}`);
            });
        });
    </script>
</body>
</html>
