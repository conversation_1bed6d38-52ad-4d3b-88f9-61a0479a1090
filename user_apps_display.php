<?php
// Componente para mostrar aplicaciones en index2.php
require_once 'config.php';

// Obtener aplicaciones activas
try {
    $stmt = $pdo->prepare("
        SELECT sa.*, 
               (SELECT COUNT(*) FROM app_downloads ad WHERE ad.app_id = sa.id) as total_downloads
        FROM support_apps sa 
        WHERE sa.is_active = 1 AND (sa.status = 'active' OR sa.status IS NULL)
        ORDER BY sa.created_at DESC
    ");
    $stmt->execute();
    $apps = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $apps = [];
}

// Función para registrar descarga
function registerDownload($app_id, $pdo) {
    try {
        $user_id = $_SESSION['user_id'] ?? null;
        $ip_address = get_real_ip();
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        
        $stmt = $pdo->prepare("INSERT INTO app_downloads (app_id, user_id, ip_address, user_agent) VALUES (?, ?, ?, ?)");
        $stmt->execute([$app_id, $user_id, $ip_address, $user_agent]);
        
        // Actualizar contador en la app
        $stmt = $pdo->prepare("UPDATE support_apps SET download_count = download_count + 1 WHERE id = ?");
        $stmt->execute([$app_id]);
        
        return true;
    } catch (Exception $e) {
        return false;
    }
}

// Este componente ya no maneja descargas directamente
// Las descargas se procesan a través de download_app.php
?>

<style>
.apps-section {
    margin: 2rem 0;
}

.apps-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.app-card {
    background: rgba(255, 255, 255, 0.03);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(10px);
    overflow: hidden;
    transition: all 0.3s ease;
}

.app-card:hover {
    background: rgba(255, 255, 255, 0.06);
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0,0,0,0.4);
    border-color: var(--primary-color, #6366f1);
}

.app-header {
    padding: 1rem;
    background: rgba(255, 255, 255, 0.02);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.app-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary, #f8fafc);
    margin-bottom: 0.25rem;
}

.app-version {
    color: var(--text-secondary, #cbd5e1);
    font-size: 0.8rem;
}

.app-content {
    padding: 1rem;
}

.app-description {
    color: var(--text-secondary, #cbd5e1);
    margin-bottom: 0.75rem;
    line-height: 1.4;
    font-size: 0.85rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.app-features {
    margin-bottom: 1rem;
}

.app-features h4 {
    color: var(--text-primary, #f8fafc);
    font-size: 0.8rem;
    margin-bottom: 0.25rem;
}

.features-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
}

.feature-tag {
    background: rgba(99, 102, 241, 0.2);
    color: #6366f1;
    padding: 0.15rem 0.4rem;
    border-radius: 8px;
    font-size: 0.7rem;
}

.app-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
    padding: 0.75rem;
    background: rgba(255,255,255,0.03);
    border-radius: 6px;
}

.app-stat {
    text-align: center;
}

.app-stat-number {
    font-size: 1rem;
    font-weight: 700;
    color: var(--accent-color, #10b981);
}

.app-stat-label {
    font-size: 0.7rem;
    color: var(--text-secondary, #cbd5e1);
}

.app-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.app-download-btn {
    flex: 1;
    padding: 0.5rem 0.75rem;
    background: var(--primary-color, #6366f1);
    color: white;
    text-decoration: none;
    border-radius: 6px;
    text-align: center;
    font-weight: 500;
    font-size: 0.8rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.4rem;
}

.app-download-btn:hover {
    background: var(--primary-dark, #4f46e5);
    transform: translateY(-2px);
}

.app-external-btn {
    padding: 0.5rem 0.75rem;
    background: transparent;
    color: var(--text-secondary, #cbd5e1);
    text-decoration: none;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    text-align: center;
    font-weight: 500;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.app-external-btn:hover {
    background: rgba(255,255,255,0.1);
    color: var(--text-primary, #f8fafc);
    border-color: rgba(255, 255, 255, 0.2);
}

.platform-badge {
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 500;
    margin-bottom: 0.75rem;
    display: inline-block;
}

.platform-android { background: rgba(76, 175, 80, 0.2); color: #4caf50; }
.platform-ios { background: rgba(33, 150, 243, 0.2); color: #2196f3; }
.platform-windows { background: rgba(96, 125, 139, 0.2); color: #607d8b; }
.platform-mac { background: rgba(158, 158, 158, 0.2); color: #9e9e9e; }
.platform-linux { background: rgba(255, 152, 0, 0.2); color: #ff9800; }
.platform-web { background: rgba(156, 39, 176, 0.2); color: #9c27b0; }

.no-apps {
    text-align: center;
    padding: 3rem;
    color: var(--text-secondary, #cbd5e1);
}

.no-apps i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

@media (max-width: 768px) {
    .apps-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 0.75rem;
    }

    .app-header {
        padding: 0.75rem;
    }

    .app-content {
        padding: 0.75rem;
    }

    .app-actions {
        flex-direction: column;
        gap: 0.4rem;
    }

    .app-download-btn,
    .app-external-btn {
        padding: 0.4rem 0.6rem;
        font-size: 0.75rem;
    }
}

@media (max-width: 480px) {
    .apps-grid {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .app-card {
        margin-bottom: 0.5rem;
    }
}
</style>

<div class="apps-section">
    <h2 style="color: var(--text-primary, #f8fafc); margin-bottom: 1rem; font-size: 1.3rem; font-weight: 600;">
        <i class="fas fa-mobile-alt" style="color: var(--primary-color, #6366f1);"></i>
        Aplicaciones IPTV Compactas
    </h2>
    
    <?php if (empty($apps)): ?>
    <div class="no-apps">
        <i class="fas fa-mobile-alt"></i>
        <p>No hay aplicaciones disponibles en este momento</p>
    </div>
    <?php else: ?>
    <div class="apps-grid">
        <?php foreach ($apps as $app): ?>
        <div class="app-card">
            <div class="app-header">
                <div class="app-title"><?php echo htmlspecialchars($app['name']); ?></div>
                <div class="app-version">Versión <?php echo htmlspecialchars($app['version']); ?></div>
            </div>
            
            <div class="app-content">
                <span class="platform-badge platform-<?php echo $app['platform']; ?>">
                    <?php echo ucfirst($app['platform']); ?>
                </span>
                
                <?php if ($app['description']): ?>
                <div class="app-description">
                    <?php echo nl2br(htmlspecialchars($app['description'])); ?>
                </div>
                <?php endif; ?>
                
                <?php if ($app['features']): ?>
                <div class="app-features">
                    <h4>Características:</h4>
                    <div class="features-list">
                        <?php 
                        $features = explode(',', $app['features']);
                        foreach ($features as $feature): 
                            $feature = trim($feature);
                            if (!empty($feature)):
                        ?>
                        <span class="feature-tag"><?php echo htmlspecialchars($feature); ?></span>
                        <?php 
                            endif;
                        endforeach; 
                        ?>
                    </div>
                </div>
                <?php endif; ?>
                
                <div class="app-stats">
                    <div class="app-stat">
                        <div class="app-stat-number"><?php echo number_format($app['total_downloads']); ?></div>
                        <div class="app-stat-label">Descargas</div>
                    </div>
                    <div class="app-stat">
                        <div class="app-stat-number"><?php echo date('d/m/Y', strtotime($app['created_at'])); ?></div>
                        <div class="app-stat-label">Publicado</div>
                    </div>
                </div>
                
                <div class="app-actions">
                    <?php
                    $hasDownload = false;

                    // Verificar si es un archivo local
                    if (!empty($app['file_path']) && file_exists($app['file_path'])):
                        $hasDownload = true;
                    ?>
                    <a href="download_app.php?id=<?php echo $app['id']; ?>&action=download"
                       class="app-download-btn"
                       onclick="trackDownload(<?php echo $app['id']; ?>, 'file')">
                        <i class="fas fa-download"></i>
                        Descargar
                    </a>
                    <?php endif; ?>

                    <?php
                    // Verificar si tiene URL de tienda (Play Store, App Store, etc.)
                    if (!empty($app['download_url'])):
                        $hasDownload = true;
                        $storeIcon = 'fas fa-store';
                        $storeText = 'Tienda';

                        // Detectar tipo de tienda por URL
                        if (strpos($app['download_url'], 'play.google.com') !== false) {
                            $storeIcon = 'fab fa-google-play';
                            $storeText = 'Play Store';
                        } elseif (strpos($app['download_url'], 'apps.apple.com') !== false) {
                            $storeIcon = 'fab fa-app-store';
                            $storeText = 'App Store';
                        } elseif (strpos($app['download_url'], 'microsoft.com') !== false) {
                            $storeIcon = 'fab fa-microsoft';
                            $storeText = 'Microsoft Store';
                        }
                    ?>
                    <a href="download_app.php?id=<?php echo $app['id']; ?>&action=store"
                       class="app-download-btn"
                       onclick="trackDownload(<?php echo $app['id']; ?>, 'store')"
                       style="background: #10b981;">
                        <i class="<?php echo $storeIcon; ?>"></i>
                        <?php echo $storeText; ?>
                    </a>
                    <?php endif; ?>

                    <?php
                    // Verificar si tiene URL externa (sitio web oficial)
                    if (!empty($app['external_url'])):
                        $hasDownload = true;
                    ?>
                    <a href="download_app.php?id=<?php echo $app['id']; ?>&action=external"
                       class="app-external-btn"
                       onclick="trackDownload(<?php echo $app['id']; ?>, 'external')">
                        <i class="fas fa-external-link-alt"></i>
                        Sitio Web
                    </a>
                    <?php endif; ?>

                    <?php if (!$hasDownload): ?>
                    <div style="color: var(--text-secondary); font-style: italic; text-align: center; padding: 0.75rem; font-size: 0.8rem;">
                        <i class="fas fa-info-circle"></i>
                        Próximamente disponible
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <?php endforeach; ?>
    </div>
    <?php endif; ?>
</div>

<script>
function trackDownload(appId, type) {
    // Tracking de descargas
    if (typeof gtag !== 'undefined') {
        gtag('event', 'download', {
            'event_category': 'app',
            'event_label': 'app_' + appId + '_' + type,
            'value': 1
        });
    }

    // Mostrar mensaje de descarga iniciada
    showDownloadMessage(type);
}

function showDownloadMessage(type) {
    const messages = {
        'file': '📱 Descarga iniciada. El archivo APK se descargará automáticamente.',
        'store': '🏪 Redirigiendo a la tienda de aplicaciones...',
        'external': '🌐 Abriendo sitio web oficial...'
    };

    const message = messages[type] || '⬇️ Procesando descarga...';

    // Crear notificación temporal
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #10b981;
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        box-shadow: 0 4px 16px rgba(0,0,0,0.4);
        z-index: 10000;
        animation: slideIn 0.3s ease;
        max-width: 300px;
    `;
    notification.innerHTML = `
        <div style="display: flex; align-items: center; gap: 0.5rem;">
            <i class="fas fa-download"></i>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => notification.remove(), 300);
    }, 4000);
}

// Estilos para animaciones
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);
</script>
