<?php
session_start();
header('Content-Type: application/json');

// Verificar si el usuario está logueado como admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    echo json_encode(['success' => false, 'error' => 'No autorizado']);
    exit;
}

// Configuración MySQL
$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    echo json_encode(['success' => false, 'error' => 'Error de base de datos']);
    exit;
}

if (!isset($_GET['id'])) {
    echo json_encode(['success' => false, 'error' => 'ID requerido']);
    exit;
}

$request_id = (int)$_GET['id'];

try {
    $stmt = $pdo->prepare("SELECT * FROM service_requests WHERE id = ?");
    $stmt->execute([$request_id]);
    $request = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$request) {
        echo json_encode(['success' => false, 'error' => 'Solicitud no encontrada']);
        exit;
    }
    
    // Generar HTML para los detalles
    $html = '
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem; margin-bottom: 2rem;">
        <div style="background: rgba(255, 255, 255, 0.03); padding: 1rem; border-radius: 12px; border: 1px solid rgba(255, 255, 255, 0.08);">
            <h4 style="color: var(--primary-color); margin-bottom: 1rem; display: flex; align-items: center; gap: 0.5rem;">
                <i class="fas fa-user"></i> Información Personal
            </h4>
            <div style="display: flex; flex-direction: column; gap: 0.75rem;">
                <div>
                    <span style="color: var(--text-muted); font-size: 0.8rem; text-transform: uppercase;">Nombre Completo</span>
                    <div style="color: var(--text-primary); font-weight: 500;">' . htmlspecialchars($request['first_name'] . ' ' . $request['last_name']) . '</div>
                </div>
                <div>
                    <span style="color: var(--text-muted); font-size: 0.8rem; text-transform: uppercase;">Email</span>
                    <div style="color: var(--text-primary); font-weight: 500;">' . htmlspecialchars($request['email']) . '</div>
                </div>
                <div>
                    <span style="color: var(--text-muted); font-size: 0.8rem; text-transform: uppercase;">Teléfono</span>
                    <div style="color: var(--text-primary); font-weight: 500;">' . htmlspecialchars($request['phone']) . '</div>
                </div>
            </div>
        </div>
        
        <div style="background: rgba(255, 255, 255, 0.03); padding: 1rem; border-radius: 12px; border: 1px solid rgba(255, 255, 255, 0.08);">
            <h4 style="color: var(--primary-color); margin-bottom: 1rem; display: flex; align-items: center; gap: 0.5rem;">
                <i class="fas fa-cog"></i> Configuración de Servicio
            </h4>
            <div style="display: flex; flex-direction: column; gap: 0.75rem;">
                <div>
                    <span style="color: var(--text-muted); font-size: 0.8rem; text-transform: uppercase;">Tipo de Servicio</span>
                    <div style="color: var(--text-primary); font-weight: 500;">' . ($request['service_type'] === 'trial' ? 'Prueba Gratuita' : 'Compra Directa') . '</div>
                </div>
                <div>
                    <span style="color: var(--text-muted); font-size: 0.8rem; text-transform: uppercase;">Dispositivo</span>
                    <div style="color: var(--text-primary); font-weight: 500;">' . ucfirst(str_replace('_', ' ', $request['device_type'])) . '</div>
                </div>
                <div>
                    <span style="color: var(--text-muted); font-size: 0.8rem; text-transform: uppercase;">Estado</span>
                    <div style="color: var(--text-primary); font-weight: 500;">
                        <span class="request-status status-' . $request['status'] . '">' . ucfirst(str_replace('_', ' ', $request['status'])) . '</span>
                    </div>
                </div>
            </div>
        </div>
    </div>';
    
    // Credenciales
    if ($request['admin_chooses_credentials']) {
        $html .= '
        <div style="background: rgba(255, 255, 255, 0.03); padding: 1rem; border-radius: 12px; border: 1px solid rgba(255, 255, 255, 0.08); margin-bottom: 1.5rem;">
            <h4 style="color: var(--primary-color); margin-bottom: 1rem; display: flex; align-items: center; gap: 0.5rem;">
                <i class="fas fa-key"></i> Credenciales
            </h4>
            <p style="color: var(--text-secondary);">El cliente ha solicitado que el administrador elija las credenciales.</p>
        </div>';
    } else {
        $html .= '
        <div style="background: rgba(255, 255, 255, 0.03); padding: 1rem; border-radius: 12px; border: 1px solid rgba(255, 255, 255, 0.08); margin-bottom: 1.5rem;">
            <h4 style="color: var(--primary-color); margin-bottom: 1rem; display: flex; align-items: center; gap: 0.5rem;">
                <i class="fas fa-key"></i> Credenciales Preferidas
            </h4>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                <div>
                    <span style="color: var(--text-muted); font-size: 0.8rem; text-transform: uppercase;">Usuario Deseado</span>
                    <div style="color: var(--text-primary); font-weight: 500;">' . htmlspecialchars($request['preferred_username'] ?: 'No especificado') . '</div>
                </div>
                <div>
                    <span style="color: var(--text-muted); font-size: 0.8rem; text-transform: uppercase;">Contraseña</span>
                    <div style="color: var(--text-primary); font-weight: 500;">' . ($request['preferred_password'] ? '••••••••' : 'No especificada') . '</div>
                </div>
            </div>
        </div>';
    }
    
    // Información del dispositivo
    if ($request['device_info'] || $request['mac_address'] || $request['serial_key']) {
        $html .= '
        <div style="background: rgba(255, 255, 255, 0.03); padding: 1rem; border-radius: 12px; border: 1px solid rgba(255, 255, 255, 0.08); margin-bottom: 1.5rem;">
            <h4 style="color: var(--primary-color); margin-bottom: 1rem; display: flex; align-items: center; gap: 0.5rem;">
                <i class="fas fa-tv"></i> Información del Dispositivo
            </h4>
            <div style="display: flex; flex-direction: column; gap: 0.75rem;">';
        
        if ($request['device_info']) {
            $html .= '
                <div>
                    <span style="color: var(--text-muted); font-size: 0.8rem; text-transform: uppercase;">Detalles</span>
                    <div style="color: var(--text-primary); font-weight: 500;">' . htmlspecialchars($request['device_info']) . '</div>
                </div>';
        }
        
        if ($request['mac_address']) {
            $html .= '
                <div>
                    <span style="color: var(--text-muted); font-size: 0.8rem; text-transform: uppercase;">Dirección MAC</span>
                    <div style="color: var(--text-primary); font-weight: 500; font-family: monospace;">' . htmlspecialchars($request['mac_address']) . '</div>
                </div>';
        }
        
        if ($request['serial_key']) {
            $html .= '
                <div>
                    <span style="color: var(--text-muted); font-size: 0.8rem; text-transform: uppercase;">Número de Serie</span>
                    <div style="color: var(--text-primary); font-weight: 500; font-family: monospace;">' . htmlspecialchars($request['serial_key']) . '</div>
                </div>';
        }
        
        $html .= '
            </div>
        </div>';
    }
    
    // Notas del administrador
    $html .= '
    <div style="background: rgba(255, 255, 255, 0.03); padding: 1rem; border-radius: 12px; border: 1px solid rgba(255, 255, 255, 0.08); margin-bottom: 1.5rem;">
        <h4 style="color: var(--primary-color); margin-bottom: 1rem; display: flex; align-items: center; gap: 0.5rem;">
            <i class="fas fa-sticky-note"></i> Notas del Administrador
        </h4>
        <form method="POST" style="display: flex; flex-direction: column; gap: 1rem;">
            <input type="hidden" name="action" value="update_status">
            <input type="hidden" name="request_id" value="' . $request['id'] . '">
            <textarea name="admin_notes" placeholder="Agregar notas..." style="padding: 0.75rem; background: var(--gradient-surface); border: 1px solid rgba(255, 255, 255, 0.15); border-radius: 8px; color: var(--text-primary); min-height: 100px; resize: vertical;">' . htmlspecialchars($request['admin_notes'] ?: '') . '</textarea>
            <div style="display: flex; gap: 0.5rem;">
                <select name="status" style="padding: 0.75rem; background: var(--gradient-surface); border: 1px solid rgba(255, 255, 255, 0.15); border-radius: 8px; color: var(--text-primary);">
                    <option value="pending"' . ($request['status'] === 'pending' ? ' selected' : '') . '>Pendiente</option>
                    <option value="in_progress"' . ($request['status'] === 'in_progress' ? ' selected' : '') . '>En progreso</option>
                    <option value="completed"' . ($request['status'] === 'completed' ? ' selected' : '') . '>Completado</option>
                    <option value="cancelled"' . ($request['status'] === 'cancelled' ? ' selected' : '') . '>Cancelado</option>
                </select>
                <button type="submit" style="padding: 0.75rem 1.5rem; background: var(--gradient-primary); color: white; border: none; border-radius: 8px; cursor: pointer; font-weight: 600;">
                    <i class="fas fa-save"></i> Actualizar
                </button>
            </div>
        </form>
    </div>
    
    <div style="display: flex; justify-content: space-between; align-items: center; padding-top: 1rem; border-top: 1px solid rgba(255, 255, 255, 0.1);">
        <div style="color: var(--text-muted); font-size: 0.9rem;">
            <i class="fas fa-calendar"></i> Creado: ' . date('d/m/Y H:i', strtotime($request['created_at'])) . '
        </div>
        <div style="color: var(--text-muted); font-size: 0.9rem;">
            <i class="fas fa-clock"></i> Actualizado: ' . date('d/m/Y H:i', strtotime($request['updated_at'])) . '
        </div>
    </div>';
    
    echo json_encode(['success' => true, 'html' => $html]);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'error' => 'Error al obtener detalles']);
}
?>
