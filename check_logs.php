<?php
session_start();

echo "<h1>Verificador de Logs - RogsMediaTV</h1>";
echo "<style>body { font-family: Arial, sans-serif; margin: 20px; } .log-entry { background: #f5f5f5; padding: 10px; margin: 5px 0; border-left: 4px solid #007bff; } .error { border-left-color: #dc3545; } .success { border-left-color: #28a745; } .info { border-left-color: #17a2b8; }</style>";

// Verificar información de sesión
echo "<h2>Información de Sesión Actual</h2>";
if (isset($_SESSION['user_id'])) {
    echo "<div class='log-entry success'>";
    echo "<strong>✅ Usuario logueado:</strong><br>";
    echo "User ID: " . $_SESSION['user_id'] . "<br>";
    echo "Username: " . ($_SESSION['username'] ?? 'N/A') . "<br>";
    echo "Sesión completa: <pre>" . print_r($_SESSION, true) . "</pre>";
    echo "</div>";
} else {
    echo "<div class='log-entry error'>";
    echo "<strong>❌ Usuario NO logueado</strong><br>";
    echo "Sesión: <pre>" . print_r($_SESSION, true) . "</pre>";
    echo "</div>";
}

// Verificar logs de error de PHP
echo "<h2>Logs de Error de PHP</h2>";

// Intentar leer el log de errores de PHP
$possible_log_paths = [
    ini_get('error_log'),
    '/var/log/php_errors.log',
    '/var/log/apache2/error.log',
    '/var/log/nginx/error.log',
    './error.log',
    '../error.log',
    '/tmp/php_errors.log'
];

$log_found = false;

foreach ($possible_log_paths as $log_path) {
    if ($log_path && file_exists($log_path) && is_readable($log_path)) {
        echo "<h3>Log encontrado: $log_path</h3>";
        $log_found = true;
        
        // Leer las últimas 50 líneas del log
        $lines = file($log_path);
        $recent_lines = array_slice($lines, -50);
        
        echo "<div style='max-height: 400px; overflow-y: auto; background: #000; color: #fff; padding: 10px; font-family: monospace;'>";
        foreach ($recent_lines as $line) {
            $line = htmlspecialchars($line);
            
            // Resaltar líneas relacionadas con nuestro sistema
            if (strpos($line, 'INDEX.PHP') !== false || strpos($line, 'pedido') !== false || strpos($line, 'order') !== false) {
                echo "<div style='background: #333; padding: 2px;'>$line</div>";
            } else {
                echo $line . "<br>";
            }
        }
        echo "</div>";
        break;
    }
}

if (!$log_found) {
    echo "<div class='log-entry info'>";
    echo "<strong>ℹ️ No se encontraron logs de error accesibles</strong><br>";
    echo "Rutas verificadas:<br>";
    foreach ($possible_log_paths as $path) {
        echo "- $path " . (file_exists($path) ? "(existe pero no legible)" : "(no existe)") . "<br>";
    }
    echo "</div>";
}

// Verificar configuración de PHP
echo "<h2>Configuración de PHP Relevante</h2>";
echo "<div class='log-entry info'>";
echo "<strong>Error Reporting:</strong> " . error_reporting() . "<br>";
echo "<strong>Display Errors:</strong> " . (ini_get('display_errors') ? 'ON' : 'OFF') . "<br>";
echo "<strong>Log Errors:</strong> " . (ini_get('log_errors') ? 'ON' : 'OFF') . "<br>";
echo "<strong>Error Log Path:</strong> " . (ini_get('error_log') ?: 'Default') . "<br>";
echo "<strong>Max Execution Time:</strong> " . ini_get('max_execution_time') . " segundos<br>";
echo "<strong>Memory Limit:</strong> " . ini_get('memory_limit') . "<br>";
echo "</div>";

// Verificar conexión a base de datos
echo "<h2>Verificación de Base de Datos</h2>";
$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div class='log-entry success'>";
    echo "<strong>✅ Conexión a base de datos exitosa</strong><br>";
    
    // Verificar tabla orders
    $stmt = $pdo->query("SHOW TABLES LIKE 'orders'");
    if ($stmt->rowCount() > 0) {
        echo "✅ Tabla 'orders' existe<br>";
        
        // Contar pedidos
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM orders");
        $count = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "📊 Total de pedidos: " . $count['count'] . "<br>";
        
        // Último pedido
        $stmt = $pdo->query("SELECT * FROM orders ORDER BY created_at DESC LIMIT 1");
        $last_order = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($last_order) {
            echo "🕒 Último pedido: " . $last_order['title'] . " (" . $last_order['created_at'] . ")<br>";
        }
    } else {
        echo "❌ Tabla 'orders' no existe<br>";
    }
    
    echo "</div>";
    
} catch(PDOException $e) {
    echo "<div class='log-entry error'>";
    echo "<strong>❌ Error de conexión a base de datos:</strong><br>";
    echo $e->getMessage();
    echo "</div>";
}

// Verificar permisos de archivos
echo "<h2>Verificación de Permisos</h2>";
$files_to_check = ['index.php', 'admin.php', 'test_order.php', 'debug_order.php'];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        $perms = fileperms($file);
        $perms_str = substr(sprintf('%o', $perms), -4);
        echo "<div class='log-entry info'>";
        echo "<strong>$file:</strong> Permisos $perms_str " . (is_readable($file) ? "✅ Legible" : "❌ No legible") . " " . (is_writable($file) ? "✅ Escribible" : "❌ No escribible");
        echo "</div>";
    }
}

// Información del servidor
echo "<h2>Información del Servidor</h2>";
echo "<div class='log-entry info'>";
echo "<strong>Servidor:</strong> " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Desconocido') . "<br>";
echo "<strong>PHP Version:</strong> " . phpversion() . "<br>";
echo "<strong>Método de solicitud:</strong> " . $_SERVER['REQUEST_METHOD'] . "<br>";
echo "<strong>User Agent:</strong> " . ($_SERVER['HTTP_USER_AGENT'] ?? 'Desconocido') . "<br>";
echo "<strong>IP del cliente:</strong> " . ($_SERVER['REMOTE_ADDR'] ?? 'Desconocida') . "<br>";
echo "<strong>Hora del servidor:</strong> " . date('Y-m-d H:i:s') . "<br>";
echo "</div>";

// Simular un error para verificar logging
echo "<h2>Test de Logging</h2>";
error_log("CHECK_LOGS.PHP - Test log entry at " . date('Y-m-d H:i:s'));
echo "<div class='log-entry info'>";
echo "✅ Se ha enviado un mensaje de prueba al log de errores";
echo "</div>";

?>

<h2>🔧 Acciones de Debug</h2>
<div style="background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
    <h3>Pasos para debuggear el problema:</h3>
    <ol>
        <li><strong>Verificar logs:</strong> Revisa los logs mostrados arriba para errores relacionados con pedidos</li>
        <li><strong>Probar debug_order.php:</strong> <a href="debug_order.php" target="_blank">Abrir debug_order.php</a> (requiere login)</li>
        <li><strong>Verificar consola del navegador:</strong> Abre F12 en index.php y busca errores JavaScript</li>
        <li><strong>Probar formulario directo:</strong> <a href="test_order.php" target="_blank">Abrir test_order.php</a></li>
    </ol>
    
    <h3>Si el problema persiste:</h3>
    <ul>
        <li>Verifica que estés logueado correctamente en index.php</li>
        <li>Revisa si hay errores de JavaScript en la consola del navegador</li>
        <li>Comprueba que el formulario oculto existe en el HTML de index.php</li>
        <li>Verifica que no hay conflictos con otros scripts JavaScript</li>
    </ul>
</div>

<hr>
<p><a href="index.php">← Volver al índice</a> | <a href="debug_order.php">Debug de pedidos</a> | <a href="test_database.php">Test de BD</a></p>

<script>
console.log('Check logs page loaded');
console.log('Current session status:', <?php echo isset($_SESSION['user_id']) ? '"logged_in"' : '"not_logged_in"'; ?>);
</script>
