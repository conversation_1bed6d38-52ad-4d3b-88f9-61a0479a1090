<?php
/**
 * Pruebas de la API RGS TOOL
 * Script para probar todos los endpoints de la API
 */

session_start();

// Verificar autenticación de admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: ../admin_login.php');
    exit;
}

$base_url = 'http://' . $_SERVER['HTTP_HOST'] . '/api/';

?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 Pruebas API - RGS TOOL</title>
    <style>
        :root {
            --primary-color: #1a1a1a;
            --secondary-color: #2d2d2d;
            --accent-color: #46d347;
            --text-primary: #ffffff;
            --text-secondary: #b0b0b0;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --error-color: #dc3545;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--primary-color);
            color: var(--text-primary);
            line-height: 1.6;
            padding: 2rem;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            color: var(--accent-color);
        }

        .test-section {
            background: var(--secondary-color);
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 1px solid #404040;
        }

        .test-section h2 {
            color: var(--accent-color);
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .test-item {
            background: var(--primary-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            border: 1px solid #404040;
        }

        .test-item h3 {
            color: var(--text-primary);
            margin-bottom: 1rem;
        }

        .test-url {
            background: #0a0a0a;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            font-family: monospace;
            font-size: 0.9rem;
            margin-bottom: 1rem;
            border: 1px solid #404040;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            background: var(--accent-color);
            color: var(--primary-color);
            text-decoration: none;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            margin-right: 1rem;
            margin-bottom: 0.5rem;
        }

        .btn:hover {
            background: #3bc55a;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: var(--secondary-color);
            color: var(--text-primary);
            border: 1px solid #404040;
        }

        .response-area {
            background: #0a0a0a;
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
            border: 1px solid #404040;
            min-height: 200px;
            font-family: monospace;
            font-size: 0.9rem;
            white-space: pre-wrap;
            overflow-x: auto;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }

        .status-success { background: var(--success-color); }
        .status-error { background: var(--error-color); }
        .status-pending { background: var(--warning-color); }

        .info-box {
            background: rgba(70, 211, 71, 0.1);
            border: 1px solid var(--accent-color);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 2rem;
        }

        .code-block {
            background: #0a0a0a;
            border-radius: 8px;
            padding: 1rem;
            font-family: monospace;
            font-size: 0.9rem;
            overflow-x: auto;
            border: 1px solid #404040;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Pruebas API RGS TOOL</h1>
            <p>Prueba todos los endpoints de la API REST</p>
        </div>

        <div class="info-box">
            <h3 style="color: var(--accent-color); margin-bottom: 1rem;">
                <i class="fas fa-info-circle"></i>
                Información de la API
            </h3>
            <p><strong>Base URL:</strong> <code><?php echo $base_url; ?></code></p>
            <p><strong>Formato:</strong> JSON</p>
            <p><strong>CORS:</strong> Habilitado</p>
            <p><strong>Documentación:</strong> <a href="README.md" style="color: var(--accent-color);">README.md</a></p>
        </div>

        <!-- Pruebas de Autenticación -->
        <div class="test-section">
            <h2><i class="fas fa-key"></i> Autenticación</h2>
            
            <div class="test-item">
                <h3>Login de Usuario</h3>
                <div class="test-url">POST <?php echo $base_url; ?>auth/login</div>
                <button class="btn" onclick="testLogin()">
                    <i class="fas fa-play"></i>
                    Probar Login
                </button>
                <div class="response-area" id="login-response">Haz clic en "Probar Login" para ejecutar la prueba...</div>
            </div>
        </div>

        <!-- Pruebas de Contenido -->
        <div class="test-section">
            <h2><i class="fas fa-film"></i> Contenido</h2>
            
            <div class="test-item">
                <h3>Contenido Trending</h3>
                <div class="test-url">GET <?php echo $base_url; ?>content/trending</div>
                <button class="btn" onclick="testTrending()">
                    <i class="fas fa-play"></i>
                    Probar Trending
                </button>
                <div class="response-area" id="trending-response">Haz clic en "Probar Trending" para ejecutar la prueba...</div>
            </div>
            
            <div class="test-item">
                <h3>Búsqueda de Contenido</h3>
                <div class="test-url">GET <?php echo $base_url; ?>content/search?q=avatar</div>
                <button class="btn" onclick="testSearch()">
                    <i class="fas fa-play"></i>
                    Probar Búsqueda
                </button>
                <div class="response-area" id="search-response">Haz clic en "Probar Búsqueda" para ejecutar la prueba...</div>
            </div>
        </div>

        <!-- Pruebas de Estadísticas -->
        <div class="test-section">
            <h2><i class="fas fa-chart-bar"></i> Estadísticas</h2>
            
            <div class="test-item">
                <h3>Estadísticas Generales</h3>
                <div class="test-url">GET <?php echo $base_url; ?>stats</div>
                <button class="btn" onclick="testStats()">
                    <i class="fas fa-play"></i>
                    Probar Estadísticas
                </button>
                <div class="response-area" id="stats-response">Haz clic en "Probar Estadísticas" para ejecutar la prueba...</div>
            </div>
        </div>

        <!-- Pruebas Completas -->
        <div class="test-section">
            <h2><i class="fas fa-rocket"></i> Pruebas Completas</h2>
            
            <button class="btn" onclick="runAllTests()">
                <i class="fas fa-play-circle"></i>
                Ejecutar Todas las Pruebas
            </button>
            
            <button class="btn btn-secondary" onclick="clearAllResponses()">
                <i class="fas fa-eraser"></i>
                Limpiar Respuestas
            </button>
        </div>

        <div style="text-align: center; margin-top: 3rem;">
            <a href="../admin.php" class="btn btn-secondary">
                <i class="fas fa-home"></i>
                Volver al Admin
            </a>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
    <script>
        const baseUrl = '<?php echo $base_url; ?>';

        async function makeRequest(url, options = {}) {
            try {
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                
                const data = await response.json();
                return {
                    status: response.status,
                    data: data
                };
            } catch (error) {
                return {
                    status: 0,
                    data: { error: error.message }
                };
            }
        }

        function displayResponse(elementId, response) {
            const element = document.getElementById(elementId);
            const statusClass = response.status === 200 ? 'status-success' : 'status-error';
            
            element.innerHTML = `
<span class="status-indicator ${statusClass}"></span>Status: ${response.status}

${JSON.stringify(response.data, null, 2)}`;
        }

        async function testLogin() {
            const response = await makeRequest(baseUrl + 'auth/login', {
                method: 'POST',
                body: JSON.stringify({
                    username: 'admin',
                    password: 'admin123'
                })
            });
            displayResponse('login-response', response);
        }

        async function testTrending() {
            const response = await makeRequest(baseUrl + 'content/trending');
            displayResponse('trending-response', response);
        }

        async function testSearch() {
            const response = await makeRequest(baseUrl + 'content/search?q=avatar');
            displayResponse('search-response', response);
        }

        async function testStats() {
            const response = await makeRequest(baseUrl + 'stats');
            displayResponse('stats-response', response);
        }

        async function runAllTests() {
            document.querySelectorAll('.response-area').forEach(area => {
                area.innerHTML = '<span class="status-indicator status-pending"></span>Ejecutando prueba...';
            });

            await testTrending();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testSearch();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testStats();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testLogin();
        }

        function clearAllResponses() {
            document.querySelectorAll('.response-area').forEach(area => {
                area.innerHTML = 'Respuesta limpiada. Haz clic en el botón de prueba para ejecutar...';
            });
        }
    </script>
</body>
</html>
