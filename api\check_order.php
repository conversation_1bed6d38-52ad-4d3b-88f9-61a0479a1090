<?php
/**
 * Verificar el último pedido creado
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');

// Configuración de base de datos
$db_config = [
    'host' => 'localhost',
    'name' => 'u170528143_php',
    'user' => 'u170528143_php',
    'pass' => '&T4v!$=i'
];

try {
    $pdo = new PDO(
        "mysql:host={$db_config['host']};dbname={$db_config['name']};charset=utf8mb4",
        $db_config['user'],
        $db_config['pass'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );

    // Obtener el último pedido
    $stmt = $pdo->query("
        SELECT o.*, u.username 
        FROM orders o 
        LEFT JOIN users u ON o.user_id = u.id 
        ORDER BY o.created_at DESC 
        LIMIT 1
    ");
    $last_order = $stmt->fetch();

    // Obtener pedidos del usuario Casa122
    $stmt = $pdo->prepare("
        SELECT o.*, u.username 
        FROM orders o 
        LEFT JOIN users u ON o.user_id = u.id 
        WHERE u.username = ? 
        ORDER BY o.created_at DESC 
        LIMIT 5
    ");
    $stmt->execute(['Casa122']);
    $casa122_orders = $stmt->fetchAll();

    // Verificar si existe el usuario Casa122
    $stmt = $pdo->prepare("SELECT * FROM users WHERE username = ?");
    $stmt->execute(['Casa122']);
    $casa122_user = $stmt->fetch();

    echo json_encode([
        'success' => true,
        'data' => [
            'last_order' => $last_order,
            'casa122_user' => $casa122_user,
            'casa122_orders' => $casa122_orders,
            'total_casa122_orders' => count($casa122_orders)
        ],
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_PRETTY_PRINT);

} catch(PDOException $e) {
    echo json_encode([
        'success' => false,
        'error' => 'Database error',
        'message' => $e->getMessage()
    ]);
}
?>
