<?php
session_start();

// Incluir configuración de seguridad geográfica
require_once 'security_config.php';

// Verificar acceso geográfico antes de cualquier procesamiento
enforceGeographicSecurity();

// Si ya está logueado como admin, redirigir
if (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true) {
    header('Location: admin.php');
    exit;
}

$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    
    // Credenciales de admin (puedes cambiarlas aquí)
    $admin_users = [
        'admin' => 'Infest84',
        'Infest84' => 'Panama25#'
    ];
    
    if (isset($admin_users[$username]) && $admin_users[$username] === $password) {
        $_SESSION['admin_logged_in'] = true;
        $_SESSION['admin_username'] = $username;
        header('Location: admin.php');
        exit;
    } else {
        $error = 'Credenciales de administrador incorrectas';
    }
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔐 Acceso de Administrador - RogsMediaTV</title>
    
    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🔐</text></svg>">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        :root {
            /* Paleta de colores profesional para soporte técnico */
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #1e293b;
            --dark-bg: #0f172a;
            --darker-bg: #020617;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --accent-color: #10b981;
            --accent-dark: #059669;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --success-color: #10b981;
            --info-color: #06b6d4;
            --border-color: #334155;
            --border-light: #475569;
            --gradient-primary: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            --gradient-secondary: linear-gradient(135deg, #10b981 0%, #059669 100%);
            --gradient-dark: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
            --gradient-bg: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            --shadow-light: 0 2px 8px rgba(0,0,0,0.3);
            --shadow-medium: 0 4px 16px rgba(0,0,0,0.4);
            --shadow-heavy: 0 8px 32px rgba(0,0,0,0.6);
            --border-radius: 12px;
            --border-radius-lg: 16px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--darker-bg);
            color: var(--text-primary);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
            background-image: 
                radial-gradient(circle at 20% 80%, rgba(229, 9, 20, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 193, 7, 0.1) 0%, transparent 50%);
        }

        .admin-login-container {
            background: var(--secondary-color);
            border-radius: 16px;
            padding: 3rem;
            width: 100%;
            max-width: 400px;
            box-shadow: var(--shadow-heavy);
            border: 1px solid var(--border-color);
            position: relative;
            overflow: hidden;
        }

        .admin-login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #ffc107 0%, #ff9800 100%);
        }

        .admin-header {
            text-align: center;
            margin-bottom: 2.5rem;
        }

        .admin-header h1 {
            font-size: 2.2rem;
            font-weight: 700;
            color: #ffc107;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
        }

        .admin-header .shield-icon {
            font-size: 2.5rem;
            color: #ffc107;
        }

        .admin-header p {
            color: var(--text-secondary);
            font-size: 1rem;
        }

        .admin-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: rgba(255, 193, 7, 0.2);
            color: #ffc107;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            margin-top: 1rem;
            border: 1px solid rgba(255, 193, 7, 0.3);
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
            font-weight: 600;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .form-input {
            width: 100%;
            padding: 1rem 1.25rem;
            background: var(--dark-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            color: var(--text-primary);
            font-size: 1rem;
            transition: var(--transition);
        }

        .form-input:focus {
            outline: none;
            border-color: #ffc107;
            box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.1);
        }

        .form-input::placeholder {
            color: var(--text-secondary);
        }

        .submit-btn {
            width: 100%;
            padding: 1rem 1.25rem;
            background: linear-gradient(135deg, #ffc107 0%, #ff9800 100%);
            color: var(--darker-bg);
            border: none;
            border-radius: var(--border-radius);
            font-size: 1rem;
            font-weight: 700;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        .submit-btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }

        .error-message {
            background: rgba(220, 53, 69, 0.1);
            color: var(--error-color);
            border: 1px solid rgba(220, 53, 69, 0.3);
            padding: 1rem;
            border-radius: var(--border-radius);
            margin-bottom: 1.5rem;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .back-link {
            text-align: center;
            margin-top: 2rem;
            padding-top: 1.5rem;
            border-top: 1px solid var(--border-color);
        }

        .back-link a {
            color: var(--text-secondary);
            text-decoration: none;
            font-size: 0.9rem;
            transition: var(--transition);
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .back-link a:hover {
            color: var(--text-primary);
        }

        .security-info {
            background: var(--dark-bg);
            padding: 1.5rem;
            border-radius: var(--border-radius);
            margin-top: 2rem;
            border-left: 4px solid #ffc107;
        }

        .security-info h3 {
            color: #ffc107;
            margin-bottom: 0.75rem;
            font-size: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .security-info p {
            color: var(--text-secondary);
            font-size: 0.85rem;
            line-height: 1.5;
        }

        /* Animaciones */
        .admin-login-container {
            animation: slideUp 0.5s ease;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive */
        @media (max-width: 480px) {
            .admin-login-container {
                padding: 2rem 1.5rem;
                margin: 1rem;
            }

            .admin-header h1 {
                font-size: 1.8rem;
            }
        }
    </style>
</head>
<body>
    <div class="admin-login-container">
        <div class="admin-header">
            <h1>
                <i class="fas fa-shield-alt shield-icon"></i>
                Administrador
            </h1>
            <p>Panel de Control Administrativo</p>
            <div class="admin-badge">
                <i class="fas fa-lock"></i>
                Acceso Restringido
            </div>
        </div>

        <?php if (!empty($error)): ?>
        <div class="error-message">
            <i class="fas fa-exclamation-triangle"></i>
            <?php echo htmlspecialchars($error); ?>
        </div>
        <?php endif; ?>

        <form method="POST" onsubmit="return handleSubmit(this)">
            <div class="form-group">
                <label for="username">
                    <i class="fas fa-user-shield"></i>
                    Usuario Administrador
                </label>
                <input 
                    type="text" 
                    id="username" 
                    name="username" 
                    class="form-input" 
                    placeholder="Ingresa tu usuario de admin"
                    required
                    autocomplete="username"
                    autofocus
                >
            </div>

            <div class="form-group">
                <label for="password">
                    <i class="fas fa-key"></i>
                    Contraseña de Administrador
                </label>
                <input 
                    type="password" 
                    id="password" 
                    name="password" 
                    class="form-input" 
                    placeholder="Ingresa tu contraseña de admin"
                    required
                    autocomplete="current-password"
                >
            </div>

            <button type="submit" class="submit-btn">
                <i class="fas fa-sign-in-alt"></i>
                Acceder al Panel
            </button>
        </form>

        <div class="back-link">
            <a href="index.php">
                <i class="fas fa-arrow-left"></i>
                Volver al sitio principal
            </a>
        </div>

        <div class="security-info">
            <h3>
                <i class="fas fa-info-circle"></i>
                Información de Seguridad
            </h3>
            <p>
                Este es un área restringida para administradores.
                Todas las acciones son registradas y monitoreadas.
            </p>
            <p style="margin-top: 10px; font-size: 0.8rem; opacity: 0.8;">
                <i class="fas fa-globe-americas"></i>
                Acceso geográfico restringido a Latinoamérica y Estados Unidos.
            </p>
        </div>
    </div>

    <script>
        function handleSubmit(form) {
            const submitBtn = form.querySelector('.submit-btn');
            const originalText = submitBtn.innerHTML;
            
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Verificando...';
            submitBtn.disabled = true;
            
            return true;
        }

        // Auto-focus en el primer campo
        document.addEventListener('DOMContentLoaded', function() {
            const usernameField = document.getElementById('username');
            if (usernameField) {
                usernameField.focus();
            }
        });
    </script>
</body>
</html>
