<?php
session_start();
require_once 'config.php';

// Verificar si el usuario está logueado como admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

// Procesar acciones
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'generate_codes':
                $quantity = (int)$_POST['quantity'];
                $list_name = $_POST['list_name'];
                $list_url = $_POST['list_url'];
                $expires_days = (int)$_POST['expires_days'];
                
                $expires_at = $expires_days > 0 ? date('Y-m-d H:i:s', strtotime("+$expires_days days")) : null;
                
                for ($i = 0; $i < $quantity; $i++) {
                    $activation_code = strtoupper(substr(md5(uniqid(rand(), true)), 0, 12));
                    
                    $stmt = $pdo->prepare("INSERT INTO list_activations (user_id, activation_code, list_name, list_url, expires_at, notes) VALUES (?, ?, ?, ?, ?, ?)");
                    $stmt->execute([1, $activation_code, $list_name, $list_url, $expires_at, 'Generado por administrador']);
                }
                
                $success_message = "Se generaron $quantity códigos de activación correctamente";
                break;
                
            case 'activate_code':
                $activation_id = (int)$_POST['activation_id'];
                
                $stmt = $pdo->prepare("UPDATE list_activations SET status = 'active', activated_at = NOW() WHERE id = ?");
                $stmt->execute([$activation_id]);
                
                $success_message = "Código activado correctamente";
                break;
                
            case 'suspend_code':
                $activation_id = (int)$_POST['activation_id'];
                
                $stmt = $pdo->prepare("UPDATE list_activations SET status = 'suspended' WHERE id = ?");
                $stmt->execute([$activation_id]);
                
                $success_message = "Código suspendido correctamente";
                break;
                
            case 'extend_expiry':
                $activation_id = (int)$_POST['activation_id'];
                $extend_days = (int)$_POST['extend_days'];
                
                $stmt = $pdo->prepare("UPDATE list_activations SET expires_at = DATE_ADD(COALESCE(expires_at, NOW()), INTERVAL ? DAY) WHERE id = ?");
                $stmt->execute([$extend_days, $activation_id]);
                
                $success_message = "Fecha de expiración extendida por $extend_days días";
                break;
                
            case 'delete_activation':
                $activation_id = (int)$_POST['activation_id'];
                
                $stmt = $pdo->prepare("DELETE FROM list_activations WHERE id = ?");
                $stmt->execute([$activation_id]);
                
                $success_message = "Activación eliminada correctamente";
                break;
        }
    }
}

// Obtener filtros
$status_filter = $_GET['status'] ?? 'all';
$list_filter = $_GET['list'] ?? 'all';

// Construir consulta
$where_conditions = [];
$params = [];

if ($status_filter !== 'all') {
    $where_conditions[] = "la.status = ?";
    $params[] = $status_filter;
}

if ($list_filter !== 'all') {
    $where_conditions[] = "la.list_name = ?";
    $params[] = $list_filter;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Obtener activaciones
$stmt = $pdo->prepare("
    SELECT la.*, u.username
    FROM list_activations la 
    LEFT JOIN users u ON la.user_id = u.id 
    $where_clause
    ORDER BY 
        CASE 
            WHEN la.status = 'pending' THEN 1
            WHEN la.status = 'active' THEN 2
            WHEN la.status = 'expired' THEN 3
            WHEN la.status = 'suspended' THEN 4
        END,
        la.created_at DESC
");
$stmt->execute($params);
$activations = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Obtener estadísticas
$stats_stmt = $pdo->query("
    SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
        SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active,
        SUM(CASE WHEN status = 'expired' THEN 1 ELSE 0 END) as expired,
        SUM(CASE WHEN status = 'suspended' THEN 1 ELSE 0 END) as suspended,
        SUM(CASE WHEN DATE(created_at) = CURDATE() THEN 1 ELSE 0 END) as today,
        SUM(CASE WHEN expires_at IS NOT NULL AND expires_at < NOW() AND status = 'active' THEN 1 ELSE 0 END) as expired_active
    FROM list_activations
");
$stats = $stats_stmt->fetch(PDO::FETCH_ASSOC);

// Obtener listas únicas para filtros
$lists_stmt = $pdo->query("SELECT DISTINCT list_name FROM list_activations WHERE list_name IS NOT NULL AND list_name != '' ORDER BY list_name");
$lists = $lists_stmt->fetchAll(PDO::FETCH_COLUMN);
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔑 Activación de Listas - Admin Soporte</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #1e293b;
            --dark-bg: #0f172a;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --accent-color: #10b981;
            --support-color: #e91e63;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --success-color: #10b981;
            --border-color: #334155;
            --gradient-bg: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            --border-radius: 12px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gradient-bg);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
        }

        .header {
            background: var(--secondary-color);
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--support-color);
            text-decoration: none;
        }

        .nav-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .nav-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            background: transparent;
            color: var(--text-secondary);
            text-decoration: none;
            border-radius: var(--border-radius);
            transition: var(--transition);
            font-weight: 500;
        }

        .nav-btn:hover {
            background: rgba(255,255,255,0.1);
            color: var(--text-primary);
        }

        .nav-btn.back-btn {
            background: var(--primary-color);
            color: white;
        }

        .main-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem 1.5rem;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .page-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--secondary-color);
            padding: 1.5rem;
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            text-align: center;
            position: relative;
        }

        .stat-card.pending::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--warning-color);
        }

        .stat-card.active::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--success-color);
        }

        .stat-card.expired::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--error-color);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--accent-color);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .filters {
            background: var(--secondary-color);
            padding: 1.5rem;
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            margin-bottom: 2rem;
        }

        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .filter-label {
            font-weight: 500;
            color: var(--text-primary);
        }

        .filter-select {
            padding: 0.75rem;
            background: var(--dark-bg);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            font-size: 0.9rem;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: var(--transition);
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-warning {
            background: var(--warning-color);
            color: white;
        }

        .btn-danger {
            background: var(--error-color);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            opacity: 0.9;
        }

        .generate-form {
            background: var(--secondary-color);
            padding: 2rem;
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            margin-bottom: 2rem;
            display: none;
        }

        .generate-form.active {
            display: block;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .form-label {
            font-weight: 500;
            color: var(--text-primary);
        }

        .form-input, .form-select {
            padding: 0.75rem;
            background: var(--dark-bg);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            font-family: inherit;
            font-size: 0.9rem;
        }

        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.15);
        }

        .activations-container {
            background: var(--secondary-color);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            overflow: hidden;
        }

        .activation-item {
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
            transition: var(--transition);
            position: relative;
        }

        .activation-item:hover {
            background: rgba(255,255,255,0.05);
        }

        .activation-item:last-child {
            border-bottom: none;
        }

        .activation-pending {
            border-left: 4px solid var(--warning-color);
        }

        .activation-active {
            border-left: 4px solid var(--success-color);
        }

        .activation-expired {
            border-left: 4px solid var(--error-color);
        }

        .activation-suspended {
            border-left: 4px solid var(--border-color);
        }

        .activation-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .activation-code {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--text-primary);
            font-family: 'Courier New', monospace;
            background: rgba(255,255,255,0.1);
            padding: 0.5rem 1rem;
            border-radius: var(--border-radius);
            margin-bottom: 0.5rem;
        }

        .activation-meta {
            display: flex;
            gap: 1rem;
            color: var(--text-secondary);
            font-size: 0.9rem;
            flex-wrap: wrap;
        }

        .status-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-pending { 
            background: rgba(245, 158, 11, 0.2); 
            color: #f59e0b; 
        }

        .status-active { 
            background: rgba(16, 185, 129, 0.2); 
            color: #10b981; 
        }

        .status-expired { 
            background: rgba(239, 68, 68, 0.2); 
            color: #ef4444; 
        }

        .status-suspended { 
            background: rgba(107, 114, 128, 0.2); 
            color: #6b7280; 
        }

        .activation-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
            margin-top: 1rem;
        }

        .activation-details {
            background: rgba(255,255,255,0.05);
            padding: 1rem;
            border-radius: var(--border-radius);
            margin: 1rem 0;
        }

        .detail-row {
            display: flex;
            margin-bottom: 0.5rem;
        }

        .detail-label {
            font-weight: 500;
            color: var(--text-primary);
            min-width: 120px;
        }

        .detail-value {
            color: var(--text-secondary);
        }

        .success-message {
            background: rgba(16, 185, 129, 0.2);
            color: var(--success-color);
            padding: 1rem;
            border-radius: var(--border-radius);
            margin-bottom: 1rem;
            border: 1px solid rgba(16, 185, 129, 0.3);
        }

        .copy-btn {
            background: transparent;
            border: 1px solid var(--border-color);
            color: var(--text-secondary);
            padding: 0.3rem 0.6rem;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.8rem;
            margin-left: 0.5rem;
            transition: var(--transition);
        }

        .copy-btn:hover {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        @media (max-width: 768px) {
            .page-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .activation-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .activation-actions {
                width: 100%;
                justify-content: flex-start;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-container">
            <a href="activations_admin.php" class="logo">
                <i class="fas fa-key"></i>
                <span>Activación de Listas</span>
            </a>
            
            <div class="nav-buttons">
                <a href="admin2.php" class="nav-btn back-btn">
                    <i class="fas fa-arrow-left"></i>
                    <span>Volver</span>
                </a>
                <a href="admin.php" class="nav-btn">
                    <i class="fas fa-cog"></i>
                    <span>Admin</span>
                </a>
            </div>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-key"></i>
                Activación de Listas M3U
            </h1>
            
            <button onclick="toggleGenerateForm()" class="btn btn-success">
                <i class="fas fa-plus"></i>
                Generar Códigos
            </button>
        </div>

        <?php if (isset($success_message)): ?>
        <div class="success-message">
            <i class="fas fa-check-circle"></i>
            <?php echo htmlspecialchars($success_message); ?>
        </div>
        <?php endif; ?>

        <!-- Estadísticas -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['total']; ?></div>
                <div class="stat-label">Total Códigos</div>
            </div>
            <div class="stat-card pending">
                <div class="stat-number"><?php echo $stats['pending']; ?></div>
                <div class="stat-label">Pendientes</div>
            </div>
            <div class="stat-card active">
                <div class="stat-number"><?php echo $stats['active']; ?></div>
                <div class="stat-label">Activos</div>
            </div>
            <div class="stat-card expired">
                <div class="stat-number"><?php echo $stats['expired']; ?></div>
                <div class="stat-label">Expirados</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['suspended']; ?></div>
                <div class="stat-label">Suspendidos</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['today']; ?></div>
                <div class="stat-label">Hoy</div>
            </div>
            <div class="stat-card expired">
                <div class="stat-number"><?php echo $stats['expired_active']; ?></div>
                <div class="stat-label">Vencidos Activos</div>
            </div>
        </div>

        <!-- Formulario para Generar Códigos -->
        <div id="generateForm" class="generate-form">
            <h2 style="margin-bottom: 1.5rem; color: var(--text-primary);">
                <i class="fas fa-key"></i>
                Generar Códigos de Activación
            </h2>

            <form method="POST">
                <input type="hidden" name="action" value="generate_codes">

                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">Cantidad de Códigos</label>
                        <input type="number" name="quantity" class="form-input" required min="1" max="100" value="10" placeholder="10">
                    </div>

                    <div class="form-group">
                        <label class="form-label">Nombre de la Lista</label>
                        <input type="text" name="list_name" class="form-input" required placeholder="Ej: Lista Premium IPTV">
                    </div>

                    <div class="form-group">
                        <label class="form-label">URL de la Lista</label>
                        <input type="url" name="list_url" class="form-input" placeholder="https://ejemplo.com/lista.m3u">
                    </div>

                    <div class="form-group">
                        <label class="form-label">Días de Expiración</label>
                        <select name="expires_days" class="form-select">
                            <option value="0">Sin expiración</option>
                            <option value="7">7 días</option>
                            <option value="30" selected>30 días</option>
                            <option value="90">90 días</option>
                            <option value="365">1 año</option>
                        </select>
                    </div>
                </div>

                <div style="display: flex; gap: 1rem;">
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-key"></i>
                        Generar Códigos
                    </button>
                    <button type="button" onclick="toggleGenerateForm()" class="btn btn-warning">
                        <i class="fas fa-times"></i>
                        Cancelar
                    </button>
                </div>
            </form>
        </div>

        <!-- Filtros -->
        <div class="filters">
            <form method="GET" class="filters-grid">
                <div class="filter-group">
                    <label class="filter-label">Estado</label>
                    <select name="status" class="filter-select" onchange="this.form.submit()">
                        <option value="all" <?php echo $status_filter === 'all' ? 'selected' : ''; ?>>Todos</option>
                        <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>Pendientes</option>
                        <option value="active" <?php echo $status_filter === 'active' ? 'selected' : ''; ?>>Activos</option>
                        <option value="expired" <?php echo $status_filter === 'expired' ? 'selected' : ''; ?>>Expirados</option>
                        <option value="suspended" <?php echo $status_filter === 'suspended' ? 'selected' : ''; ?>>Suspendidos</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label class="filter-label">Lista</label>
                    <select name="list" class="filter-select" onchange="this.form.submit()">
                        <option value="all" <?php echo $list_filter === 'all' ? 'selected' : ''; ?>>Todas</option>
                        <?php foreach ($lists as $list): ?>
                        <option value="<?php echo htmlspecialchars($list); ?>" <?php echo $list_filter === $list ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($list); ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </form>
        </div>

        <!-- Lista de Activaciones -->
        <div class="activations-container">
            <?php if (empty($activations)): ?>
            <div class="activation-item" style="text-align: center; color: var(--text-secondary);">
                <i class="fas fa-key" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                <p>No hay códigos de activación que coincidan con los filtros seleccionados</p>
            </div>
            <?php else: ?>
                <?php foreach ($activations as $activation): ?>
                <div class="activation-item activation-<?php echo $activation['status']; ?>">
                    <div class="activation-header">
                        <div>
                            <div class="activation-code">
                                🔑 <?php echo htmlspecialchars($activation['activation_code']); ?>
                                <button class="copy-btn" onclick="copyToClipboard('<?php echo $activation['activation_code']; ?>')">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                            <div class="activation-meta">
                                <span><i class="fas fa-user"></i> <?php echo htmlspecialchars($activation['username'] ?? 'Sistema'); ?></span>
                                <span><i class="fas fa-calendar"></i> <?php echo date('d/m/Y H:i', strtotime($activation['created_at'])); ?></span>
                                <?php if ($activation['activated_at']): ?>
                                <span><i class="fas fa-check"></i> Activado: <?php echo date('d/m/Y H:i', strtotime($activation['activated_at'])); ?></span>
                                <?php endif; ?>
                                <?php if ($activation['expires_at']): ?>
                                <span><i class="fas fa-clock"></i> Expira: <?php echo date('d/m/Y H:i', strtotime($activation['expires_at'])); ?></span>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div>
                            <span class="status-badge status-<?php echo $activation['status']; ?>">
                                <?php
                                $status_labels = [
                                    'pending' => 'Pendiente',
                                    'active' => 'Activo',
                                    'expired' => 'Expirado',
                                    'suspended' => 'Suspendido'
                                ];
                                echo $status_labels[$activation['status']] ?? $activation['status'];
                                ?>
                            </span>
                        </div>
                    </div>

                    <div class="activation-details">
                        <?php if ($activation['list_name']): ?>
                        <div class="detail-row">
                            <div class="detail-label">Lista:</div>
                            <div class="detail-value"><?php echo htmlspecialchars($activation['list_name']); ?></div>
                        </div>
                        <?php endif; ?>

                        <?php if ($activation['list_url']): ?>
                        <div class="detail-row">
                            <div class="detail-label">URL:</div>
                            <div class="detail-value">
                                <a href="<?php echo htmlspecialchars($activation['list_url']); ?>" target="_blank" style="color: var(--accent-color);">
                                    <?php echo htmlspecialchars($activation['list_url']); ?>
                                </a>
                            </div>
                        </div>
                        <?php endif; ?>

                        <?php if ($activation['expires_at']): ?>
                        <div class="detail-row">
                            <div class="detail-label">Expiración:</div>
                            <div class="detail-value">
                                <?php
                                $expires = strtotime($activation['expires_at']);
                                $now = time();
                                $diff = $expires - $now;

                                if ($diff > 0) {
                                    $days = floor($diff / (60 * 60 * 24));
                                    echo date('d/m/Y H:i', $expires) . " ($days días restantes)";
                                } else {
                                    echo date('d/m/Y H:i', $expires) . " (EXPIRADO)";
                                }
                                ?>
                            </div>
                        </div>
                        <?php endif; ?>

                        <?php if ($activation['notes']): ?>
                        <div class="detail-row">
                            <div class="detail-label">Notas:</div>
                            <div class="detail-value"><?php echo htmlspecialchars($activation['notes']); ?></div>
                        </div>
                        <?php endif; ?>
                    </div>

                    <div class="activation-actions">
                        <?php if ($activation['status'] === 'pending'): ?>
                        <form method="POST" style="display: inline;">
                            <input type="hidden" name="action" value="activate_code">
                            <input type="hidden" name="activation_id" value="<?php echo $activation['id']; ?>">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-play"></i>
                                Activar
                            </button>
                        </form>
                        <?php endif; ?>

                        <?php if ($activation['status'] === 'active'): ?>
                        <form method="POST" style="display: inline;">
                            <input type="hidden" name="action" value="suspend_code">
                            <input type="hidden" name="activation_id" value="<?php echo $activation['id']; ?>">
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-pause"></i>
                                Suspender
                            </button>
                        </form>

                        <button onclick="showExtendForm(<?php echo $activation['id']; ?>)" class="btn btn-primary">
                            <i class="fas fa-calendar-plus"></i>
                            Extender
                        </button>
                        <?php endif; ?>

                        <?php if ($activation['status'] === 'suspended'): ?>
                        <form method="POST" style="display: inline;">
                            <input type="hidden" name="action" value="activate_code">
                            <input type="hidden" name="activation_id" value="<?php echo $activation['id']; ?>">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-play"></i>
                                Reactivar
                            </button>
                        </form>
                        <?php endif; ?>

                        <form method="POST" style="display: inline;">
                            <input type="hidden" name="action" value="delete_activation">
                            <input type="hidden" name="activation_id" value="<?php echo $activation['id']; ?>">
                            <button type="submit" class="btn btn-danger"
                                    onclick="return confirm('¿Estás seguro de que quieres eliminar este código de activación?')">
                                <i class="fas fa-trash"></i>
                                Eliminar
                            </button>
                        </form>
                    </div>
                </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </main>

    <!-- Modal para extender expiración -->
    <div id="extendModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); z-index: 1000;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: var(--secondary-color); padding: 2rem; border-radius: var(--border-radius); width: 90%; max-width: 400px;">
            <h3 style="margin-bottom: 1rem; color: var(--text-primary);">Extender Expiración</h3>
            <form method="POST">
                <input type="hidden" name="action" value="extend_expiry">
                <input type="hidden" name="activation_id" id="extendActivationId">
                <div class="form-group" style="margin-bottom: 1.5rem;">
                    <label class="form-label">Días a extender</label>
                    <select name="extend_days" class="form-select" required>
                        <option value="7">7 días</option>
                        <option value="30" selected>30 días</option>
                        <option value="90">90 días</option>
                        <option value="365">1 año</option>
                    </select>
                </div>
                <div style="display: flex; gap: 1rem;">
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-calendar-plus"></i>
                        Extender
                    </button>
                    <button type="button" onclick="closeExtendModal()" class="btn btn-warning">
                        <i class="fas fa-times"></i>
                        Cancelar
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function toggleGenerateForm() {
            const form = document.getElementById('generateForm');
            form.classList.toggle('active');

            if (form.classList.contains('active')) {
                form.scrollIntoView({ behavior: 'smooth' });
                const firstInput = form.querySelector('input[type="number"]');
                if (firstInput) {
                    setTimeout(() => firstInput.focus(), 300);
                }
            }
        }

        function showExtendForm(activationId) {
            document.getElementById('extendActivationId').value = activationId;
            document.getElementById('extendModal').style.display = 'block';
        }

        function closeExtendModal() {
            document.getElementById('extendModal').style.display = 'none';
        }

        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                // Mostrar mensaje de éxito
                const btn = event.target.closest('.copy-btn');
                const originalIcon = btn.innerHTML;
                btn.innerHTML = '<i class="fas fa-check"></i>';
                btn.style.background = 'var(--success-color)';
                btn.style.color = 'white';

                setTimeout(() => {
                    btn.innerHTML = originalIcon;
                    btn.style.background = 'transparent';
                    btn.style.color = 'var(--text-secondary)';
                }, 2000);
            }).catch(function(err) {
                console.error('Error al copiar: ', err);
            });
        }

        // Cerrar modal al hacer clic fuera
        window.onclick = function(event) {
            const extendModal = document.getElementById('extendModal');
            if (event.target === extendModal) {
                closeExtendModal();
            }
        }

        // Auto-refresh cada 60 segundos
        setInterval(function() {
            if (document.hidden) return;

            const activeElement = document.activeElement;
            if (activeElement.tagName !== 'INPUT' && activeElement.tagName !== 'SELECT') {
                location.reload();
            }
        }, 60000);

        // Confirmación para acciones críticas
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', function(e) {
                const action = this.querySelector('input[name="action"]')?.value;
                if (action === 'delete_activation') {
                    if (!confirm('¿Estás seguro de que quieres eliminar este código de activación? Esta acción no se puede deshacer.')) {
                        e.preventDefault();
                    }
                } else if (action === 'suspend_code') {
                    if (!confirm('¿Estás seguro de que quieres suspender este código de activación?')) {
                        e.preventDefault();
                    }
                }
            });
        });

        // Resaltar códigos que expiran pronto
        document.querySelectorAll('.activation-active').forEach(item => {
            const expiryText = item.querySelector('.activation-meta span:last-child');
            if (expiryText && expiryText.textContent.includes('Expira:')) {
                const match = expiryText.textContent.match(/(\d+) días restantes/);
                if (match) {
                    const daysLeft = parseInt(match[1]);
                    if (daysLeft <= 7) {
                        item.style.borderLeftColor = 'var(--error-color)';
                        item.style.background = 'rgba(239, 68, 68, 0.05)';
                    } else if (daysLeft <= 30) {
                        item.style.borderLeftColor = 'var(--warning-color)';
                        item.style.background = 'rgba(245, 158, 11, 0.05)';
                    }
                }
            }
        });
    </script>
</body>
</html>
