# RGS TOOL API - Configuración Apache
# Configuración para API REST con rutas limpias

RewriteEngine On

# Habilitar CORS para todas las requests
Header always set Access-Control-Allow-Origin "*"
Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With, X-API-Key"
Header always set Access-Control-Max-Age "3600"

# Manejar preflight requests (OPTIONS)
RewriteCond %{REQUEST_METHOD} OPTIONS
RewriteRule ^(.*)$ index.php [QSA,L]

# Redireccionar todas las requests a index.php
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]

# Configuración de seguridad
<Files "config.php">
    Order Deny,Allow
    Deny from all
</Files>

<Files "*.log">
    Order Deny,Allow
    Deny from all
</Files>

# Configuración de cache para respuestas JSON
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType application/json "access plus 5 minutes"
</IfModule>

# Configuración de compresión
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE application/json
    AddOutputFilterByType DEFLATE text/plain
</IfModule>

# Configuración de tipos MIME
AddType application/json .json

# Deshabilitar listado de directorios
Options -Indexes

# Configuración de límites
<IfModule mod_php.c>
    php_value max_execution_time 30
    php_value memory_limit 128M
    php_value post_max_size 10M
    php_value upload_max_filesize 10M
</IfModule>
