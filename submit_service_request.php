<?php
session_start();
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Manejar preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Configuración MySQL
$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Error de conexión a la base de datos',
        'details' => 'No se pudo conectar al servidor de base de datos'
    ]);
    exit;
}

// Verificar que sea una petición POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'error' => 'Método no permitido',
        'details' => 'Este endpoint solo acepta peticiones POST',
        'received_method' => $_SERVER['REQUEST_METHOD']
    ]);
    exit;
}

// Obtener datos del formulario
$input = null;

// Si hay datos en $_POST (FormData), usarlos
if (!empty($_POST)) {
    $input = $_POST;
    error_log("Usando datos de \$_POST (FormData)");
} else {
    // Si no, intentar obtener datos JSON
    $json_input = file_get_contents('php://input');
    if ($json_input) {
        $input = json_decode($json_input, true);
        error_log("Usando datos JSON");
    }
}

// Si aún no hay datos, error
if (!$input) {
    echo json_encode([
        'success' => false,
        'error' => 'No se recibieron datos del formulario'
    ]);
    exit;
}

// Validar campos requeridos
$required_fields = ['firstName', 'lastName', 'email', 'phone', 'service_type', 'device_type'];
foreach ($required_fields as $field) {
    if (!isset($input[$field]) || empty(trim($input[$field]))) {
        echo json_encode(['error' => "Campo requerido: $field"]);
        exit;
    }
}

// Validar email
if (!filter_var($input['email'], FILTER_VALIDATE_EMAIL)) {
    echo json_encode(['error' => 'Email inválido']);
    exit;
}

// Validar términos aceptados
if (!isset($input['acceptTerms']) || ($input['acceptTerms'] !== 'on' && $input['acceptTerms'] !== true && $input['acceptTerms'] !== 1)) {
    echo json_encode(['error' => 'Debes aceptar los términos y condiciones']);
    exit;
}

// Crear tablas si no existen
$pdo->exec("CREATE TABLE IF NOT EXISTS service_requests (
    id INT AUTO_INCREMENT PRIMARY KEY,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    preferred_username VARCHAR(50),
    preferred_password VARCHAR(100),
    admin_chooses_credentials BOOLEAN DEFAULT FALSE,
    service_type ENUM('trial', 'direct_purchase', 'renewal', 'reseller_activation', 'reseller_renewal') NOT NULL,
    device_type ENUM('android', 'ios', 'smart_tv_samsung', 'smart_tv_lg', 'smart_tv_other', 'pc_windows', 'pc_mac', 'other') NOT NULL,
    device_info TEXT,
    mac_address VARCHAR(17),
    serial_key VARCHAR(100),
    terms_accepted BOOLEAN DEFAULT FALSE,
    status ENUM('pending', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending',
    admin_notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    assigned_to INT,
    client_notified BOOLEAN DEFAULT FALSE,
    admin_notified BOOLEAN DEFAULT TRUE,
    INDEX idx_status (status),
    INDEX idx_device_type (device_type),
    INDEX idx_created_at (created_at),
    INDEX idx_email (email),
    platform_id VARCHAR(100),
    renewal_period ENUM('1_month', '3_months', '6_months', '1_year'),
    wants_current_promo BOOLEAN DEFAULT FALSE,
    payment_proof_url VARCHAR(500),
    is_reseller BOOLEAN DEFAULT FALSE,
    reseller_type ENUM('new', 'renewal'),
    reseller_info JSON,
    INDEX idx_platform_id (platform_id),
    INDEX idx_renewal_period (renewal_period),
    INDEX idx_is_reseller (is_reseller)
)");

$pdo->exec("CREATE TABLE IF NOT EXISTS service_communications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    request_id INT NOT NULL,
    sender_type ENUM('client', 'admin') NOT NULL,
    sender_name VARCHAR(100) NOT NULL,
    message TEXT NOT NULL,
    attachment_url VARCHAR(500),
    is_read BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_request_id (request_id),
    INDEX idx_sender_type (sender_type),
    INDEX idx_created_at (created_at),
    INDEX idx_is_read (is_read)
)");

// Crear tabla de aplicaciones disponibles
$pdo->exec("CREATE TABLE IF NOT EXISTS available_apps (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    device_type ENUM('android', 'ios', 'smart_tv_samsung', 'smart_tv_lg', 'smart_tv_other', 'pc_windows', 'pc_mac') NOT NULL,
    download_url VARCHAR(500),
    installation_guide TEXT,
    requirements TEXT,
    version VARCHAR(20),
    file_size VARCHAR(20),
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_device_type (device_type),
    INDEX idx_is_active (is_active)
)");

// Insertar aplicaciones de ejemplo si la tabla está vacía
$count_stmt = $pdo->query("SELECT COUNT(*) FROM available_apps");
if ($count_stmt->fetchColumn() == 0) {
    $apps = [
        // Android Apps
        ['IPTV Pro', 'android', 'https://play.google.com/store/apps/details?id=ru.iptvremote.android.iptv', 'Descarga desde Google Play Store y configura con tus credenciales', 'Android 5.0+', '2.1.5', '15MB'],
        ['GSE Smart IPTV', 'android', 'https://play.google.com/store/apps/details?id=com.gsetech.smartiptv2', 'Descarga desde Google Play Store, muy fácil de usar', 'Android 4.4+', '7.5.2', '25MB'],
        ['IPTV Smarters Pro', 'android', 'https://play.google.com/store/apps/details?id=com.nst.iptvsmarterstvbox', 'Aplicación profesional con muchas funciones', 'Android 4.1+', '3.0.9', '20MB'],
        ['TiviMate', 'android', 'https://play.google.com/store/apps/details?id=ar.tvplayer.tv', 'Interfaz moderna y elegante para IPTV', 'Android 5.0+', '4.6.0', '18MB'],

        // iOS Apps
        ['GSE Smart IPTV', 'ios', 'https://apps.apple.com/app/gse-smart-iptv/id1028734023', 'Descarga desde App Store, compatible con iPhone y iPad', 'iOS 12.0+', '7.5.2', '30MB'],
        ['IPTV Player', 'ios', 'https://apps.apple.com/app/iptv-player/id1112017770', 'Reproductor IPTV simple y efectivo', 'iOS 11.0+', '2.1.1', '18MB'],
        ['nPlayer', 'ios', 'https://apps.apple.com/app/nplayer/id1116905928', 'Reproductor multimedia avanzado con soporte IPTV', 'iOS 13.0+', '1.9.7', '45MB'],

        // Smart TV Samsung Apps
        ['Smart IPTV', 'smart_tv_samsung', '', 'Buscar "Smart IPTV" en Samsung Apps Store. Costo: $5.99 una sola vez', 'Tizen 2.4+', '1.7.7', '12MB'],
        ['SS IPTV', 'smart_tv_samsung', '', 'Buscar "SS IPTV" en Samsung Apps Store. Aplicación gratuita', 'Tizen 2.3+', '0.8.32', '8MB'],
        ['IPTV', 'smart_tv_samsung', '', 'Buscar "IPTV" en Samsung Apps Store. Aplicación simple y gratuita', 'Tizen 2.4+', '6.1.1', '10MB'],

        // Smart TV LG Apps
        ['Smart IPTV', 'smart_tv_lg', '', 'Buscar "Smart IPTV" en LG Content Store. Costo: $5.99 una sola vez', 'webOS 1.0+', '1.7.7', '12MB'],
        ['SS IPTV', 'smart_tv_lg', '', 'Buscar "SS IPTV" en LG Content Store. Aplicación gratuita', 'webOS 1.0+', '0.8.32', '8MB'],
        ['IPTV', 'smart_tv_lg', '', 'Buscar "IPTV" en LG Content Store. Aplicación simple y gratuita', 'webOS 1.0+', '6.1.1', '10MB'],

        // Smart TV Other Apps
        ['Smart IPTV', 'smart_tv_other', '', 'Buscar en la tienda de aplicaciones de tu Smart TV', 'Varía según TV', '1.7.7', '12MB'],
        ['SS IPTV', 'smart_tv_other', '', 'Aplicación gratuita disponible en la mayoría de Smart TVs', 'Varía según TV', '0.8.32', '8MB'],

        // PC Windows Apps
        ['VLC Media Player', 'pc_windows', 'https://www.videolan.org/vlc/', 'Descarga gratuita desde sitio oficial. Abre listas M3U directamente', 'Windows 7+', '3.0.18', '40MB'],
        ['Kodi', 'pc_windows', 'https://kodi.tv/download/', 'Centro multimedia completo con addon PVR IPTV Simple Client', 'Windows 8+', '20.2', '60MB'],
        ['IPTV Player', 'pc_windows', 'https://www.microsoft.com/store/apps/9nblggh5l7d3', 'Aplicación de Microsoft Store específica para IPTV', 'Windows 10+', '2.1.0', '25MB'],

        // PC Mac Apps
        ['VLC Media Player', 'pc_mac', 'https://www.videolan.org/vlc/', 'Descarga gratuita desde sitio oficial. Abre listas M3U directamente', 'macOS 10.10+', '3.0.18', '45MB'],
        ['IINA', 'pc_mac', 'https://iina.io/', 'Reproductor moderno para macOS con soporte IPTV', 'macOS 10.11+', '1.3.1', '50MB'],
        ['Kodi', 'pc_mac', 'https://kodi.tv/download/', 'Centro multimedia completo con addon PVR IPTV Simple Client', 'macOS 10.13+', '20.2', '65MB']
    ];

    $stmt = $pdo->prepare("INSERT INTO available_apps (name, device_type, download_url, installation_guide, requirements, version, file_size) VALUES (?, ?, ?, ?, ?, ?, ?)");

    foreach ($apps as $app) {
        try {
            $stmt->execute($app);
        } catch (Exception $e) {
            // Continuar si hay error en una app específica
        }
    }
}

try {
    // Procesar campos adicionales según el tipo de servicio
    $platform_id = isset($input['platformId']) ? trim($input['platformId']) : null;
    $renewal_period = isset($input['renewalPeriod']) ? $input['renewalPeriod'] : null;
    $wants_current_promo = (isset($input['wantsCurrentPromo']) && ($input['wantsCurrentPromo'] === '1' || $input['wantsCurrentPromo'] === 1 || $input['wantsCurrentPromo'] === true)) ? 1 : 0;
    $is_reseller = (isset($input['isReseller']) && ($input['isReseller'] === '1' || $input['isReseller'] === 1 || $input['isReseller'] === true)) ? 1 : 0;
    $reseller_type = isset($input['resellerType']) ? $input['resellerType'] : null;

    // Información adicional del revendedor
    $reseller_info = null;
    if ($is_reseller) {
        $reseller_info = json_encode([
            'business_experience' => $input['businessExperience'] ?? '',
            'expected_clients' => $input['expectedClients'] ?? '',
            'marketing_channels' => isset($input['marketingChannels']) ? json_decode($input['marketingChannels'], true) : [],
            'additional_info' => $input['additionalInfo'] ?? ''
        ]);
    }

    // Manejar subida de archivo de comprobante
    $payment_proof_url = null;
    $file_upload_error = null;

    if (isset($_FILES['paymentProof'])) {
        $file = $_FILES['paymentProof'];

        // Verificar errores de subida
        switch ($file['error']) {
            case UPLOAD_ERR_OK:
                // Sin errores, continuar procesamiento
                break;
            case UPLOAD_ERR_INI_SIZE:
            case UPLOAD_ERR_FORM_SIZE:
                $file_upload_error = 'El archivo es demasiado grande. Máximo permitido: ' . ini_get('upload_max_filesize');
                break;
            case UPLOAD_ERR_PARTIAL:
                $file_upload_error = 'El archivo se subió parcialmente. Intenta nuevamente.';
                break;
            case UPLOAD_ERR_NO_FILE:
                // No hay archivo, continuar sin error
                break;
            case UPLOAD_ERR_NO_TMP_DIR:
                $file_upload_error = 'Error del servidor: directorio temporal no encontrado.';
                break;
            case UPLOAD_ERR_CANT_WRITE:
                $file_upload_error = 'Error del servidor: no se puede escribir el archivo.';
                break;
            default:
                $file_upload_error = 'Error desconocido al subir el archivo.';
                break;
        }

        // Si hay error de subida, devolver error
        if ($file_upload_error) {
            echo json_encode([
                'success' => false,
                'error' => $file_upload_error
            ]);
            exit;
        }

        // Si hay archivo y no hay errores, procesarlo
        if ($file['error'] === UPLOAD_ERR_OK && $file['size'] > 0) {
            // Verificar tipo de archivo
            $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'application/pdf'];
            $file_type = $file['type'];
            $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
            $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'pdf'];

            if (!in_array($file_type, $allowed_types) || !in_array($file_extension, $allowed_extensions)) {
                echo json_encode([
                    'success' => false,
                    'error' => 'Tipo de archivo no permitido. Solo se permiten: JPG, PNG, GIF, PDF'
                ]);
                exit;
            }

            // Verificar tamaño (máximo 5MB)
            $max_size = 5 * 1024 * 1024; // 5MB
            if ($file['size'] > $max_size) {
                echo json_encode([
                    'success' => false,
                    'error' => 'El archivo es demasiado grande. Máximo 5MB permitido.'
                ]);
                exit;
            }

            // Crear directorio si no existe
            $upload_dir = 'uploads/payment_proofs/';
            if (!file_exists($upload_dir)) {
                if (!mkdir($upload_dir, 0755, true)) {
                    echo json_encode([
                        'success' => false,
                        'error' => 'Error del servidor: no se puede crear el directorio de uploads.'
                    ]);
                    exit;
                }
            }

            // Verificar que el directorio sea escribible
            if (!is_writable($upload_dir)) {
                echo json_encode([
                    'success' => false,
                    'error' => 'Error del servidor: directorio de uploads no escribible.'
                ]);
                exit;
            }

            // Generar nombre único para el archivo
            $file_name = 'payment_' . time() . '_' . uniqid() . '.' . $file_extension;
            $file_path = $upload_dir . $file_name;

            // Mover archivo subido
            if (move_uploaded_file($file['tmp_name'], $file_path)) {
                $payment_proof_url = $file_path;
                error_log("Archivo subido exitosamente: " . $file_path);
            } else {
                echo json_encode([
                    'success' => false,
                    'error' => 'Error al guardar el archivo en el servidor.'
                ]);
                exit;
            }
        }
    }

    // Preparar datos para insertar (incluyendo campos extendidos)
    $data = [
        'first_name' => trim($input['firstName']),
        'last_name' => trim($input['lastName']),
        'email' => trim($input['email']),
        'phone' => trim($input['phone']),
        'preferred_username' => isset($input['preferredUsername']) ? trim($input['preferredUsername']) : null,
        'preferred_password' => isset($input['preferredPassword']) ? trim($input['preferredPassword']) : null,
        'admin_chooses_credentials' => isset($input['adminChoosesCredentials']) ? 1 : 0,
        'service_type' => $input['service_type'],
        'device_type' => $input['device_type'],
        'device_info' => isset($input['deviceDetails']) ? trim($input['deviceDetails']) : null,
        'mac_address' => isset($input['macAddress']) ? trim($input['macAddress']) : null,
        'serial_key' => isset($input['serialKey']) ? trim($input['serialKey']) : null,
        'platform_id' => $platform_id,
        'renewal_period' => $renewal_period,
        'wants_current_promo' => $wants_current_promo,
        'payment_proof_url' => $payment_proof_url,
        'is_reseller' => $is_reseller,
        'reseller_type' => $reseller_type,
        'reseller_info' => $reseller_info,
        'terms_accepted' => true
    ];

    // Insertar solicitud de servicio con campos extendidos
    $stmt = $pdo->prepare("
        INSERT INTO service_requests (
            first_name, last_name, email, phone, preferred_username, preferred_password,
            admin_chooses_credentials, service_type, device_type, device_info,
            mac_address, serial_key, platform_id, renewal_period, wants_current_promo,
            payment_proof_url, is_reseller, reseller_type, reseller_info, terms_accepted
        ) VALUES (
            :first_name, :last_name, :email, :phone, :preferred_username, :preferred_password,
            :admin_chooses_credentials, :service_type, :device_type, :device_info,
            :mac_address, :serial_key, :platform_id, :renewal_period, :wants_current_promo,
            :payment_proof_url, :is_reseller, :reseller_type, :reseller_info, :terms_accepted
        )
    ");
    
    $stmt->execute($data);
    $request_id = $pdo->lastInsertId();

    // Registrar archivo subido en la tabla uploaded_files
    if ($payment_proof_url && isset($_FILES['paymentProof'])) {
        try {
            $file_stmt = $pdo->prepare("INSERT INTO uploaded_files (request_id, file_name, file_path, file_type, file_size, upload_type) VALUES (?, ?, ?, ?, ?, 'payment_proof')");
            $file_stmt->execute([
                $request_id,
                $_FILES['paymentProof']['name'],
                $payment_proof_url,
                $_FILES['paymentProof']['type'],
                $_FILES['paymentProof']['size']
            ]);
        } catch (Exception $e) {
            // Continuar aunque falle el registro del archivo
            error_log("Error registrando archivo: " . $e->getMessage());
        }
    }

    // Crear mensaje inicial de bienvenida
    $welcome_message = "¡Hola {$data['first_name']}! Hemos recibido tu solicitud de servicio IPTV.\n\n";
    $welcome_message .= "Detalles de tu solicitud:\n";

    // Tipo de servicio con descripción mejorada
    $service_types = [
        'trial' => 'Prueba gratuita (24-48 horas)',
        'direct_purchase' => 'Compra directa',
        'renewal' => 'Renovación de servicio',
        'reseller_activation' => 'Activación como revendedor',
        'reseller_renewal' => 'Renovación de revendedor'
    ];
    $welcome_message .= "- Tipo de servicio: " . ($service_types[$data['service_type']] ?? $data['service_type']) . "\n";
    $welcome_message .= "- Dispositivo: " . ucfirst(str_replace('_', ' ', $data['device_type'])) . "\n";

    // Información adicional según el tipo
    if ($data['platform_id']) {
        $welcome_message .= "- ID de plataforma: " . $data['platform_id'] . "\n";
    }

    if ($data['renewal_period']) {
        $periods = [
            '1_month' => '1 mes',
            '3_months' => '3 meses',
            '6_months' => '6 meses',
            '1_year' => '1 año'
        ];
        $welcome_message .= "- Período de renovación: " . ($periods[$data['renewal_period']] ?? $data['renewal_period']) . "\n";
    }

    if ($data['wants_current_promo']) {
        $welcome_message .= "- Promoción aplicada: Sí, descuento especial incluido\n";
    }

    if ($data['is_reseller']) {
        $welcome_message .= "- Solicitud de revendedor: " . ($data['reseller_type'] === 'new' ? 'Nuevo revendedor' : 'Renovación de revendedor') . "\n";
    }

    if ($data['payment_proof_url']) {
        $welcome_message .= "- Comprobante de pago: Archivo recibido correctamente\n";
    }
    
    if ($data['admin_chooses_credentials']) {
        $welcome_message .= "- Credenciales: El administrador las asignará\n";
    } else {
        $welcome_message .= "- Usuario deseado: " . ($data['preferred_username'] ?: 'No especificado') . "\n";
    }
    
    $welcome_message .= "\nNuestro equipo revisará tu solicitud y te contactaremos pronto. ¡Gracias por elegirnos!";
    
    // Insertar mensaje de bienvenida
    $msg_stmt = $pdo->prepare("
        INSERT INTO service_communications (request_id, sender_type, sender_name, message)
        VALUES (?, 'admin', 'Sistema', ?)
    ");
    $msg_stmt->execute([$request_id, $welcome_message]);
    
    // Enviar notificación por email (opcional)
    $subject = "Nueva Solicitud de Servicio IPTV - #{$request_id}";
    $email_body = "Nueva solicitud de servicio recibida:\n\n";
    $email_body .= "ID: #{$request_id}\n";
    $email_body .= "Cliente: {$data['first_name']} {$data['last_name']}\n";
    $email_body .= "Email: {$data['email']}\n";
    $email_body .= "Teléfono: {$data['phone']}\n";
    $email_body .= "Tipo: " . ($data['service_type'] === 'trial' ? 'Prueba' : 'Compra directa') . "\n";
    $email_body .= "Dispositivo: " . $data['device_type'] . "\n";
    $email_body .= "Fecha: " . date('Y-m-d H:i:s') . "\n";
    
    // Aquí puedes agregar el envío de email real si tienes configurado un servidor de correo
    // mail('<EMAIL>', $subject, $email_body);
    
    // Respuesta exitosa
    echo json_encode([
        'success' => true,
        'message' => 'Solicitud enviada exitosamente',
        'request_id' => $request_id,
        'status' => 'pending'
    ]);
    
} catch (Exception $e) {
    error_log("Error en submit_service_request.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'error' => 'Error al procesar la solicitud',
        'details' => $e->getMessage()
    ]);
}
?>
