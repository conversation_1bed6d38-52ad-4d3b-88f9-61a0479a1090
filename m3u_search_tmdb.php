<?php
// Buscador M3U con integración TMDB
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();

if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

// Incluir configuración TMDB
require_once 'tmdb_config.php';

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Error de conexión: " . $e->getMessage());
}

// Función para limpiar título para comparación
function cleanTitle($title) {
    $clean = strtolower($title);
    $clean = preg_replace('/[^\w\s]/', ' ', $clean);
    $clean = preg_replace('/\s+/', ' ', $clean);
    return trim($clean);
}

// Función para extraer información TMDB del logo URL
function extractTMDBInfoFromLogo($logo_url) {
    if (empty($logo_url) || strpos($logo_url, 'image.tmdb.org') === false) {
        return null;
    }

    // Extraer el hash de la imagen TMDB del URL
    // Ejemplo: https://image.tmdb.org/t/p/w600_and_h900_bestv2/bkIhygnbv9ydlDo4SEsOcqGgQD4.jpg
    if (preg_match('/\/([a-zA-Z0-9]+)\.jpg$/', $logo_url, $matches)) {
        return [
            'tmdb_image_hash' => $matches[1],
            'logo_url' => $logo_url
        ];
    }

    return null;
}

// Función para extraer año del título
function extractYearFromTitle($title) {
    if (preg_match('/\((\d{4})\)/', $title, $matches)) {
        return (int)$matches[1];
    }
    return null;
}

// Función para búsqueda mejorada con sistema de relevancia (basada en m3u_search_enhanced.php)
function enhancedM3USearch($query, $pdo) {
    $results = [];

    // 1. Búsqueda directa por TMDB ID si el query es numérico
    if (is_numeric($query)) {
        $stmt = $pdo->prepare("
            SELECT c.*, l.name as list_name, l.folder_name, 'tmdb_id_exact' as match_type, 100 as relevance
            FROM m3u_content c
            LEFT JOIN m3u_lists l ON c.list_id = l.id
            WHERE l.is_active = 1 AND c.tmdb_id = ?
            ORDER BY c.title
        ");
        $stmt->execute([$query]);
        $results = array_merge($results, $stmt->fetchAll(PDO::FETCH_ASSOC));
    }

    // 2. Búsqueda por información TMDB (más precisa)
    $search_term = '%' . $query . '%';
    $stmt = $pdo->prepare("
        SELECT c.*, l.name as list_name, l.folder_name, 'tmdb_title' as match_type, 95 as relevance
        FROM m3u_content c
        LEFT JOIN m3u_lists l ON c.list_id = l.id
        WHERE l.is_active = 1 AND (
            c.tmdb_title LIKE ? OR
            c.tmdb_original_title LIKE ? OR
            c.tmdb_overview LIKE ?
        )
        ORDER BY c.title
        LIMIT 200
    ");
    $stmt->execute([$search_term, $search_term, $search_term]);
    $tmdb_results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $results = array_merge($results, $tmdb_results);

    // 3. Búsqueda por título original (fallback)
    $stmt = $pdo->prepare("
        SELECT c.*, l.name as list_name, l.folder_name, 'original_title' as match_type, 80 as relevance
        FROM m3u_content c
        LEFT JOIN m3u_lists l ON c.list_id = l.id
        WHERE l.is_active = 1
        AND (c.title LIKE ? OR c.clean_title LIKE ?)
        AND c.id NOT IN (
            SELECT DISTINCT c2.id FROM m3u_content c2
            LEFT JOIN m3u_lists l2 ON c2.list_id = l2.id
            WHERE l2.is_active = 1 AND (
                c2.tmdb_title LIKE ? OR
                c2.tmdb_original_title LIKE ? OR
                c2.tmdb_overview LIKE ?
            )
        )
        ORDER BY
            CASE
                WHEN c.title LIKE ? THEN 1
                WHEN c.clean_title LIKE ? THEN 2
                ELSE 3
            END,
            c.title
        LIMIT 300
    ");
    $exact_term = '%' . $query . '%';
    $stmt->execute([
        $search_term, $search_term, // Para la búsqueda principal
        $search_term, $search_term, $search_term, // Para excluir ya encontrados
        $exact_term, $exact_term // Para el ORDER BY
    ]);
    $original_results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $results = array_merge($results, $original_results);

    // 4. Buscar contenido relacionado por TMDB ID si encontramos coincidencias exactas
    $tmdb_ids = [];
    foreach ($results as $result) {
        if (!empty($result['tmdb_id']) && !in_array($result['tmdb_id'], $tmdb_ids)) {
            $tmdb_ids[] = $result['tmdb_id'];
        }
    }

    if (!empty($tmdb_ids)) {
        $placeholders = str_repeat('?,', count($tmdb_ids) - 1) . '?';
        $existing_ids = array_column($results, 'id');
        $existing_placeholders = !empty($existing_ids) ? str_repeat('?,', count($existing_ids) - 1) . '?' : '0';

        $stmt = $pdo->prepare("
            SELECT c.*, l.name as list_name, l.folder_name, 'related_tmdb' as match_type, 90 as relevance
            FROM m3u_content c
            LEFT JOIN m3u_lists l ON c.list_id = l.id
            WHERE l.is_active = 1
            AND c.tmdb_id IN ($placeholders)
            AND c.id NOT IN ($existing_placeholders)
            ORDER BY c.title
        ");
        $stmt->execute(array_merge($tmdb_ids, $existing_ids));
        $related = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $results = array_merge($results, $related);
    }

    // Eliminar duplicados y ordenar por relevancia
    $unique_results = [];
    $seen_ids = [];

    foreach ($results as $result) {
        if (!in_array($result['id'], $seen_ids)) {
            $unique_results[] = $result;
            $seen_ids[] = $result['id'];
        }
    }

    // Ordenar por relevancia y luego por título
    usort($unique_results, function($a, $b) {
        if ($a['relevance'] == $b['relevance']) {
            return strcmp($a['title'], $b['title']);
        }
        return $b['relevance'] - $a['relevance'];
    });

    return $unique_results;
}

// Función para matching avanzado con información TMDB (simplificada para compatibilidad)
function findAdvancedTMDBMatch($m3u_item) {
    if (!isTMDBConfigured()) {
        return null;
    }

    $title = $m3u_item['title'] ?? '';
    $logo_url = $m3u_item['logo'] ?? '';

    // Limpiar título como en el sistema original
    $search_title = preg_replace('/s\d+e\d+.*$/i', '', $title);
    $search_title = preg_replace('/\(\d{4}\)/', '', $search_title);
    $search_title = trim($search_title);

    // Extraer año del título original
    $year = extractYearFromTitle($title);

    // Buscar en TMDB usando el mismo método que el sistema original
    $tmdb_data = searchTMDBContent($search_title);
    if (!$tmdb_data || empty($tmdb_data['results'])) {
        return null;
    }

    $clean_m3u = cleanTitle($search_title);
    $best_match = null;
    $best_score = 0;

    foreach ($tmdb_data['results'] as $result) {
        $tmdb_title = $result['title'] ?? $result['name'] ?? '';
        $clean_tmdb = cleanTitle($tmdb_title);

        // Calcular similitud base (como en el sistema original)
        similar_text($clean_m3u, $clean_tmdb, $title_similarity);
        $score = $title_similarity;

        // Bonus por año exacto
        if ($year) {
            $tmdb_year = null;
            if (isset($result['release_date']) && !empty($result['release_date'])) {
                $tmdb_year = (int)date('Y', strtotime($result['release_date']));
            } elseif (isset($result['first_air_date']) && !empty($result['first_air_date'])) {
                $tmdb_year = (int)date('Y', strtotime($result['first_air_date']));
            }

            if ($tmdb_year && abs($year - $tmdb_year) <= 1) {
                $score += 15; // Año exacto o muy cercano
            }
        }

        // Bonus por logo TMDB coincidente
        if (!empty($logo_url) && isset($result['poster_path'])) {
            $tmdb_poster = $result['poster_path'];
            if ($tmdb_poster && strpos($logo_url, basename($tmdb_poster, '.jpg')) !== false) {
                $score += 10; // Logo coincide
            }
        }

        // Guardar el mejor match (mantener umbral original de 70)
        if ($score > $best_score && $score > 70) {
            $best_score = $score;
            $best_match = $result;
            $best_match['match_score'] = $score;
            $best_match['match_details'] = [
                'title_similarity' => $title_similarity,
                'year_match' => isset($tmdb_year) && $year ? abs($year - $tmdb_year) : null,
                'has_logo_match' => !empty($logo_url) && isset($result['poster_path']) && strpos($logo_url, basename($result['poster_path'], '.jpg')) !== false,
                'original_year' => $year,
                'tmdb_year' => $tmdb_year ?? null
            ];
        }
    }

    return $best_match;
}

// Función para generar archivo M3U de serie por lista específica
function generateSeriesM3UByList($pdo, $series_title, $list_id) {
    try {
        $stmt = $pdo->prepare("
            SELECT c.*, l.name as list_name
            FROM m3u_content c
            LEFT JOIN m3u_lists l ON c.list_id = l.id
            WHERE c.list_id = ?
            AND l.is_active = 1
            AND (c.title LIKE ? OR c.title LIKE ?)
            ORDER BY c.title
        ");

        $search_pattern1 = "%$series_title%";
        $search_pattern2 = "%" . preg_replace('/s\d+e\d+.*$/i', '', $series_title) . "%";

        $stmt->execute([$list_id, $search_pattern1, $search_pattern2]);
        $episodes = $stmt->fetchAll(PDO::FETCH_ASSOC);

        if (empty($episodes)) {
            return null;
        }

        // Generar contenido M3U
        $list_name = $episodes[0]['list_name'] ?? 'Lista';
        $m3u_content = "#EXTM3U\n";
        $m3u_content .= "#EXTINF:-1,Serie: " . $series_title . "\n";
        $m3u_content .= "# Generado por RGS TOOL - " . date('Y-m-d H:i:s') . "\n";
        $m3u_content .= "# Lista origen: " . $list_name . "\n";
        $m3u_content .= "# Total de episodios: " . count($episodes) . "\n\n";

        foreach ($episodes as $episode) {
            $m3u_content .= "#EXTINF:-1," . $episode['title'] . "\n";
            $m3u_content .= $episode['url'] . "\n\n";
        }

        return [
            'content' => $m3u_content,
            'filename' => preg_replace('/[^a-zA-Z0-9_-]/', '_', $series_title) . '_' . preg_replace('/[^a-zA-Z0-9_-]/', '_', $list_name) . '.m3u',
            'episodes_count' => count($episodes),
            'list_name' => $list_name
        ];

    } catch (Exception $e) {
        return null;
    }
}

// Procesar descarga de serie por lista
if (isset($_GET['download_series']) && isset($_GET['series_title']) && isset($_GET['list_id'])) {
    $series_title = $_GET['series_title'];
    $list_id = (int)$_GET['list_id'];

    if (!empty($series_title) && $list_id > 0) {
        $series_data = generateSeriesM3UByList($pdo, $series_title, $list_id);

        if ($series_data) {
            header('Content-Type: application/octet-stream');
            header('Content-Disposition: attachment; filename="' . $series_data['filename'] . '"');
            header('Content-Length: ' . strlen($series_data['content']));
            echo $series_data['content'];
            exit;
        }
    }

    $error_message = "No se encontraron episodios para la serie: $series_title en la lista especificada";
}

// Función para obtener detalles específicos de TMDB
function getTMDBDetails($tmdb_id, $media_type) {
    if (!isTMDBConfigured()) {
        return null;
    }

    return getTMDBContentDetails($tmdb_id, $media_type);
}

// Función para encontrar el mejor match TMDB (versión mejorada)
function findBestTMDBMatch($m3u_title, $m3u_item = null) {
    if (!isTMDBConfigured()) {
        return null;
    }

    // Si tenemos información completa del item M3U, usar matching avanzado
    if ($m3u_item && is_array($m3u_item)) {
        return findAdvancedTMDBMatch($m3u_item);
    }

    // Fallback al método original para compatibilidad
    // Limpiar título para búsqueda
    $search_title = preg_replace('/s\d+e\d+.*$/i', '', $m3u_title);
    $search_title = preg_replace('/\(\d{4}\)/', '', $search_title);
    $search_title = trim($search_title);

    $tmdb_data = searchTMDBContent($search_title);
    if (!$tmdb_data || empty($tmdb_data['results'])) {
        return null;
    }

    $clean_m3u = cleanTitle($search_title);
    $best_match = null;
    $best_score = 0;

    foreach ($tmdb_data['results'] as $result) {
        $tmdb_title = $result['title'] ?? $result['name'] ?? '';
        $clean_tmdb = cleanTitle($tmdb_title);

        // Calcular similitud
        similar_text($clean_m3u, $clean_tmdb, $score);

        if ($score > $best_score && $score > 70) {
            $best_score = $score;
            $best_match = $result;
        }
    }

    return $best_match;
}

$search_query = $_GET['q'] ?? $_GET['search'] ?? '';
$auto_search = isset($_GET['auto_search']) && $_GET['auto_search'] == '1';
$tmdb_id_from_admin = $_GET['tmdb_id'] ?? null;
$media_type_from_admin = $_GET['media_type'] ?? null;
$results = [];
$total_content = 0;

// Obtener estadísticas generales
try {
    $stmt = $pdo->query("SELECT COUNT(*) FROM m3u_content c LEFT JOIN m3u_lists l ON c.list_id = l.id WHERE l.is_active = 1");
    $total_content = $stmt->fetchColumn();
} catch (Exception $e) {
    $total_content = 0;
}

// Realizar búsqueda si hay query
if ($search_query && strlen($search_query) >= 2) {
    try {
        // Usar el nuevo sistema de búsqueda mejorada
        $raw_results = enhancedM3USearch($search_query, $pdo);

        // Limitar a 50 resultados para mantener rendimiento
        $raw_results = array_slice($raw_results, 0, 50);
        
        // Funciones auxiliares para detección de series (copiadas de order_matches.php)
        function isSeriesEpisode($title) {
            $patterns = [
                '/s\d+\s*e\d+/i',           // S01E01, S1E1, S01 E01, S1 E1
                '/s\d+\s*ep\d+/i',          // S01EP01, S1 EP1
                '/\d+x\d+/i',               // 1x01, 1x1
                '/season\s*\d+/i',          // Season 1, Season 01
                '/temporada\s*\d+/i',       // Temporada 1
                '/cap[ií]tulo\s*\d+/i',     // Capítulo 1
                '/ep\s*\d+/i',              // Ep 1, Ep01
                '/episode\s*\d+/i',         // Episode 1
                '/\s\d+\s*-\s*\d+/i',       // " 1 - 01", " 01-01"
                '/\[\d+x\d+\]/i',           // [1x01]
                '/\(\d+x\d+\)/i',           // (1x01)
                '/\s\d{1,2}x\d{1,2}\s/i',   // " 1x01 "
                '/\s\d{1,2}\.\d{1,2}\s/i',  // " 1.01 "
                '/\sT\d+\s*E\d+/i',         // T01E01, T1 E1
                '/\sT\d+\s*Cap\d+/i'        // T01Cap01, T1 Cap1
            ];

            foreach ($patterns as $pattern) {
                if (preg_match($pattern, $title)) {
                    return true;
                }
            }
            return false;
        }

        function extractSeriesBaseName($title) {
            $clean_title = $title;
            $patterns = [
                '/s\d+\s*e\d+.*$/i',           // S01E01, S01 E01 y todo lo que sigue
                '/s\d+\s*ep\d+.*$/i',          // S01EP01, S01 EP01 y todo lo que sigue
                '/\d+x\d+.*$/i',               // 1x01 y todo lo que sigue
                '/season\s*\d+.*$/i',          // Season 1 y todo lo que sigue
                '/temporada\s*\d+.*$/i',       // Temporada 1 y todo lo que sigue
                '/cap[ií]tulo\s*\d+.*$/i',     // Capítulo 1 y todo lo que sigue
                '/ep\s*\d+.*$/i',              // Ep 1 y todo lo que sigue
                '/episode\s*\d+.*$/i',         // Episode 1 y todo lo que sigue
                '/\s*-\s*\d+.*$/i',            // - 01 y todo lo que sigue
                '/\s*\(\d+\).*$/i',            // (01) y todo lo que sigue
                '/\s*\[\d+\].*$/i',            // [01] y todo lo que sigue
                '/\s\d+\s*-\s*\d+.*$/i',       // " 1 - 01" y todo lo que sigue
                '/\[\d+x\d+\].*$/i',           // [1x01] y todo lo que sigue
                '/\(\d+x\d+\).*$/i',           // (1x01) y todo lo que sigue
                '/\s\d{1,2}x\d{1,2}.*$/i',     // " 1x01" y todo lo que sigue
                '/\s\d{1,2}\.\d{1,2}.*$/i',    // " 1.01" y todo lo que sigue
                '/\sT\d+\s*E\d+.*$/i',         // T01E01, T1 E1 y todo lo que sigue
                '/\sT\d+\s*Cap\d+.*$/i',       // T01Cap01, T1 Cap1 y todo lo que sigue
                '/\s*\d{4}\s*$/i',             // Año al final (ej: " 2023")
                '/\s*\(\d{4}\)\s*$/i'          // Año entre paréntesis al final
            ];

            foreach ($patterns as $pattern) {
                $clean_title = preg_replace($pattern, '', $clean_title);
            }

            $clean_title = trim($clean_title);
            $clean_title = rtrim($clean_title, ' -_.');
            return trim($clean_title);
        }

        // Agrupar por serie y agregar info TMDB
        $grouped_results = [];
        foreach ($raw_results as $item) {
            // Verificar si es una serie o película
            if (isSeriesEpisode($item['title'])) {
                // Es una serie, extraer nombre base
                $base_title = extractSeriesBaseName($item['title']);
            } else {
                // Es una película, usar título completo
                $base_title = $item['title'];
            }

            $base_title = trim($base_title);

            $key = strtolower($base_title);

            if (!isset($grouped_results[$key])) {
                $grouped_results[$key] = [
                    'base_title' => $base_title,
                    'items' => [],
                    'tmdb_info' => null,
                    'media_type' => $item['media_type'],
                    'year' => $item['year'],
                    'lists' => [], // Cambiar para agrupar por listas
                    'from_admin' => false,
                    'match_type' => $item['match_type'] ?? 'unknown',
                    'relevance' => $item['relevance'] ?? 0
                ];

                // Si viene desde admin con TMDB ID, usar esa información
                if ($tmdb_id_from_admin && $media_type_from_admin &&
                    (stripos($base_title, $search_query) !== false || stripos($search_query, $base_title) !== false)) {
                    // Marcar como resultado desde admin
                    $grouped_results[$key]['from_admin'] = true;
                    $grouped_results[$key]['admin_tmdb_id'] = $tmdb_id_from_admin;
                    $grouped_results[$key]['admin_media_type'] = $media_type_from_admin;

                    // Intentar obtener info TMDB específica
                    $specific_tmdb_info = getTMDBDetails($tmdb_id_from_admin, $media_type_from_admin);
                    if ($specific_tmdb_info) {
                        $grouped_results[$key]['tmdb_info'] = $specific_tmdb_info;
                    } else {
                        // Fallback al método original
                        $grouped_results[$key]['tmdb_info'] = findBestTMDBMatch($base_title);
                    }
                } else {
                    // Buscar info TMDB usando el método avanzado con información completa del primer item
                    $first_item = $item; // Usar el item actual que tiene toda la información M3U
                    $grouped_results[$key]['tmdb_info'] = findBestTMDBMatch($base_title, $first_item);
                }
            }

            // Agrupar por lista
            $list_id = $item['list_id'];
            $list_name = $item['list_name'];

            if (!isset($grouped_results[$key]['lists'][$list_id])) {
                $grouped_results[$key]['lists'][$list_id] = [
                    'list_name' => $list_name,
                    'list_id' => $list_id,
                    'items' => []
                ];
            }

            $grouped_results[$key]['lists'][$list_id]['items'][] = $item;
            $grouped_results[$key]['items'][] = $item; // Mantener para compatibilidad
        }
        
        $results = array_values($grouped_results);
        
    } catch (Exception $e) {
        $error_message = "Error en la búsqueda: " . $e->getMessage();
    }
}

// Obtener pedidos recientes para sugerencias
$recent_orders = [];
try {
    $stmt = $pdo->query("
        SELECT DISTINCT title 
        FROM orders 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        AND title IS NOT NULL 
        AND title != ''
        ORDER BY created_at DESC 
        LIMIT 15
    ");
    $recent_orders = $stmt->fetchAll(PDO::FETCH_COLUMN);
} catch (Exception $e) {
    // Silenciar error de pedidos
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Buscar Contenido M3U con TMDB - RGS TOOL</title>
    <style>
        :root {
            /* Paleta de colores profesional con verde como principal */
            --primary-color: #10b981;
            --primary-dark: #059669;
            --primary-light: #34d399;
            --secondary-color: #1f2937;
            --dark-bg: #111827;
            --darker-bg: #030712;
            --surface: #374151;
            --surface-light: #4b5563;
            --surface-elevated: #6b7280;

            /* Colores de texto profesionales */
            --text-primary: #f9fafb;
            --text-secondary: #d1d5db;
            --text-muted: #9ca3af;
            --text-accent: #34d399;

            /* Colores de estado para TMDB Search */
            --accent-color: #10b981;
            --accent-dark: #059669;
            --warning-color: #d97706;
            --error-color: #dc2626;
            --success-color: #16a34a;
            --info-color: #2563eb;
            --tmdb-color: #10b981;

            /* Gradientes corporativos 3D con verde */
            --gradient-primary: linear-gradient(135deg, #10b981 0%, #059669 50%, #047857 100%);
            --gradient-secondary: linear-gradient(135deg, #34d399 0%, #10b981 50%, #059669 100%);
            --gradient-tmdb: linear-gradient(135deg, #10b981 0%, #059669 50%, #065f46 100%);
            --gradient-surface: linear-gradient(135deg, #1f2937 0%, #374151 50%, #4b5563 100%);
            --gradient-hero: linear-gradient(135deg, #030712 0%, #111827 25%, #1f2937 50%, #374151 100%);
            --gradient-card: linear-gradient(145deg, #1f2937 0%, #374151 100%);
            --gradient-elevated: linear-gradient(145deg, #374151 0%, #4b5563 100%);

            /* Sombras 3D profesionales */
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            --shadow-3d: 0 8px 16px rgba(0, 0, 0, 0.15), 0 4px 8px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);
            --shadow-elevated: 0 12px 24px rgba(0, 0, 0, 0.2), 0 6px 12px rgba(0, 0, 0, 0.15);
            --shadow-glow: 0 0 20px rgba(16, 185, 129, 0.4);
            --shadow-tmdb: 0 0 20px rgba(16, 185, 129, 0.3);

            /* Espaciado consistente */
            --space-xs: 0.25rem;
            --space-sm: 0.5rem;
            --space-md: 1rem;
            --space-lg: 1.5rem;
            --space-xl: 2rem;
            --space-2xl: 3rem;

            /* Border radius moderno */
            --radius-sm: 6px;
            --radius-md: 8px;
            --radius-lg: 12px;
            --radius-xl: 16px;
            --radius-full: 9999px;

            /* Transiciones y efectos 3D */
            --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-bounce: 400ms cubic-bezier(0.68, -0.55, 0.265, 1.55);

            /* Transformaciones 3D */
            --transform-hover: translateY(-4px) scale(1.02);
            --transform-active: translateY(-2px) scale(0.98);
            --transform-3d: perspective(1000px) rotateX(2deg) rotateY(-2deg);
            --transform-card: perspective(1000px) rotateX(0deg) rotateY(0deg);

            /* Filtros profesionales */
            --blur-glass: blur(16px) saturate(180%);
            --blur-backdrop: blur(12px) saturate(150%);
            --brightness-hover: brightness(1.1);
            --contrast-elevated: contrast(1.05);

            /* Compatibilidad */
            --border-color: #374151;
            --border-light: #4b5563;
            --border-radius: var(--radius-lg);
            --border-radius-lg: var(--radius-xl);
            --transition: var(--transition-normal);
            --gradient-bg: var(--gradient-hero);
            --gradient-dark: var(--gradient-surface);
            --shadow-light: var(--shadow-sm);
            --shadow-medium: var(--shadow-md);
            --shadow-heavy: var(--shadow-xl);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gradient-hero);
            background-attachment: fixed;
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
            font-feature-settings: "kern" 1, "liga" 1;
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            position: relative;
            overflow-x: hidden;
        }

        /* Efectos de fondo 3D para TMDB Search */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 25% 25%, rgba(16, 185, 129, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(52, 211, 153, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 50% 50%, rgba(5, 150, 105, 0.06) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: var(--space-xl);
        }

        .back-link {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--text-primary);
            text-decoration: none;
            margin-bottom: var(--space-xl);
            font-weight: 500;
            padding: 0.5rem 1rem;
            background: var(--gradient-surface);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: var(--radius-lg);
            backdrop-filter: var(--blur-backdrop);
            transition: var(--transition-normal);
            box-shadow: var(--shadow-sm);
        }

        .back-link:hover {
            background: var(--gradient-elevated);
            border-color: var(--primary-color);
            transform: var(--transform-hover);
            box-shadow: var(--shadow-md), var(--shadow-glow);
            color: var(--text-accent);
        }

        .back-link i {
            transition: var(--transition-normal);
        }

        .back-link:hover i {
            transform: translateX(-3px);
        }

        .header {
            text-align: center;
            margin-bottom: var(--space-2xl);
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 200px;
            height: 200px;
            background: radial-gradient(circle, rgba(16, 185, 129, 0.1) 0%, transparent 70%);
            border-radius: 50%;
            z-index: -1;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 800;
            letter-spacing: -0.025em;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            position: relative;
        }

        .header h1 i {
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: tmdbPulse 3s ease-in-out infinite;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
        }

        @keyframes tmdbPulse {
            0%, 100% { transform: scale(1) rotateY(0deg); }
            50% { transform: scale(1.1) rotateY(15deg); }
        }

        .header p {
            color: var(--text-secondary);
            font-size: 1.1rem;
            margin-bottom: var(--space-lg);
        }

        .stats {
            display: flex;
            gap: var(--space-xl);
            justify-content: center;
            margin-bottom: var(--space-xl);
            flex-wrap: wrap;
        }

        .stat {
            text-align: center;
            background: var(--gradient-card);
            border: 1px solid rgba(255, 255, 255, 0.12);
            border-radius: var(--radius-xl);
            backdrop-filter: var(--blur-backdrop);
            padding: var(--space-lg);
            min-width: 150px;
            box-shadow: var(--shadow-3d);
            transform: var(--transform-card);
            position: relative;
            overflow: hidden;
            transition: var(--transition-normal);
        }

        .stat::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--gradient-primary);
            transform: scaleX(0);
            transition: var(--transition-normal);
            border-radius: var(--radius-xl) var(--radius-xl) 0 0;
        }

        .stat:hover {
            background: var(--gradient-elevated);
            border-color: var(--primary-color);
            transform: var(--transform-hover) var(--transform-3d);
            box-shadow: var(--shadow-2xl), var(--shadow-glow);
            filter: var(--brightness-hover);
        }

        .stat:hover::before {
            transform: scaleX(1);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 800;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            transition: var(--transition-normal);
        }

        .stat:hover .stat-number {
            transform: scale(1.1);
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.8rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            transition: var(--transition-normal);
        }

        .stat:hover .stat-label {
            color: var(--text-primary);
        }

        .search-box {
            background: var(--gradient-card);
            border: 1px solid rgba(255, 255, 255, 0.12);
            border-radius: var(--radius-xl);
            backdrop-filter: var(--blur-backdrop);
            padding: var(--space-xl);
            margin-bottom: var(--space-xl);
            box-shadow: var(--shadow-3d);
            position: relative;
            overflow: hidden;
            transition: var(--transition-normal);
        }

        .search-box::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--gradient-primary);
            opacity: 0.8;
        }

        .search-box:hover {
            border-color: var(--primary-color);
            box-shadow: var(--shadow-elevated), var(--shadow-glow);
        }

        .search-form {
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .search-input {
            flex: 1;
            min-width: 300px;
            padding: 1rem;
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: var(--radius-lg);
            background: var(--gradient-surface);
            color: var(--text-primary);
            font-size: 1rem;
            backdrop-filter: var(--blur-backdrop);
            transition: var(--transition-normal);
            box-shadow: var(--shadow-sm);
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: var(--shadow-md), var(--shadow-glow);
            transform: translateY(-2px);
            background: var(--gradient-elevated);
        }

        .search-input:hover {
            border-color: rgba(255, 255, 255, 0.2);
            transform: translateY(-1px);
        }

        /* Corregir options de todos los selects */
        select option {
            background: var(--surface);
            color: var(--text-primary);
            padding: 0.5rem;
            border: none;
        }

        select option:hover,
        select option:focus,
        select option:checked {
            background: var(--primary-color);
            color: white;
        }

        /* Para navegadores webkit */
        select option:checked {
            background: var(--primary-color) !important;
            color: white !important;
        }

        /* Estilos específicos para select */
        select {
            cursor: pointer;
            appearance: none;
            -webkit-appearance: none;
            -moz-appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2310b981' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 0.7rem center;
            background-size: 1rem;
            padding-right: 2.5rem;
        }

        /* Estilos adicionales para mejor compatibilidad */
        select::-ms-expand {
            display: none;
        }

        /* Forzar estilos en dropdown */
        select optgroup {
            background: var(--surface);
            color: var(--text-primary);
        }

        select optgroup option {
            background: var(--surface);
            color: var(--text-primary);
        }

        .search-btn {
            padding: 1rem 2rem;
            background: var(--gradient-primary);
            color: white;
            border: 1px solid var(--primary-color);
            border-radius: var(--radius-lg);
            cursor: pointer;
            font-weight: 600;
            font-size: 1rem;
            transition: var(--transition-normal);
            backdrop-filter: var(--blur-backdrop);
            box-shadow: var(--shadow-sm);
            position: relative;
            overflow: hidden;
        }

        .search-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
            opacity: 0;
            transition: var(--transition-fast);
        }

        .search-btn:hover::before {
            opacity: 1;
        }

        .search-btn:hover {
            background: var(--gradient-primary);
            transform: var(--transform-hover);
            box-shadow: var(--shadow-md), var(--shadow-glow);
            filter: var(--brightness-hover);
        }

        .search-btn:active {
            transform: var(--transform-active);
        }

        .suggestions {
            margin-top: 1.5rem;
        }

        .suggestions h4 {
            color: var(--text-secondary);
            margin-bottom: 1rem;
        }

        .suggestion-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .suggestion-tag {
            background: var(--dark-bg);
            border: 1px solid var(--border-color);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: var(--transition);
        }

        .suggestion-tag:hover {
            background: var(--accent-color);
            color: white;
            transform: translateY(-2px);
        }

        .results {
            background: var(--gradient-card);
            border: 1px solid rgba(255, 255, 255, 0.12);
            border-radius: var(--radius-xl);
            backdrop-filter: var(--blur-backdrop);
            padding: var(--space-xl);
            box-shadow: var(--shadow-3d);
            position: relative;
            overflow: hidden;
        }

        .results::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--gradient-primary);
            opacity: 0.8;
        }

        .result-item {
            background: var(--gradient-card);
            border: 1px solid rgba(255, 255, 255, 0.12);
            border-radius: var(--radius-xl);
            backdrop-filter: var(--blur-backdrop);
            margin-bottom: var(--space-xl);
            overflow: hidden;
            transition: var(--transition-normal);
            box-shadow: var(--shadow-3d);
            transform: var(--transform-card);
            position: relative;
        }

        .result-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--gradient-primary);
            transform: scaleX(0);
            transition: var(--transition-normal);
            border-radius: var(--radius-xl) var(--radius-xl) 0 0;
        }

        .result-item::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%, rgba(255, 255, 255, 0.05) 100%);
            opacity: 0;
            transition: var(--transition-normal);
            pointer-events: none;
        }

        .result-item:hover {
            background: var(--gradient-elevated);
            border-color: var(--primary-color);
            transform: var(--transform-hover) var(--transform-3d);
            box-shadow: var(--shadow-2xl), var(--shadow-glow);
            filter: var(--brightness-hover);
        }

        .result-item:hover::before {
            transform: scaleX(1);
        }

        .result-item:hover::after {
            opacity: 1;
        }

        .result-content {
            display: flex;
            gap: 1.5rem;
            padding: 1.5rem;
        }

        .tmdb-poster {
            width: 120px;
            height: 180px;
            border-radius: 8px;
            object-fit: cover;
            background: var(--secondary-color);
            flex-shrink: 0;
        }

        .result-info {
            flex: 1;
        }

        .result-title {
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .tmdb-title {
            color: var(--text-secondary);
            font-size: 1rem;
            margin-bottom: 1rem;
            font-style: italic;
        }

        .result-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin-bottom: 1rem;
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 0.3rem;
        }

        .tmdb-overview {
            color: var(--text-secondary);
            font-size: 0.9rem;
            line-height: 1.5;
            margin-bottom: 1rem;
            max-height: 4.5em;
            overflow: hidden;
        }

        .episodes-count {
            background: var(--accent-color);
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
            margin-bottom: 1rem;
            display: inline-block;
        }

        .download-section {
            background: rgba(16, 185, 129, 0.05);
            border: 1px solid rgba(16, 185, 129, 0.2);
            border-radius: var(--border-radius);
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .download-section h4 {
            color: var(--accent-color);
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .list-download-btn {
            position: relative;
            overflow: hidden;
        }

        .list-download-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        .episode-badge {
            background: rgba(0, 0, 0, 0.4);
            padding: 0.1rem 0.4rem;
            border-radius: 10px;
            margin-left: 0.3rem;
            font-size: 0.7rem;
            font-weight: bold;
        }

        .result-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .btn {
            padding: 0.6rem 1.2rem;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
        }

        .btn-copy {
            background: var(--success-color);
            color: white;
        }

        .btn-info {
            background: var(--info-color);
            color: white;
        }

        .btn-warning {
            background: var(--warning-color);
            color: white;
        }

        .btn:hover {
            transform: translateY(-1px);
            opacity: 0.9;
        }

        .no-results {
            text-align: center;
            padding: 3rem;
            color: var(--text-secondary);
        }

        .no-results i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .error {
            background: rgba(239, 68, 68, 0.2);
            color: var(--error-color);
            padding: 1rem;
            border-radius: var(--border-radius);
            margin-bottom: 1rem;
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        .success {
            background: rgba(16, 185, 129, 0.2);
            color: var(--success-color);
            padding: 1rem;
            border-radius: var(--border-radius);
            margin-bottom: 1rem;
            border: 1px solid rgba(16, 185, 129, 0.3);
        }

        .match-quality-high {
            background: var(--success-color);
            color: white;
        }

        .match-quality-good {
            background: var(--accent-color);
            color: white;
        }

        .match-quality-medium {
            background: var(--warning-color);
            color: white;
        }

        .match-quality-low {
            background: var(--error-color);
            color: white;
        }

        .match-details {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            padding: 0.5rem;
            margin: 0.5rem 0;
            font-size: 0.8rem;
        }

        .advanced-match-indicator {
            background: linear-gradient(45deg, var(--accent-color), var(--primary-color));
            color: white;
            padding: 0.2rem 0.6rem;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: bold;
            display: inline-flex;
            align-items: center;
            gap: 0.3rem;
            margin-left: 0.5rem;
            animation: pulse 2s infinite;
        }

        .relevance-badge {
            padding: 0.2rem 0.5rem;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: bold;
            color: white;
            margin-left: 0.5rem;
            display: inline-flex;
            align-items: center;
            gap: 0.3rem;
        }

        .relevance-exact {
            background: linear-gradient(135deg, var(--success-color), #059669);
            box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
        }

        .relevance-high {
            background: linear-gradient(135deg, var(--accent-color), #059669);
            box-shadow: 0 2px 8px rgba(70, 211, 71, 0.3);
        }

        .relevance-medium {
            background: linear-gradient(135deg, var(--warning-color), #f97316);
            box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
        }

        .relevance-low {
            background: linear-gradient(135deg, var(--info-color), #2563eb);
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
        }

        .enhanced-search-info {
            background: rgba(37, 99, 235, 0.1);
            border: 1px solid var(--primary-color);
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1.5rem;
            color: var(--text-secondary);
        }

        .enhanced-search-info h4 {
            color: var(--primary-color);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        @media (max-width: 768px) {
            .result-content {
                flex-direction: column;
            }
            
            .tmdb-poster {
                width: 100%;
                height: 200px;
                align-self: center;
                max-width: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="admin.php" class="back-link">
            <i class="fas fa-arrow-left"></i>
            Volver al Panel Admin
        </a>
        
        <div class="header">
            <h1><i class="fas fa-search"></i> Buscar Contenido M3U</h1>
            <p>Encuentra series y películas con información de TMDB</p>

            <?php if (!isTMDBConfigured()): ?>
            <div style="background: rgba(255, 193, 7, 0.2); border: 1px solid #ffc107; border-radius: 8px; padding: 1rem; margin: 1rem 0; font-size: 0.9rem;">
                <strong>⚠️ TMDB no configurado:</strong> Para ver posters y información adicional, configura tu API key en
                <code>tmdb_config.php</code>. Es gratis en
                <a href="https://www.themoviedb.org/" target="_blank" style="color: #ffc107;">themoviedb.org</a>
            </div>
            <?php endif; ?>
        </div>

        <!-- Estadísticas -->
        <div class="stats">
            <div class="stat">
                <div class="stat-number"><?php echo number_format($total_content); ?></div>
                <div class="stat-label">Contenido Total</div>
            </div>
            <div class="stat">
                <div class="stat-number"><?php echo count($results); ?></div>
                <div class="stat-label">Series/Películas</div>
            </div>
        </div>

        <!-- Formulario de búsqueda -->
        <div class="search-box">
            <?php if ($auto_search && $search_query): ?>
            <div style="background: rgba(70, 211, 71, 0.1); border: 1px solid var(--accent-color); border-radius: 8px; padding: 1rem; margin-bottom: 1.5rem;">
                <strong>🎯 Búsqueda desde Admin:</strong> Mostrando coincidencias para "<strong><?php echo htmlspecialchars($search_query); ?></strong>"
                <?php if ($tmdb_id_from_admin): ?>
                <br><strong>📺 TMDB ID:</strong> <?php echo htmlspecialchars($tmdb_id_from_admin); ?>
                <?php if ($media_type_from_admin): ?>
                    | <strong>Tipo:</strong> <?php echo $media_type_from_admin === 'movie' ? '🎬 Película' : '📺 Serie'; ?>
                <?php endif; ?>
                <?php endif; ?>
                <br><small style="color: var(--text-secondary); margin-top: 0.5rem; display: block;">
                    <i class="fas fa-info-circle"></i>
                    Esta búsqueda se ejecutó automáticamente desde el panel de administración con información TMDB precisa
                </small>
            </div>
            <?php else: ?>
            <div class="enhanced-search-info">
                <h4><i class="fas fa-rocket"></i> Sistema de Búsqueda Mejorado Activo</h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; font-size: 0.9rem;">
                    <div>
                        <strong style="color: var(--success-color);">🎯 ID Exacto (100%):</strong><br>
                        Coincidencia directa por TMDB ID
                    </div>
                    <div>
                        <strong style="color: var(--accent-color);">📺 TMDB (95%):</strong><br>
                        Título oficial de TMDB
                    </div>
                    <div>
                        <strong style="color: var(--warning-color);">📝 Título (80%):</strong><br>
                        Título original del M3U
                    </div>
                    <div>
                        <strong style="color: var(--info-color);">🔗 Relacionado (90%):</strong><br>
                        Contenido del mismo TMDB ID
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <form method="GET" class="search-form">
                <input type="text" 
                       name="q" 
                       class="search-input" 
                       value="<?php echo htmlspecialchars($search_query); ?>" 
                       placeholder="Buscar series, películas... (ej: Breaking Bad, Avengers)"
                       autofocus>
                <button type="submit" class="search-btn">
                    <i class="fas fa-search"></i>
                    Buscar
                </button>
            </form>

            <?php if (!empty($recent_orders)): ?>
            <div class="suggestions">
                <h4>Pedidos recientes (haz clic para buscar):</h4>
                <div class="suggestion-tags">
                    <?php foreach ($recent_orders as $order_title): ?>
                    <span class="suggestion-tag" onclick="searchFor('<?php echo addslashes($order_title); ?>')">
                        <?php echo htmlspecialchars($order_title); ?>
                    </span>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php endif; ?>
        </div>

        <!-- Resultados -->
        <?php if (isset($error_message)): ?>
        <div class="error"><?php echo htmlspecialchars($error_message); ?></div>
        <?php endif; ?>

        <?php if ($search_query): ?>
        <div class="results">
            <h2>Resultados para: "<?php echo htmlspecialchars($search_query); ?>"</h2>
            
            <?php if (empty($results)): ?>
            <div class="no-results">
                <i class="fas fa-search-minus"></i>
                <h3>No se encontraron resultados</h3>
                <p>Intenta con términos más generales o verifica que tengas listas M3U analizadas</p>
                <div style="margin-top: 1.5rem; text-align: left; max-width: 400px; margin-left: auto; margin-right: auto;">
                    <h4 style="color: var(--accent-color); margin-bottom: 1rem;">💡 Consejos de búsqueda:</h4>
                    <ul style="color: var(--text-secondary); line-height: 1.8;">
                        <li>Usa solo el nombre principal (ej: "Breaking" en lugar de "Breaking Bad S01E01")</li>
                        <li>Evita caracteres especiales y números de temporada</li>
                        <li>Prueba con nombres en inglés si no encuentras en español</li>
                    </ul>
                </div>
                <p style="margin-top: 2rem;">
                    <a href="m3u_content_viewer.php" class="btn btn-info">
                        <i class="fas fa-tv"></i>
                        Ver Todo el Contenido
                    </a>
                </p>
            </div>
            <?php else: ?>
            <?php foreach ($results as $result): ?>
            <div class="result-item">
                <div class="result-content">
                    <?php
                    $poster_url = null;
                    if ($result['tmdb_info'] && isset($result['tmdb_info']['poster_path'])) {
                        $poster_url = getTMDBImageUrl($result['tmdb_info']['poster_path'], 'w200');
                    }
                    ?>
                    <?php if ($poster_url): ?>
                    <img src="<?php echo $poster_url; ?>"
                         alt="Poster"
                         class="tmdb-poster"
                         onerror="this.style.display='none'">
                    <?php else: ?>
                    <div class="tmdb-poster" style="display: flex; align-items: center; justify-content: center; background: var(--secondary-color); color: var(--text-secondary);">
                        <i class="fas fa-image" style="font-size: 2rem;"></i>
                    </div>
                    <?php endif; ?>
                    
                    <div class="result-info">
                        <div class="result-title">
                            <?php echo htmlspecialchars($result['base_title']); ?>
                            <?php if (isset($result['from_admin']) && $result['from_admin']): ?>
                            <span style="background: var(--accent-color); color: white; padding: 0.2rem 0.5rem; border-radius: 12px; font-size: 0.7rem; margin-left: 0.5rem;">
                                🎯 DESDE ADMIN
                            </span>
                            <?php endif; ?>

                            <?php if (isset($result['match_type']) && isset($result['relevance'])): ?>
                            <span style="background: <?php
                                $relevance = $result['relevance'];
                                if ($relevance >= 95) echo 'var(--success-color)';
                                elseif ($relevance >= 90) echo 'var(--accent-color)';
                                elseif ($relevance >= 80) echo 'var(--warning-color)';
                                else echo 'var(--info-color)';
                            ?>; color: white; padding: 0.2rem 0.5rem; border-radius: 12px; font-size: 0.7rem; margin-left: 0.5rem;">
                                <?php
                                $match_labels = [
                                    'tmdb_id_exact' => '🎯 ID Exacto',
                                    'tmdb_title' => '📺 TMDB',
                                    'related_tmdb' => '🔗 Relacionado',
                                    'original_title' => '📝 Título'
                                ];
                                echo $match_labels[$result['match_type']] ?? $result['match_type'];
                                ?> (<?php echo $relevance; ?>%)
                            </span>
                            <?php endif; ?>
                        </div>

                        <?php if ($result['tmdb_info']): ?>
                        <div class="tmdb-title">
                            📺 <?php echo htmlspecialchars($result['tmdb_info']['title'] ?? $result['tmdb_info']['name'] ?? ''); ?>
                            <?php if (isset($result['admin_tmdb_id'])): ?>
                            <span style="color: var(--accent-color); font-weight: bold; margin-left: 0.5rem;">
                                (ID: <?php echo $result['admin_tmdb_id']; ?>)
                            </span>
                            <?php endif; ?>

                            <?php if (isset($result['tmdb_info']['match_score'])): ?>
                            <span style="background: <?php
                                $score = $result['tmdb_info']['match_score'];
                                if ($score >= 90) echo 'var(--success-color)';
                                elseif ($score >= 80) echo 'var(--accent-color)';
                                elseif ($score >= 70) echo 'var(--warning-color)';
                                else echo 'var(--error-color)';
                            ?>; color: white; padding: 0.2rem 0.5rem; border-radius: 12px; font-size: 0.7rem; margin-left: 0.5rem;">
                                🎯 <?php echo round($score); ?>% Match
                            </span>
                            <?php endif; ?>
                        </div>

                        <?php if (isset($result['tmdb_info']['match_details'])): ?>
                        <div style="background: rgba(255, 255, 255, 0.05); border: 1px solid var(--border-color); border-radius: 6px; padding: 0.75rem; margin: 0.5rem 0; font-size: 0.8rem;">
                            <?php $details = $result['tmdb_info']['match_details']; ?>
                            <div style="display: flex; flex-wrap: wrap; gap: 1rem; align-items: center;">
                                <div>
                                    <strong style="color: var(--accent-color);">📊 Similitud:</strong>
                                    <span style="color: var(--text-primary);"><?php echo round($details['title_similarity'] ?? 0); ?>%</span>
                                </div>

                                <?php if ($details['year_match'] !== null): ?>
                                <div>
                                    <strong style="color: var(--info-color);">📅 Año:</strong>
                                    <span style="color: var(--text-primary);">
                                        <?php if ($details['year_match'] == 0): ?>
                                            ✅ Exacto
                                        <?php else: ?>
                                            ±<?php echo $details['year_match']; ?> años
                                        <?php endif; ?>
                                    </span>
                                </div>
                                <?php endif; ?>

                                <?php if ($details['has_logo_match']): ?>
                                <div>
                                    <strong style="color: var(--success-color);">🖼️ Logo:</strong>
                                    <span style="color: var(--success-color);">✅ Coincide</span>
                                </div>
                                <?php endif; ?>

                                <?php if (isset($details['popularity_bonus']) && $details['popularity_bonus'] > 0): ?>
                                <div>
                                    <strong style="color: var(--warning-color);">⭐ Popular:</strong>
                                    <span style="color: var(--text-primary);">+<?php echo round($details['popularity_bonus'], 1); ?>pts</span>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php endif; ?>
                        <?php endif; ?>
                        
                        <div class="result-meta">
                            <div class="meta-item">
                                <i class="fas fa-list"></i>
                                Disponible en <?php echo count($result['lists']); ?> lista(s)
                            </div>
                            <div class="meta-item">
                                <i class="fas fa-film"></i>
                                <?php echo $result['media_type'] === 'movie' ? '🎬 Película' : ($result['media_type'] === 'tv' ? '📺 Serie' : '❓ Desconocido'); ?>
                            </div>
                            <?php if ($result['tmdb_info'] && isset($result['tmdb_info']['release_date'])): ?>
                            <div class="meta-item">
                                <i class="fas fa-calendar"></i>
                                <?php echo date('Y', strtotime($result['tmdb_info']['release_date'])); ?>
                            </div>
                            <?php elseif ($result['tmdb_info'] && isset($result['tmdb_info']['first_air_date'])): ?>
                            <div class="meta-item">
                                <i class="fas fa-calendar"></i>
                                <?php echo date('Y', strtotime($result['tmdb_info']['first_air_date'])); ?>
                            </div>
                            <?php elseif ($result['year']): ?>
                            <div class="meta-item">
                                <i class="fas fa-calendar"></i>
                                <?php echo $result['year']; ?>
                            </div>
                            <?php endif; ?>
                            <?php if ($result['tmdb_info'] && isset($result['tmdb_info']['vote_average'])): ?>
                            <div class="meta-item">
                                <i class="fas fa-star" style="color: #ffc107;"></i>
                                <?php echo number_format($result['tmdb_info']['vote_average'], 1); ?>/10
                            </div>
                            <?php endif; ?>
                        </div>
                        
                        <?php if ($result['tmdb_info'] && !empty($result['tmdb_info']['overview'])): ?>
                        <div class="tmdb-overview">
                            <?php echo htmlspecialchars(substr($result['tmdb_info']['overview'], 0, 200)); ?>
                            <?php if (strlen($result['tmdb_info']['overview']) > 200): ?>...<?php endif; ?>
                        </div>
                        <?php endif; ?>
                        
                        <div class="episodes-count">
                            📺 <?php echo count($result['items']); ?> episodio(s) disponible(s)
                        </div>

                        <?php if (isset($result['from_admin']) && $result['from_admin']): ?>
                        <div style="background: rgba(70, 211, 71, 0.1); border: 1px solid var(--accent-color); border-radius: 8px; padding: 0.75rem; margin: 1rem 0; font-size: 0.9rem;">
                            <strong style="color: var(--accent-color);">🎯 Búsqueda Precisa desde Admin</strong>
                            <br><small style="color: var(--text-secondary);">
                                Esta coincidencia se encontró usando la información exacta del pedido (TMDB ID: <?php echo $result['admin_tmdb_id']; ?>)
                            </small>
                        </div>
                        <?php endif; ?>

                        <!-- Opciones de descarga por lista -->
                        <div class="download-section">
                            <h4>
                                <i class="fas fa-download"></i>
                                Descargar desde:
                            </h4>
                            <div style="display: flex; flex-wrap: wrap; gap: 0.5rem;">
                                <?php foreach ($result['lists'] as $list): ?>
                                <a href="?download_series=1&series_title=<?php echo urlencode($result['base_title']); ?>&list_id=<?php echo $list['list_id']; ?>"
                                   class="btn btn-warning list-download-btn"
                                   style="font-size: 0.8rem; padding: 0.4rem 0.8rem;"
                                   title="Descargar <?php echo count($list['items']); ?> episodios de <?php echo htmlspecialchars($list['list_name']); ?>">
                                    <i class="fas fa-satellite-dish"></i>
                                    <?php echo htmlspecialchars($list['list_name']); ?>
                                    <span class="episode-badge">
                                        <?php echo count($list['items']); ?> eps
                                    </span>
                                </a>
                                <?php endforeach; ?>
                            </div>

                            <?php if (count($result['lists']) > 1): ?>
                            <div style="margin-top: 0.5rem; font-size: 0.8rem; color: var(--text-secondary);">
                                <i class="fas fa-info-circle"></i>
                                Cada botón descarga solo los episodios de esa lista específica
                            </div>
                            <?php endif; ?>
                        </div>

                        <div class="result-actions">
                            <button class="btn btn-copy" onclick="copyFirstUrl('<?php echo addslashes($result['items'][0]['url']); ?>')">
                                <i class="fas fa-copy"></i>
                                Copiar Primera URL
                            </button>

                            <?php if (count($result['lists']) === 1): ?>
                            <?php $single_list = array_values($result['lists'])[0]; ?>
                            <a href="?download_series=1&series_title=<?php echo urlencode($result['base_title']); ?>&list_id=<?php echo $single_list['list_id']; ?>"
                               class="btn btn-info">
                                <i class="fas fa-download"></i>
                                Descargar Todo (<?php echo count($single_list['items']); ?> eps)
                            </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
            
            <?php if (count($results) >= 50): ?>
            <div class="success">
                <i class="fas fa-info-circle"></i>
                Se muestran los primeros 50 resultados agrupados. Usa términos más específicos para refinar la búsqueda.
            </div>
            <?php endif; ?>

            <div style="background: rgba(70, 211, 71, 0.1); border: 1px solid var(--accent-color); border-radius: 8px; padding: 1.5rem; margin-top: 2rem;">
                <h3 style="color: var(--accent-color); margin-bottom: 1rem;">
                    <i class="fas fa-download"></i>
                    Sobre las Descargas
                </h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem; color: var(--text-secondary);">
                    <div>
                        <strong style="color: var(--text-primary);">📁 Por Lista:</strong><br>
                        Cada botón descarga solo los episodios de esa lista específica
                    </div>
                    <div>
                        <strong style="color: var(--text-primary);">📺 Formato M3U:</strong><br>
                        Archivos compatibles con VLC, IPTV players y reproductores
                    </div>
                    <div>
                        <strong style="color: var(--text-primary);">🎯 Contenido:</strong><br>
                        Solo episodios que coinciden con tu búsqueda
                    </div>
                </div>
            </div>

            <div style="background: rgba(37, 99, 235, 0.1); border: 1px solid var(--primary-color); border-radius: 8px; padding: 1.5rem; margin-top: 1rem;">
                <h3 style="color: var(--primary-color); margin-bottom: 1rem;">
                    <i class="fas fa-brain"></i>
                    Sistema de Matching Avanzado
                </h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem; color: var(--text-secondary);">
                    <div>
                        <strong style="color: var(--text-primary);">🎯 Precisión Mejorada:</strong><br>
                        Usa información TMDB del M3U (logos, años) para matching exacto
                    </div>
                    <div>
                        <strong style="color: var(--text-primary);">📊 Puntuación de Calidad:</strong><br>
                        Cada resultado muestra su nivel de coincidencia (60-100%)
                    </div>
                    <div>
                        <strong style="color: var(--text-primary);">🔍 Múltiples Factores:</strong><br>
                        Analiza título, año, imagen TMDB y popularidad
                    </div>
                    <div>
                        <strong style="color: var(--text-primary);">⚡ Desde Admin:</strong><br>
                        Búsquedas desde admin usan TMDB ID exacto para 100% precisión
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
        <?php endif; ?>

        <div style="margin-top: 2rem; text-align: center; color: var(--text-secondary);">
            <p>
                <a href="m3u_manager.php" style="color: var(--accent-color);">
                    <i class="fas fa-cog"></i> Gestionar Listas
                </a> | 
                <a href="m3u_content_viewer.php" style="color: var(--accent-color);">
                    <i class="fas fa-tv"></i> Ver Todo
                </a> | 
                <a href="admin.php" style="color: var(--accent-color);">
                    <i class="fas fa-home"></i> Admin
                </a>
            </p>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
    <script>
        function searchFor(title) {
            document.querySelector('input[name="q"]').value = title;
            document.querySelector('form').submit();
        }

        function copyFirstUrl(url) {
            copyToClipboard(url);
        }

        function copyToClipboard(text) {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(function() {
                    showMessage('✅ URL copiada al portapapeles');
                }).catch(function(err) {
                    fallbackCopy(text);
                });
            } else {
                fallbackCopy(text);
            }
        }

        function fallbackCopy(text) {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            
            try {
                document.execCommand('copy');
                showMessage('✅ URL copiada al portapapeles');
            } catch (err) {
                showMessage('❌ Error al copiar. Copia manualmente la URL.');
            }
            
            document.body.removeChild(textArea);
        }



        function showMessage(message) {
            const div = document.createElement('div');
            div.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: var(--success-color);
                color: white;
                padding: 1rem 1.5rem;
                border-radius: 8px;
                z-index: 1000;
                animation: slideIn 0.3s ease;
            `;
            div.textContent = message;
            document.body.appendChild(div);
            
            setTimeout(() => {
                div.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => document.body.removeChild(div), 300);
            }, 3000);
        }

        // Auto-focus en el campo de búsqueda y scroll automático para búsquedas desde admin
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.querySelector('input[name="q"]');
            const urlParams = new URLSearchParams(window.location.search);
            const autoSearch = urlParams.get('auto_search');

            if (autoSearch === '1' && searchInput && searchInput.value) {
                // Si es búsqueda automática desde admin, hacer scroll hacia los resultados
                setTimeout(() => {
                    const resultsSection = document.querySelector('.results');
                    if (resultsSection) {
                        resultsSection.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                }, 500);
            } else if (searchInput && !searchInput.value) {
                searchInput.focus();
            }
        });

        // Agregar estilos para animaciones
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
