<?php
header('Content-Type: application/json');

// Configuración MySQL
$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Error de base de datos']);
    exit;
}

// Verificar que sea una petición POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Método no permitido']);
    exit;
}

// Obtener datos JSON
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['device_type'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Tipo de dispositivo requerido']);
    exit;
}

$device_type = $input['device_type'];

// Crear tabla si no existe (fallback)
$pdo->exec("CREATE TABLE IF NOT EXISTS available_apps (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    device_type ENUM('android', 'ios', 'smart_tv_samsung', 'smart_tv_lg', 'smart_tv_other', 'pc_windows', 'pc_mac') NOT NULL,
    download_url VARCHAR(500),
    installation_guide TEXT,
    requirements TEXT,
    version VARCHAR(20),
    file_size VARCHAR(20),
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_device_type (device_type),
    INDEX idx_is_active (is_active)
)");

// Insertar aplicaciones de ejemplo si la tabla está vacía
$count_stmt = $pdo->query("SELECT COUNT(*) FROM available_apps");
if ($count_stmt->fetchColumn() == 0) {
    $apps = [
        ['IPTV Pro', 'android', 'https://play.google.com/store/apps/details?id=ru.iptvremote.android.iptv', 'Descarga desde Google Play Store y configura con tus credenciales', 'Android 5.0+', '2.1.5', '15MB'],
        ['GSE Smart IPTV', 'android', 'https://play.google.com/store/apps/details?id=com.gsetech.smartiptv2', 'Descarga desde Google Play Store, muy fácil de usar', 'Android 4.4+', '7.5.2', '25MB'],
        ['IPTV Smarters Pro', 'android', 'https://play.google.com/store/apps/details?id=com.nst.iptvsmarterstvbox', 'Aplicación profesional con muchas funciones', 'Android 4.1+', '3.0.9', '20MB'],
        ['TiviMate', 'android', 'https://play.google.com/store/apps/details?id=ar.tvplayer.tv', 'Interfaz moderna y elegante para IPTV', 'Android 5.0+', '4.6.0', '18MB'],
        
        ['GSE Smart IPTV', 'ios', 'https://apps.apple.com/app/gse-smart-iptv/id1028734023', 'Descarga desde App Store, compatible con iPhone y iPad', 'iOS 12.0+', '7.5.2', '30MB'],
        ['IPTV Player', 'ios', 'https://apps.apple.com/app/iptv-player/id1112017770', 'Reproductor IPTV simple y efectivo', 'iOS 11.0+', '2.1.1', '18MB'],
        ['nPlayer', 'ios', 'https://apps.apple.com/app/nplayer/id1116905928', 'Reproductor multimedia avanzado con soporte IPTV', 'iOS 13.0+', '1.9.7', '45MB'],
        
        ['Smart IPTV', 'smart_tv_samsung', '', 'Buscar "Smart IPTV" en Samsung Apps Store. Costo: $5.99 una sola vez', 'Tizen 2.4+', '1.7.7', '12MB'],
        ['SS IPTV', 'smart_tv_samsung', '', 'Buscar "SS IPTV" en Samsung Apps Store. Aplicación gratuita', 'Tizen 2.3+', '0.8.32', '8MB'],
        ['IPTV', 'smart_tv_samsung', '', 'Buscar "IPTV" en Samsung Apps Store. Aplicación simple y gratuita', 'Tizen 2.4+', '6.1.1', '10MB'],
        
        ['Smart IPTV', 'smart_tv_lg', '', 'Buscar "Smart IPTV" en LG Content Store. Costo: $5.99 una sola vez', 'webOS 1.0+', '1.7.7', '12MB'],
        ['SS IPTV', 'smart_tv_lg', '', 'Buscar "SS IPTV" en LG Content Store. Aplicación gratuita', 'webOS 1.0+', '0.8.32', '8MB'],
        ['IPTV', 'smart_tv_lg', '', 'Buscar "IPTV" en LG Content Store. Aplicación simple y gratuita', 'webOS 1.0+', '6.1.1', '10MB'],
        
        ['Smart IPTV', 'smart_tv_other', '', 'Buscar en la tienda de aplicaciones de tu Smart TV', 'Varía según TV', '1.7.7', '12MB'],
        ['SS IPTV', 'smart_tv_other', '', 'Aplicación gratuita disponible en la mayoría de Smart TVs', 'Varía según TV', '0.8.32', '8MB'],
        
        ['VLC Media Player', 'pc_windows', 'https://www.videolan.org/vlc/', 'Descarga gratuita desde sitio oficial. Abre listas M3U directamente', 'Windows 7+', '3.0.18', '40MB'],
        ['Kodi', 'pc_windows', 'https://kodi.tv/download/', 'Centro multimedia completo con addon PVR IPTV Simple Client', 'Windows 8+', '20.2', '60MB'],
        ['IPTV Player', 'pc_windows', 'https://www.microsoft.com/store/apps/9nblggh5l7d3', 'Aplicación de Microsoft Store específica para IPTV', 'Windows 10+', '2.1.0', '25MB'],
        
        ['VLC Media Player', 'pc_mac', 'https://www.videolan.org/vlc/', 'Descarga gratuita desde sitio oficial. Abre listas M3U directamente', 'macOS 10.10+', '3.0.18', '45MB'],
        ['IINA', 'pc_mac', 'https://iina.io/', 'Reproductor moderno para macOS con soporte IPTV', 'macOS 10.11+', '1.3.1', '50MB'],
        ['Kodi', 'pc_mac', 'https://kodi.tv/download/', 'Centro multimedia completo con addon PVR IPTV Simple Client', 'macOS 10.13+', '20.2', '65MB']
    ];
    
    $stmt = $pdo->prepare("INSERT INTO available_apps (name, device_type, download_url, installation_guide, requirements, version, file_size) VALUES (?, ?, ?, ?, ?, ?, ?)");
    
    foreach ($apps as $app) {
        $stmt->execute($app);
    }
}

// Obtener aplicaciones para el dispositivo específico
try {
    $stmt = $pdo->prepare("
        SELECT name, download_url, installation_guide, requirements, version, file_size 
        FROM available_apps 
        WHERE device_type = ? AND is_active = TRUE 
        ORDER BY name ASC
    ");
    
    $stmt->execute([$device_type]);
    $apps = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Si no hay aplicaciones específicas, buscar aplicaciones genéricas
    if (empty($apps) && strpos($device_type, 'smart_tv') !== false) {
        $stmt = $pdo->prepare("
            SELECT name, download_url, installation_guide, requirements, version, file_size 
            FROM available_apps 
            WHERE device_type = 'smart_tv_other' AND is_active = TRUE 
            ORDER BY name ASC
        ");
        
        $stmt->execute();
        $apps = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    echo json_encode($apps);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Error al obtener aplicaciones']);
}
?>
