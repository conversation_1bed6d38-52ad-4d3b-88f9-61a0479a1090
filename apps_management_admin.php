<?php
session_start();

// Verificar si el usuario está logueado como admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

// Configuración MySQL
$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Error de conexión a la base de datos: " . $e->getMessage());
}

// Procesar acciones
$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'update_app':
                $app_id = (int)$_POST['app_id'];
                $name = $_POST['name'];
                $download_url = $_POST['download_url'];
                $installation_guide = $_POST['installation_guide'];
                $requirements = $_POST['requirements'];
                $version = $_POST['version'];
                $file_size = $_POST['file_size'];
                $is_active = isset($_POST['is_active']) ? 1 : 0;
                
                $stmt = $pdo->prepare("UPDATE available_apps SET name = ?, download_url = ?, installation_guide = ?, requirements = ?, version = ?, file_size = ?, is_active = ?, updated_at = NOW() WHERE id = ?");
                if ($stmt->execute([$name, $download_url, $installation_guide, $requirements, $version, $file_size, $is_active, $app_id])) {
                    $message = "Aplicación actualizada correctamente.";
                    $message_type = 'success';
                } else {
                    $message = "Error al actualizar la aplicación.";
                    $message_type = 'error';
                }
                break;
                
            case 'update_management':
                $app_id = (int)$_POST['app_id'];
                $is_featured = isset($_POST['is_featured']) ? 1 : 0;
                $display_order = (int)$_POST['display_order'];
                $custom_description = $_POST['custom_description'];
                $admin_notes = $_POST['admin_notes'];
                $is_recommended = isset($_POST['is_recommended']) ? 1 : 0;
                $updated_by = $_SESSION['admin_username'] ?? 'Admin';
                
                // Verificar si ya existe registro de gestión
                $stmt = $pdo->prepare("SELECT id FROM app_management WHERE app_id = ?");
                $stmt->execute([$app_id]);
                $existing = $stmt->fetch();
                
                if ($existing) {
                    $stmt = $pdo->prepare("UPDATE app_management SET is_featured = ?, display_order = ?, custom_description = ?, admin_notes = ?, is_recommended = ?, updated_by = ?, updated_at = NOW() WHERE app_id = ?");
                    $result = $stmt->execute([$is_featured, $display_order, $custom_description, $admin_notes, $is_recommended, $updated_by, $app_id]);
                } else {
                    $stmt = $pdo->prepare("INSERT INTO app_management (app_id, is_featured, display_order, custom_description, admin_notes, is_recommended, updated_by) VALUES (?, ?, ?, ?, ?, ?, ?)");
                    $result = $stmt->execute([$app_id, $is_featured, $display_order, $custom_description, $admin_notes, $is_recommended, $updated_by]);
                }
                
                if ($result) {
                    $message = "Configuración de gestión actualizada correctamente.";
                    $message_type = 'success';
                } else {
                    $message = "Error al actualizar la configuración de gestión.";
                    $message_type = 'error';
                }
                break;
                
            case 'add_app':
                $name = $_POST['name'];
                $device_type = $_POST['device_type'];
                $download_url = $_POST['download_url'];
                $installation_guide = $_POST['installation_guide'];
                $requirements = $_POST['requirements'];
                $version = $_POST['version'];
                $file_size = $_POST['file_size'];
                $is_active = isset($_POST['is_active']) ? 1 : 0;
                
                $stmt = $pdo->prepare("INSERT INTO available_apps (name, device_type, download_url, installation_guide, requirements, version, file_size, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
                if ($stmt->execute([$name, $device_type, $download_url, $installation_guide, $requirements, $version, $file_size, $is_active])) {
                    $message = "Nueva aplicación agregada correctamente.";
                    $message_type = 'success';
                } else {
                    $message = "Error al agregar la aplicación.";
                    $message_type = 'error';
                }
                break;
        }
    }
}

// Obtener aplicaciones con información de gestión
$stmt = $pdo->query("
    SELECT 
        aa.*,
        am.is_featured,
        am.display_order,
        am.custom_description,
        am.admin_notes,
        am.is_recommended,
        am.updated_by,
        am.updated_at as management_updated_at
    FROM available_apps aa
    LEFT JOIN app_management am ON aa.id = am.app_id
    ORDER BY aa.device_type, COALESCE(am.display_order, 999), aa.name
");
$apps = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Agrupar por tipo de dispositivo
$apps_by_device = [];
foreach ($apps as $app) {
    $apps_by_device[$app['device_type']][] = $app;
}

// Función para obtener nombre del dispositivo
function getDeviceDisplayName($device_type) {
    $names = [
        'android' => 'Android',
        'ios' => 'iOS',
        'smart_tv_samsung' => 'Smart TV Samsung',
        'smart_tv_lg' => 'Smart TV LG',
        'smart_tv_other' => 'Otras Smart TV',
        'pc_windows' => 'PC Windows',
        'pc_mac' => 'Mac'
    ];
    return $names[$device_type] ?? $device_type;
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📱 Gestión de Aplicaciones - Admin</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            /* Paleta de colores profesional con verde como principal */
            --primary-color: #10b981;
            --primary-dark: #059669;
            --primary-light: #34d399;
            --secondary-color: #1f2937;
            --dark-bg: #111827;
            --darker-bg: #030712;
            --surface: #374151;
            --surface-light: #4b5563;
            --surface-elevated: #6b7280;

            /* Colores de texto profesionales */
            --text-primary: #f9fafb;
            --text-secondary: #d1d5db;
            --text-muted: #9ca3af;
            --text-accent: #34d399;

            /* Gradientes corporativos 3D con verde */
            --gradient-primary: linear-gradient(135deg, #10b981 0%, #059669 50%, #047857 100%);
            --gradient-secondary: linear-gradient(135deg, #34d399 0%, #10b981 50%, #059669 100%);
            --gradient-surface: linear-gradient(135deg, #1f2937 0%, #374151 50%, #4b5563 100%);
            --gradient-hero: linear-gradient(135deg, #030712 0%, #111827 25%, #1f2937 50%, #374151 100%);
            --gradient-card: linear-gradient(145deg, #1f2937 0%, #374151 100%);
            --gradient-elevated: linear-gradient(145deg, #374151 0%, #4b5563 100%);

            /* Sombras 3D profesionales */
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            --shadow-3d: 0 8px 16px rgba(0, 0, 0, 0.15), 0 4px 8px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);
            --shadow-elevated: 0 12px 24px rgba(0, 0, 0, 0.2), 0 6px 12px rgba(0, 0, 0, 0.15);
            --shadow-glow: 0 0 20px rgba(16, 185, 129, 0.4);

            /* Espaciado y bordes */
            --space-xs: 0.25rem;
            --space-sm: 0.5rem;
            --space-md: 1rem;
            --space-lg: 1.5rem;
            --space-xl: 2rem;
            --space-2xl: 3rem;
            --radius-sm: 6px;
            --radius-md: 8px;
            --radius-lg: 12px;
            --radius-xl: 16px;
            --radius-full: 9999px;

            /* Transiciones */
            --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);

            /* Transformaciones 3D */
            --transform-hover: translateY(-4px) scale(1.02);
            --transform-card: perspective(1000px) rotateX(0deg) rotateY(0deg);

            /* Filtros */
            --blur-backdrop: blur(12px) saturate(150%);
            --brightness-hover: brightness(1.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gradient-hero);
            background-attachment: fixed;
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
            font-feature-settings: "kern" 1, "liga" 1;
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Efectos de fondo */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 25% 25%, rgba(16, 185, 129, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(52, 211, 153, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 50% 50%, rgba(5, 150, 105, 0.06) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: var(--space-xl);
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-xl);
        }

        .page-title {
            font-size: 2.5rem;
            font-weight: 800;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .back-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            background: var(--gradient-surface);
            color: var(--text-primary);
            text-decoration: none;
            border-radius: var(--radius-lg);
            transition: var(--transition-normal);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .back-btn:hover {
            background: var(--gradient-elevated);
            border-color: var(--primary-color);
            transform: var(--transform-hover);
        }

        .device-section {
            margin-bottom: var(--space-2xl);
        }

        .device-header {
            background: var(--gradient-primary);
            color: white;
            padding: var(--space-lg);
            border-radius: var(--radius-xl) var(--radius-xl) 0 0;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.2rem;
            font-weight: 600;
        }

        .apps-grid {
            background: var(--gradient-card);
            border: 1px solid rgba(255, 255, 255, 0.12);
            border-radius: 0 0 var(--radius-xl) var(--radius-xl);
            padding: var(--space-lg);
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
            gap: var(--space-lg);
        }

        .app-card {
            background: var(--gradient-surface);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: var(--radius-lg);
            padding: var(--space-lg);
            transition: var(--transition-normal);
        }

        .app-card:hover {
            background: var(--gradient-elevated);
            border-color: var(--primary-color);
            transform: translateY(-2px);
        }

        .app-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-md);
        }

        .app-name {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .app-badges {
            display: flex;
            gap: 0.5rem;
        }

        .badge {
            padding: 0.25rem 0.5rem;
            border-radius: var(--radius-full);
            font-size: 0.7rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .badge-featured {
            background: var(--gradient-primary);
            color: white;
        }

        .badge-recommended {
            background: rgba(59, 130, 246, 0.2);
            color: #3b82f6;
            border: 1px solid rgba(59, 130, 246, 0.3);
        }

        .badge-inactive {
            background: rgba(239, 68, 68, 0.2);
            color: #ef4444;
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        .app-info {
            margin-bottom: var(--space-md);
            font-size: 0.9rem;
            color: var(--text-secondary);
        }

        .app-actions {
            display: flex;
            gap: 0.5rem;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: var(--radius-md);
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition-normal);
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
            font-size: 0.85rem;
        }

        .btn-primary {
            background: var(--gradient-primary);
            color: white;
        }

        .btn-secondary {
            background: var(--gradient-surface);
            color: var(--text-primary);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .add-app-btn {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            width: 60px;
            height: 60px;
            background: var(--gradient-primary);
            color: white;
            border: none;
            border-radius: 50%;
            font-size: 1.5rem;
            cursor: pointer;
            box-shadow: var(--shadow-lg), var(--shadow-glow);
            transition: var(--transition-normal);
        }

        .add-app-btn:hover {
            transform: scale(1.1);
            box-shadow: var(--shadow-xl), var(--shadow-glow);
        }

        /* Modal styles */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            z-index: 1000;
            backdrop-filter: blur(8px);
        }

        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: var(--gradient-card);
            border: 1px solid rgba(255, 255, 255, 0.12);
            border-radius: var(--radius-xl);
            padding: var(--space-xl);
            max-width: 600px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-lg);
        }

        .modal-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
        }

        .modal-close {
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: 1.5rem;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: var(--radius-lg);
            transition: var(--transition-normal);
        }

        .modal-close:hover {
            color: var(--text-primary);
            background: rgba(255, 255, 255, 0.1);
        }

        .form-group {
            margin-bottom: var(--space-lg);
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .form-input {
            width: 100%;
            padding: 0.75rem;
            background: var(--gradient-surface);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: var(--radius-lg);
            color: var(--text-primary);
            transition: var(--transition-normal);
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: var(--shadow-md), var(--shadow-glow);
        }

        .checkbox-group {
            display: flex;
            gap: var(--space-lg);
            margin-top: 0.5rem;
        }

        .checkbox-label {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            cursor: pointer;
        }

        .message {
            padding: 1rem;
            border-radius: var(--radius-lg);
            margin-bottom: var(--space-lg);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .message.success {
            background: rgba(16, 185, 129, 0.15);
            border: 1px solid rgba(16, 185, 129, 0.3);
            color: var(--primary-color);
        }

        .message.error {
            background: rgba(239, 68, 68, 0.15);
            border: 1px solid rgba(239, 68, 68, 0.3);
            color: #ef4444;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .container {
                padding: var(--space-lg);
            }

            .header {
                flex-direction: column;
                gap: var(--space-md);
                text-align: center;
            }

            .page-title {
                font-size: 2rem;
            }

            .apps-grid {
                grid-template-columns: 1fr;
            }

            .app-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
