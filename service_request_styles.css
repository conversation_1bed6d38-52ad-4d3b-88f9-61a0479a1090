/* Estilos para el Modal de Solicitud de Servicio */
.service-request-modal {
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    background: var(--gradient-card);
    border: 1px solid rgba(255, 255, 255, 0.12);
    border-radius: var(--radius-xl);
    backdrop-filter: var(--blur-backdrop);
    box-shadow: var(--shadow-2xl), var(--shadow-glow);
}

.service-request-form {
    padding: 0;
}

/* Pasos del formulario */
.form-step {
    display: none;
    padding: var(--space-xl);
    animation: fadeInUp 0.3s ease-out;
}

.form-step.active {
    display: block;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.step-header {
    text-align: center;
    margin-bottom: var(--space-xl);
}

.step-header h4 {
    font-size: 1.5rem;
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.step-header p {
    color: var(--text-secondary);
    font-size: 1rem;
}

/* Grid de formulario */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-lg);
    margin-bottom: var(--space-xl);
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.form-group input,
.form-group textarea,
.form-group select {
    padding: 0.75rem;
    background: var(--gradient-surface);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: var(--radius-lg);
    color: var(--text-primary);
    font-size: 0.9rem;
    transition: var(--transition-normal);
    backdrop-filter: var(--blur-backdrop);
    box-shadow: var(--shadow-sm);
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md), var(--shadow-glow);
    transform: translateY(-2px);
    background: var(--gradient-elevated);
}

.form-group small {
    color: var(--text-muted);
    font-size: 0.8rem;
}

/* Opciones de credenciales */
.credential-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-lg);
    margin-bottom: var(--space-xl);
}

.option-card {
    position: relative;
}

.option-card input[type="radio"] {
    display: none;
}

.option-label {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
    padding: var(--space-lg);
    background: var(--gradient-surface);
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-xl);
    cursor: pointer;
    transition: var(--transition-normal);
    text-align: center;
    backdrop-filter: var(--blur-backdrop);
    box-shadow: var(--shadow-sm);
}

.option-label i {
    font-size: 2rem;
    color: var(--text-secondary);
    transition: var(--transition-normal);
}

.option-label span {
    font-weight: 600;
    color: var(--text-primary);
}

.option-card input[type="radio"]:checked + .option-label {
    border-color: var(--primary-color);
    background: var(--gradient-elevated);
    box-shadow: var(--shadow-md), var(--shadow-glow);
    transform: translateY(-4px);
}

.option-card input[type="radio"]:checked + .option-label i {
    color: var(--primary-color);
    transform: scale(1.1);
}

/* Campos de credenciales */
.credentials-fields {
    transition: var(--transition-normal);
}

.credentials-fields.hidden {
    opacity: 0;
    pointer-events: none;
    height: 0;
    overflow: hidden;
}

/* Opciones de servicio */
.service-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-xl);
    margin-bottom: var(--space-xl);
}

.service-card {
    position: relative;
}

.service-card input[type="radio"] {
    display: none;
}

.service-label {
    display: block;
    padding: var(--space-xl);
    background: var(--gradient-surface);
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-xl);
    cursor: pointer;
    transition: var(--transition-normal);
    backdrop-filter: var(--blur-backdrop);
    box-shadow: var(--shadow-sm);
    text-align: center;
}

.service-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-lg);
    font-size: 2rem;
    color: white;
    box-shadow: var(--shadow-md), var(--shadow-glow);
    transition: var(--transition-normal);
}

.service-label h5 {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.service-label p {
    color: var(--text-secondary);
    margin-bottom: var(--space-lg);
}

.service-features {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.service-features span {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.service-features i {
    color: var(--primary-color);
}

.service-card input[type="radio"]:checked + .service-label {
    border-color: var(--primary-color);
    background: var(--gradient-elevated);
    box-shadow: var(--shadow-lg), var(--shadow-glow);
    transform: translateY(-8px);
}

.service-card input[type="radio"]:checked + .service-label .service-icon {
    transform: scale(1.1);
    box-shadow: var(--shadow-xl), var(--shadow-glow);
}

/* Grid de dispositivos */
.device-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--space-lg);
    margin-bottom: var(--space-xl);
}

.device-card {
    position: relative;
}

.device-card input[type="radio"] {
    display: none;
}

.device-label {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
    padding: var(--space-lg);
    background: var(--gradient-surface);
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-xl);
    cursor: pointer;
    transition: var(--transition-normal);
    text-align: center;
    backdrop-filter: var(--blur-backdrop);
    box-shadow: var(--shadow-sm);
}

.device-label i {
    font-size: 2.5rem;
    color: var(--text-secondary);
    transition: var(--transition-normal);
}

.device-label span {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.device-card input[type="radio"]:checked + .device-label {
    border-color: var(--primary-color);
    background: var(--gradient-elevated);
    box-shadow: var(--shadow-md), var(--shadow-glow);
    transform: translateY(-4px);
}

.device-card input[type="radio"]:checked + .device-label i {
    color: var(--primary-color);
    transform: scale(1.1);
}

/* Información del dispositivo */
.device-info,
.smart-tv-fields {
    margin-top: var(--space-lg);
    padding: var(--space-lg);
    background: rgba(255, 255, 255, 0.03);
    border-radius: var(--radius-lg);
    border: 1px solid rgba(255, 255, 255, 0.08);
}

/* Contenedor de aplicaciones */
.apps-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-lg);
    margin-bottom: var(--space-xl);
}

.app-card {
    background: var(--gradient-surface);
    border: 1px solid rgba(255, 255, 255, 0.12);
    border-radius: var(--radius-xl);
    padding: var(--space-lg);
    backdrop-filter: var(--blur-backdrop);
    box-shadow: var(--shadow-sm);
    transition: var(--transition-normal);
}

.app-card:hover {
    background: var(--gradient-elevated);
    border-color: var(--primary-color);
    transform: translateY(-4px);
    box-shadow: var(--shadow-md), var(--shadow-glow);
}

.app-header {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    margin-bottom: var(--space-md);
}

.app-icon {
    width: 48px;
    height: 48px;
    background: var(--gradient-primary);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    box-shadow: var(--shadow-sm), var(--shadow-glow);
}

.app-info h6 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.app-info .app-version {
    color: var(--text-secondary);
    font-size: 0.8rem;
}

.app-description {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: var(--space-md);
    line-height: 1.5;
}

.app-details {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: var(--space-md);
}

.app-tag {
    padding: 0.25rem 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.app-actions {
    display: flex;
    gap: 0.5rem;
}

.app-btn {
    flex: 1;
    padding: 0.5rem;
    background: var(--gradient-primary);
    color: white;
    border: none;
    border-radius: var(--radius-lg);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-normal);
    text-decoration: none;
    text-align: center;
    font-size: 0.8rem;
    box-shadow: var(--shadow-sm);
}

.app-btn:hover {
    background: var(--gradient-primary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md), var(--shadow-glow);
}

.app-btn.secondary {
    background: var(--gradient-surface);
    color: var(--text-primary);
}

/* Términos y condiciones */
.terms-container {
    margin-bottom: var(--space-xl);
}

.terms-content {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: var(--radius-lg);
    padding: var(--space-lg);
    margin-bottom: var(--space-lg);
    max-height: 200px;
    overflow-y: auto;
}

.terms-content h5 {
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: var(--space-md);
}

.terms-content ul {
    list-style: none;
    padding: 0;
}

.terms-content li {
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
    padding-left: 1.5rem;
    position: relative;
}

.terms-content li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--primary-color);
    font-weight: bold;
}

/* Checkbox personalizado */
.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    font-weight: 500;
    color: var(--text-primary);
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    background: var(--gradient-surface);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-sm);
    position: relative;
    transition: var(--transition-normal);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: var(--gradient-primary);
    border-color: var(--primary-color);
    box-shadow: var(--shadow-glow);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: bold;
    font-size: 0.8rem;
}

/* Acciones del formulario */
.form-actions {
    display: flex;
    justify-content: space-between;
    gap: var(--space-md);
    padding-top: var(--space-lg);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--radius-lg);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-normal);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    backdrop-filter: var(--blur-backdrop);
    box-shadow: var(--shadow-sm);
    text-decoration: none;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-sm), var(--shadow-glow);
}

.btn-primary:hover {
    background: var(--gradient-primary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md), var(--shadow-glow);
}

.btn-secondary {
    background: var(--gradient-surface);
    color: var(--text-primary);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-secondary:hover {
    background: var(--gradient-elevated);
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.btn-success {
    background: linear-gradient(135deg, #16a34a 0%, #15803d 100%);
    color: white;
    box-shadow: var(--shadow-sm), 0 0 15px rgba(22, 163, 74, 0.3);
}

.btn-success:hover {
    background: linear-gradient(135deg, #16a34a 0%, #15803d 100%);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md), 0 0 25px rgba(22, 163, 74, 0.5);
}

/* Indicador de progreso */
.progress-indicator {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    padding: var(--space-lg);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.02);
}

.progress-step {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--gradient-surface);
    border: 2px solid rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: var(--text-secondary);
    transition: var(--transition-normal);
    position: relative;
}

.progress-step.active {
    background: var(--gradient-primary);
    border-color: var(--primary-color);
    color: white;
    box-shadow: var(--shadow-glow);
    transform: scale(1.1);
}

.progress-step.completed {
    background: var(--gradient-primary);
    border-color: var(--primary-color);
    color: white;
}

.progress-step.completed::after {
    content: '✓';
    position: absolute;
    font-size: 0.8rem;
}

/* Responsive */
@media (max-width: 768px) {
    .service-request-modal {
        max-width: 95vw;
        margin: 1rem;
    }
    
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .credential-options,
    .service-options {
        grid-template-columns: 1fr;
    }
    
    .device-grid {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    }
    
    .apps-container {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .progress-indicator {
        gap: 0.5rem;
    }
    
    .progress-step {
        width: 30px;
        height: 30px;
        font-size: 0.8rem;
    }
}

/* Animaciones adicionales */
@keyframes shimmer {
    0% { background-position: -200px 0; }
    100% { background-position: calc(200px + 100%) 0; }
}

.loading {
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}
