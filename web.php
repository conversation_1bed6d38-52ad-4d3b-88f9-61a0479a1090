<?php
// --- CONFIGURACIÓN ---
$tmdbKey = '201066b4b17391d478e55247f43eed64';
$baseDir = 'series';
$usuario = 'Infest84';
$contrasena = 'Panama25#'; // cámbiala por seguridad
$pinDescarga = '1984';

// --- FUNCIONES ---
function getSeriesInfo($name, $tmdbKey) {
    $query = urlencode($name);
    $url = "https://api.themoviedb.org/3/search/tv?api_key=$tmdbKey&query=$query&language=es-ES";
    $res = @file_get_contents($url);
    $data = json_decode($res, true);
    return $data['results'][0] ?? null;
}

// NUEVA FUNCIÓN PARA PELÍCULAS
function getMovieInfo($name, $tmdbKey) {
    $query = urlencode($name);
    $url = "https://api.themoviedb.org/3/search/movie?api_key=$tmdbKey&query=$query&language=es-ES";
    $res = @file_get_contents($url);
    $data = json_decode($res, true);
    return $data['results'][0] ?? null;
}

// Función para compatibilidad con versiones anteriores de PHP
if (!function_exists('str_starts_with')) {
    function str_starts_with($haystack, $needle) {
        return strpos($haystack, $needle) === 0;
    }
}
function isMobile() {
    return preg_match('/Mobile|Android|iPhone|iPad|iPod|Opera Mini|IEMobile|BlackBerry/i', $_SERVER['HTTP_USER_AGENT']);
}

// --- VER DETALLE DE UNA SERIE POR ID ---
if (isset($_GET['id']) && is_numeric($_GET['id'])) {
    $tmdbId = intval($_GET['id']);
    $url = "https://api.themoviedb.org/3/tv/$tmdbId?api_key=$tmdbKey&language=es-ES";
    $res = @file_get_contents($url);
    $data = json_decode($res, true);
    if (!$data || isset($data['status_code'])) {
        die("<h2>❌ Serie no encontrada con ID $tmdbId</h2>");
    }

    $nombre = $data['name'];
    $poster = $data['poster_path'];
    $normalizedTarget = strtolower(preg_replace('/[^a-z0-9]/', '', $nombre));
    $m3uPath = '';

    foreach (glob("$baseDir/*/*.m3u") as $fileFound) {
        $foundName = pathinfo($fileFound, PATHINFO_FILENAME);
        $normalizedFound = strtolower(preg_replace('/[^a-z0-9]/', '', $foundName));
        if ($normalizedFound === $normalizedTarget) {
            $m3uPath = $fileFound;
            break;
        }
    }
    
    $isMobile = isMobile();
    $posterWidth = $isMobile ? 150 : 300;


    echo "<!DOCTYPE html><html><head><meta charset='utf-8'><title>$nombre</title>
    <link href='https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap' rel='stylesheet'>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css'>
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #1e293b;
            --dark-bg: #0f172a;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --accent-color: #10b981;
            --error-color: #ef4444;
            --border-color: #334155;
            --gradient-bg: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            --gradient-primary: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            --shadow-medium: 0 4px 16px rgba(0,0,0,0.4);
            --border-radius: 12px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * { margin: 0; padding: 0; box-sizing: border-box; }

        body {
            font-family: 'Inter', sans-serif;
            background: var(--gradient-bg);
            color: var(--text-primary);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }

        .detail-container {
            background: var(--secondary-color);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            padding: 3rem;
            text-align: center;
            max-width: 500px;
            width: 100%;
            box-shadow: var(--shadow-medium);
        }

        .detail-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 2rem;
        }

        .detail-poster {
            margin-bottom: 2rem;
        }

        .detail-poster img {
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-medium);
            max-width: 250px;
            width: 100%;
            height: auto;
        }

        .detail-info {
            color: var(--text-secondary);
            margin-bottom: 2rem;
            font-size: 0.9rem;
        }

        .detail-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        button {
            padding: 0.875rem 1.5rem;
            font-size: 1rem;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-weight: 600;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-family: inherit;
        }

        .btn-download {
            background: var(--gradient-primary);
            color: white;
        }

        .btn-download:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        .btn-back {
            background: var(--dark-bg);
            color: var(--text-secondary);
            border: 1px solid var(--border-color);
        }

        .btn-back:hover {
            background: var(--secondary-color);
            color: var(--text-primary);
            transform: translateY(-2px);
        }

        .error-message {
            color: var(--error-color);
            font-size: 1.1rem;
            margin-top: 1rem;
        }

        @media (max-width: 768px) {
            .detail-container {
                padding: 2rem;
                margin: 1rem;
            }

            .detail-title {
                font-size: 1.5rem;
            }

            .detail-actions {
                flex-direction: column;
            }

            button {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
    <script>
        function descargarConPin(url) {
            const pin = prompt('🔒 Ingresa el PIN para descargar:');
            if (pin === '$pinDescarga') {
                window.location.href = url;
            } else {
                alert('❌ PIN incorrecto.');
            }
        }
    </script>
    </head><body>";

    echo "<div class='detail-container'>";
    echo "<h1 class='detail-title'>$nombre</h1>";

    if ($poster) {
        echo "<div class='detail-poster'>";
        echo "<img src='https://image.tmdb.org/t/p/w500$poster' alt='" . htmlspecialchars($nombre) . "'>";
        echo "</div>";
    }

    echo "<div class='detail-info'>";
    echo "<p><i class='fas fa-database'></i> ID TMDB: $tmdbId</p>";
    echo "</div>";

    echo "<div class='detail-actions'>";
    if ($m3uPath) {
        echo "<button class='btn-download' onclick=\"descargarConPin('$m3uPath')\">
                <i class='fas fa-download'></i>
                Descargar lista
              </button>";
    } else {
        echo "<div class='error-message'>
                <i class='fas fa-exclamation-triangle'></i>
                Archivo M3U no encontrado en el servidor
              </div>";
    }

    echo "<a href='web.php'>
            <button class='btn-back'>
                <i class='fas fa-arrow-left'></i>
                Volver
            </button>
          </a>";
    echo "</div>";
    echo "</div></body></html>";
    exit;
}

// --- AUTENTICACIÓN BÁSICA ---
if (!isset($_SERVER['PHP_AUTH_USER']) || $_SERVER['PHP_AUTH_USER'] !== $usuario || $_SERVER['PHP_AUTH_PW'] !== $contrasena) {
    header('WWW-Authenticate: Basic realm="Zona restringida"');
    header('HTTP/1.0 401 Unauthorized');
    echo 'Acceso denegado.';
    exit;
}

// --- SUBIDA Y PROCESADO DE ARCHIVO ---
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['lista']) && isset($_POST['carpeta'])) {
    $carpeta = preg_replace('/[^a-zA-Z0-9_-]/', '', $_POST['carpeta']);
    $uploadDir = "$baseDir/$carpeta";
    if (!is_dir($uploadDir)) mkdir($uploadDir, 0777, true);

    $lines = file($_FILES['lista']['tmp_name']);
    $seriesMap = [];
    $currentExtinf = '';

    foreach ($lines as $line) {
        $line = trim($line);
        if (!$line) continue;

        if (str_starts_with($line, '#EXTINF')) {
            preg_match('/#EXTINF.*?,\s*(.+)/', $line, $matches);
            $fullTitle = $matches[1] ?? 'Desconocido';

            if (preg_match('/^(.*?)(?:\s*[-:]?\s*)?(S\d{1,2}E\d{1,2}|T\d+E\d+|\d{1,2}x\d{1,2}|Temporada\s*\d+)/i', $fullTitle, $match)) {
                $seriesName = trim($match[1]);
                $episodeInfo = $match[2];
            } else {
                $seriesName = 'Desconocido';
                $episodeInfo = '';
            }

            $season = 'Temporada 0';
            if (preg_match('/S(\d{1,2})E\d{1,2}/i', $episodeInfo, $m)) $season = 'Temporada ' . intval($m[1]);
            elseif (preg_match('/T(\d+)E\d+/i', $episodeInfo, $m)) $season = 'Temporada ' . intval($m[1]);
            elseif (preg_match('/(\d{1,2})x\d{1,2}/', $episodeInfo, $m)) $season = 'Temporada ' . intval($m[1]);
            elseif (preg_match('/Temporada\s+(\d+)/i', $episodeInfo, $m)) $season = 'Temporada ' . intval($m[1]);

            $seriesKey = strtolower(str_replace(' ', '_', preg_replace('/[^a-zA-Z0-9 _-]/', '', $seriesName)));
            if (!isset($seriesMap[$seriesKey])) $seriesMap[$seriesKey] = ['name' => $seriesName, 'seasons' => []];
            if (!isset($seriesMap[$seriesKey]['seasons'][$season])) $seriesMap[$seriesKey]['seasons'][$season] = [];

            $seriesMap[$seriesKey]['seasons'][$season][] = ['extinf' => $line, 'url' => null];
        } elseif (str_starts_with($line, 'http')) {
            $seriesMap[$seriesKey]['seasons'][$season][count($seriesMap[$seriesKey]['seasons'][$season]) - 1]['url'] = $line;
        }
    }

    $cacheFile = "$uploadDir/.series_cache.json";
    $cache = [];
    if (file_exists($cacheFile)) {
        $cache = json_decode(file_get_contents($cacheFile), true) ?: [];
    }

    foreach ($seriesMap as $key => $data) {
        $filename = "$uploadDir/{$key}.m3u";
        // Si el archivo ya existe, saltar para evitar duplicados
        if (file_exists($filename)) continue;
        $output = "#EXTM3U\n";
        uksort($data['seasons'], fn($a, $b) => intval(filter_var($a, FILTER_SANITIZE_NUMBER_INT)) <=> intval(filter_var($b, FILTER_SANITIZE_NUMBER_INT)));
        foreach ($data['seasons'] as $season => $episodes) {
            $output .= "\n# --- $season ---\n";
            foreach ($episodes as $ep) {
                if ($ep['extinf'] && $ep['url']) {
                    $output .= $ep['extinf'] . "\n" . $ep['url'] . "\n";
                }
            }
        }
        file_put_contents($filename, $output);
        // Guardar en cache
        $cache[$key] = [
            'name' => $data['name'],
            'file' => $filename,
            'carpeta' => $carpeta
        ];
    }
    file_put_contents($cacheFile, json_encode($cache, JSON_UNESCAPED_UNICODE|JSON_PRETTY_PRINT));

    header("Location: web.php?carpeta=$carpeta&success=1");
    exit;
}

// --- ENDPOINT AJAX PARA DETALLES EMERGENTES ---
if (isset($_GET['ajax']) && $_GET['ajax'] === 'detalles' && isset($_GET['type'], $_GET['id'])) {
    $type = $_GET['type'];
    $id = intval($_GET['id']);
    if ($type === 'serie') {
        $url = "https://api.themoviedb.org/3/tv/$id?api_key=$tmdbKey&language=es-ES";
    } else {
        $url = "https://api.themoviedb.org/3/movie/$id?api_key=$tmdbKey&language=es-ES";
    }
    $res = @file_get_contents($url);
    $data = json_decode($res, true);
    if (!$data || isset($data['status_code'])) {
        echo "<p>No se encontraron detalles.</p>";
        exit;
    }
    echo "<h2>" . htmlspecialchars($data['name'] ?? $data['title']) . "</h2>";
    if (!empty($data['poster_path'])) {
        echo "<img src='https://image.tmdb.org/t/p/w300" . $data['poster_path'] . "' style='width:120px;border-radius:8px;'><br>";
    }
    echo "<p><b>Año:</b> " . htmlspecialchars(substr($data['first_air_date'] ?? $data['release_date'] ?? '', 0, 4)) . "</p>";
    echo "<p><b>Sinopsis:</b> " . htmlspecialchars($data['overview']) . "</p>";
    exit;
}

// --- LISTADO DE SERIES Y PELÍCULAS ---
$peliculasDir = 'peliculas';
$peliculas = [];
if (is_dir($peliculasDir)) {
    $peliculas = glob("$peliculasDir/*.m3u");
}

$subdir = isset($_GET['carpeta']) ? preg_replace('/[^a-zA-Z0-9_-]/', '', $_GET['carpeta']) : '';
$currentDir = $subdir ? "$baseDir/$subdir" : $baseDir;
$cacheFile = "$currentDir/.series_cache.json";
if (file_exists($cacheFile)) {
    $seriesCache = json_decode(file_get_contents($cacheFile), true) ?: [];
    $series = array_map(function($item) { return $item['file']; }, $seriesCache);
} else {
    $series = glob("$currentDir/*.m3u");
}
$carpetas = array_filter(glob("$baseDir/*"), 'is_dir');
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RGSMedia Series</title>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            /* Paleta de colores moderna y profesional */
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #1e293b;
            --dark-bg: #0f172a;
            --darker-bg: #020617;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --accent-color: #10b981;
            --accent-dark: #059669;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --success-color: #10b981;
            --info-color: #06b6d4;
            --border-color: #334155;
            --border-light: #475569;
            --gradient-primary: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            --gradient-secondary: linear-gradient(135deg, #10b981 0%, #059669 100%);
            --gradient-dark: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
            --gradient-bg: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            --shadow-light: 0 2px 8px rgba(0,0,0,0.3);
            --shadow-medium: 0 4px 16px rgba(0,0,0,0.4);
            --shadow-heavy: 0 8px 32px rgba(0,0,0,0.6);
            --border-radius: 12px;
            --border-radius-lg: 16px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gradient-bg);
            color: var(--text-primary);
            line-height: 1.5;
            min-height: 100vh;
            padding: 1rem;
        }

        /* Header */
        .header {
            text-align: center;
            margin-bottom: 1.5rem;
            padding: 1.5rem 0;
            background: var(--gradient-dark);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            box-shadow: var(--shadow-medium);
        }

        h1 {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.25rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            color: var(--text-secondary);
            font-size: 0.95rem;
            font-weight: 400;
        }

        /* Secciones principales */
        .section {
            background: var(--secondary-color);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            margin-bottom: 1.25rem;
            overflow: hidden;
            box-shadow: var(--shadow-light);
        }

        .section-header {
            background: var(--dark-bg);
            padding: 1rem 1.5rem;
            border-bottom: 1px solid var(--border-color);
        }

        .section-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .section-content {
            padding: 1.25rem;
        }

        /* Secciones compactas para formularios */
        .section.compact {
            margin-bottom: 1rem;
        }

        .section.compact .section-header {
            padding: 0.75rem 1.25rem;
        }

        .section.compact .section-content {
            padding: 1rem;
        }

        .section.compact .section-title {
            font-size: 1rem;
        }

        /* Formularios */
        .form-group {
            margin-bottom: 1rem;
        }

        .form-group.compact {
            margin-bottom: 0.75rem;
        }

        .form-label {
            display: block;
            color: var(--text-secondary);
            margin-bottom: 0.375rem;
            font-size: 0.85rem;
            font-weight: 500;
        }

        input, select, button {
            width: 100%;
            padding: 0.625rem 0.875rem;
            font-size: 0.9rem;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            transition: var(--transition);
            font-family: inherit;
        }

        /* Formularios en línea para secciones compactas */
        .form-inline {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            align-items: end;
        }

        input, select {
            background: var(--dark-bg);
            color: var(--text-primary);
        }

        input:focus, select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        button {
            background: var(--gradient-primary);
            color: white;
            cursor: pointer;
            font-weight: 600;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        button:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        /* Barra de progreso */
        .progress-container {
            display: none;
            margin: 1.5rem 0;
        }

        .progress-wrapper {
            background: var(--dark-bg);
            border-radius: var(--border-radius);
            overflow: hidden;
            border: 1px solid var(--border-color);
        }

        .progress-bar {
            width: 0%;
            height: 40px;
            background: var(--gradient-secondary);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            transition: width 0.3s ease;
            position: relative;
        }

        .progress-text {
            margin-top: 1rem;
            text-align: center;
            font-size: 0.9rem;
            color: var(--text-secondary);
        }

        /* Grid de series */
        .series-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .series-card {
            background: var(--secondary-color);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            overflow: hidden;
            transition: var(--transition);
            box-shadow: var(--shadow-light);
        }

        .series-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-medium);
            border-color: var(--primary-color);
        }

        .series-poster {
            position: relative;
            width: 100%;
            height: 200px;
            overflow: hidden;
        }

        .series-poster img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: var(--transition);
        }

        .series-card:hover .series-poster img {
            transform: scale(1.03);
        }

        .poster-placeholder {
            width: 100%;
            height: 100%;
            background: var(--gradient-dark);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-secondary);
            font-size: 2rem;
        }

        .series-info {
            padding: 0.875rem;
        }

        .series-title {
            font-size: 0.9rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.75rem;
            line-height: 1.2;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            min-height: 2.4rem;
        }

        .series-actions {
            display: flex;
            gap: 0.5rem;
            flex-direction: column;
        }

        .btn-details {
            background: var(--info-color);
            color: white;
            padding: 0.375rem 0.75rem;
            border: none;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.375rem;
        }

        .btn-details:hover {
            background: #0891b2;
            transform: translateY(-1px);
        }

        .btn-download {
            background: var(--accent-color);
            color: white;
            padding: 0.375rem 0.75rem;
            border: none;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.375rem;
        }

        .btn-download:hover {
            background: var(--accent-dark);
            transform: translateY(-1px);
        }

        /* Mensaje de éxito */
        .success-message {
            background: var(--gradient-secondary);
            color: white;
            padding: 1.5rem;
            border-radius: var(--border-radius);
            margin-bottom: 2rem;
            text-align: center;
            font-weight: 500;
            box-shadow: var(--shadow-light);
        }

        /* Modal de detalles */
        #modalDetalles {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(0,0,0,0.8);
            z-index: 9999;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(4px);
        }

        #modalContent {
            background: var(--secondary-color);
            padding: 2rem;
            border-radius: var(--border-radius-lg);
            max-width: 500px;
            width: 90%;
            color: var(--text-primary);
            position: relative;
            border: 1px solid var(--border-color);
            box-shadow: var(--shadow-heavy);
            max-height: 80vh;
            overflow-y: auto;
        }

        #modalContent .close-btn {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: var(--error-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: 0.5rem 0.75rem;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            width: auto;
        }

        #modalContent .close-btn:hover {
            background: #dc2626;
        }

        /* Animaciones de carga y transiciones */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(15, 23, 42, 0.95);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            backdrop-filter: blur(8px);
        }

        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid var(--border-color);
            border-top: 4px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 1.5rem;
        }

        .loading-text {
            color: var(--text-primary);
            font-size: 1.1rem;
            font-weight: 500;
            text-align: center;
            margin-bottom: 0.5rem;
        }

        .loading-subtext {
            color: var(--text-secondary);
            font-size: 0.9rem;
            text-align: center;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Animación de aparición de tarjetas */
        .series-card {
            opacity: 0;
            transform: translateY(20px);
            animation: fadeInUp 0.6s ease forwards;
        }

        .series-card:nth-child(1) { animation-delay: 0.1s; }
        .series-card:nth-child(2) { animation-delay: 0.2s; }
        .series-card:nth-child(3) { animation-delay: 0.3s; }
        .series-card:nth-child(4) { animation-delay: 0.4s; }
        .series-card:nth-child(5) { animation-delay: 0.5s; }
        .series-card:nth-child(6) { animation-delay: 0.6s; }
        .series-card:nth-child(n+7) { animation-delay: 0.7s; }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Animación de búsqueda */
        .search-loading {
            position: relative;
        }

        .search-loading::after {
            content: '';
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            width: 16px;
            height: 16px;
            border: 2px solid var(--border-color);
            border-top: 2px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        /* Animación de filtrado */
        .series-card.filtering {
            transition: all 0.3s ease;
        }

        .series-card.hidden {
            opacity: 0;
            transform: scale(0.8);
            pointer-events: none;
        }

        /* Animación de recarga de contenido */
        .content-reloading {
            position: relative;
            overflow: hidden;
        }

        .content-reloading::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(37, 99, 235, 0.2), transparent);
            animation: shimmer 1.5s infinite;
            z-index: 1;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        /* Animación de pulsación para elementos activos */
        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.05);
                opacity: 0.8;
            }
        }

        /* Skeleton loading para tarjetas */
        .skeleton-card {
            background: var(--secondary-color);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            overflow: hidden;
            position: relative;
        }

        .skeleton-poster {
            width: 100%;
            height: 280px;
            background: var(--dark-bg);
            position: relative;
            overflow: hidden;
        }

        .skeleton-info {
            padding: 1.5rem;
        }

        .skeleton-title {
            height: 20px;
            background: var(--dark-bg);
            border-radius: 4px;
            margin-bottom: 1rem;
        }

        .skeleton-button {
            height: 36px;
            background: var(--dark-bg);
            border-radius: 6px;
            margin-bottom: 0.75rem;
        }

        .skeleton-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            animation: skeletonShimmer 1.5s infinite;
        }

        @keyframes skeletonShimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        /* Responsive */
        @media (max-width: 768px) {
            body {
                padding: 0.75rem;
            }

            h1 {
                font-size: 1.75rem;
            }

            .series-grid {
                grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
                gap: 0.75rem;
            }

            .series-poster {
                height: 170px;
            }

            .series-info {
                padding: 0.75rem;
            }

            .section-content {
                padding: 1rem;
            }

            .section.compact .section-content {
                padding: 0.75rem;
            }

            .form-inline {
                grid-template-columns: 1fr;
                gap: 0.75rem;
            }

            .loading-spinner {
                width: 50px;
                height: 50px;
            }

            .loading-text {
                font-size: 1rem;
            }
        }

        @media (max-width: 480px) {
            body {
                padding: 0.5rem;
            }

            .header {
                margin-bottom: 1rem;
                padding: 1rem 0;
            }

            h1 {
                font-size: 1.5rem;
            }

            .subtitle {
                font-size: 0.85rem;
            }

            .series-grid {
                grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
                gap: 0.5rem;
            }

            .series-poster {
                height: 140px;
            }

            .series-info {
                padding: 0.5rem;
            }

            .series-title {
                font-size: 0.8rem;
                margin-bottom: 0.5rem;
                min-height: 2rem;
            }

            .series-actions {
                gap: 0.375rem;
            }

            .btn-details, .btn-download {
                padding: 0.3rem 0.5rem;
                font-size: 0.75rem;
            }

            .section {
                margin-bottom: 0.75rem;
            }

            .section.compact {
                margin-bottom: 0.5rem;
            }

            .loading-spinner {
                width: 40px;
                height: 40px;
            }
        }

        /* Para pantallas muy grandes, mostrar más columnas */
        @media (min-width: 1200px) {
            .series-grid {
                grid-template-columns: repeat(auto-fill, minmax(130px, 1fr));
            }
        }

        @media (min-width: 1600px) {
            .series-grid {
                grid-template-columns: repeat(auto-fill, minmax(125px, 1fr));
            }
        }
    </style>
    <script>
        // Variables globales para control de animaciones
        let searchTimeout;
        let isSearching = false;

        // Función de filtrado con animación
        function filterSeries() {
            const searchInput = document.getElementById('search');
            const searchValue = searchInput.value.toLowerCase();

            // Agregar indicador de búsqueda
            if (!isSearching && searchValue.length > 0) {
                isSearching = true;
                searchInput.classList.add('search-loading');

                // Simular tiempo de búsqueda para mostrar animación
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    performSearch(searchValue);
                    searchInput.classList.remove('search-loading');
                    isSearching = false;
                }, 300);
            } else if (searchValue.length === 0) {
                // Búsqueda vacía - mostrar todos inmediatamente
                clearTimeout(searchTimeout);
                searchInput.classList.remove('search-loading');
                isSearching = false;
                performSearch(searchValue);
            }
        }

        function performSearch(searchValue) {
            const cards = document.querySelectorAll('.series-card');
            const searchStatus = document.getElementById('search-status');
            const noResults = document.getElementById('no-results');
            let visibleCount = 0;

            cards.forEach((card, index) => {
                const title = card.getAttribute('data-name').toLowerCase();
                const shouldShow = searchValue === '' || title.includes(searchValue);

                if (shouldShow) {
                    visibleCount++;
                    card.classList.remove('hidden');
                    // Reanimación escalonada para resultados
                    setTimeout(() => {
                        card.style.animation = 'none';
                        card.offsetHeight; // Trigger reflow
                        card.style.animation = `fadeInUp 0.4s ease forwards`;
                        card.style.animationDelay = `${index * 0.05}s`;
                    }, 50);
                } else {
                    card.classList.add('hidden');
                }
            });

            // Mostrar/ocultar mensaje de no resultados
            if (searchValue && visibleCount === 0) {
                noResults.style.display = 'block';
                noResults.style.opacity = '0';
                setTimeout(() => {
                    noResults.style.transition = 'opacity 0.3s ease';
                    noResults.style.opacity = '1';
                }, 100);
            } else {
                noResults.style.display = 'none';
            }

            // Actualizar contador de resultados
            setTimeout(() => {
                if (searchValue) {
                    if (visibleCount > 0) {
                        searchStatus.innerHTML = `<i class="fas fa-check"></i> ${visibleCount} resultado${visibleCount !== 1 ? 's' : ''}`;
                        searchStatus.style.color = 'var(--success-color)';
                    } else {
                        searchStatus.innerHTML = `<i class="fas fa-times"></i> Sin resultados`;
                        searchStatus.style.color = 'var(--error-color)';
                    }
                } else {
                    searchStatus.style.opacity = '0';
                }
            }, 400);
        }

        function showSearchStatus() {
            const searchStatus = document.getElementById('search-status');
            const searchValue = document.getElementById('search').value;

            if (searchValue.length > 0) {
                searchStatus.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Buscando...';
                searchStatus.style.color = 'var(--accent-color)';
                searchStatus.style.opacity = '1';
            } else {
                searchStatus.style.opacity = '0';
            }
        }

        function descargarConPin(url) {
            const pin = prompt('🔒 Ingresa el PIN para descargar:');
            if (pin === '<?= $pinDescarga ?>') {
                showLoadingOverlay('Preparando descarga...', 'Iniciando descarga del archivo');

                setTimeout(() => {
                    var link = document.createElement('a');
                    link.href = url;
                    link.download = url.substring(url.lastIndexOf('/') + 1);
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    hideLoadingOverlay();
                }, 1000);
            } else {
                alert('❌ PIN incorrecto.');
            }
        }

        // Función para mostrar overlay de carga
        function showLoadingOverlay(title = 'Cargando...', subtitle = 'Por favor espera') {
            const overlay = document.createElement('div');
            overlay.className = 'loading-overlay';
            overlay.id = 'loadingOverlay';

            overlay.innerHTML = `
                <div class="loading-spinner"></div>
                <div class="loading-text">${title}</div>
                <div class="loading-subtext">${subtitle}</div>
            `;

            document.body.appendChild(overlay);
        }

        // Función para ocultar overlay de carga
        function hideLoadingOverlay() {
            const overlay = document.getElementById('loadingOverlay');
            if (overlay) {
                overlay.style.opacity = '0';
                setTimeout(() => {
                    overlay.remove();
                }, 300);
            }
        }

        // Función para recargar contenido con animación
        function reloadContent() {
            const contentSection = document.querySelector('.series-grid').parentElement;
            contentSection.classList.add('content-reloading');

            showLoadingOverlay('Recargando contenido...', 'Actualizando la biblioteca');

            setTimeout(() => {
                location.reload();
            }, 1500);
        }

        // Función para cambiar carpeta con animación
        function changeFolderWithAnimation(value) {
            if (value !== new URLSearchParams(window.location.search).get('carpeta') || '') {
                showLoadingOverlay('Cambiando carpeta...', 'Cargando contenido de la carpeta');
                setTimeout(() => {
                    location.href = 'web.php?carpeta=' + value;
                }, 800);
            }
        }

        // MODAL PARA DETALLES
        function verDetalles(tipo, id) {
            if (!id) return;
            fetch('web.php?ajax=detalles&type=' + tipo + '&id=' + id)
                .then(r => r.text())
                .then(html => {
                    document.getElementById('detallesBody').innerHTML = html;
                    document.getElementById('modalDetalles').style.display = 'flex';
                });
        }
        function cerrarModal() {
            document.getElementById('modalDetalles').style.display = 'none';
        }

        // Función para manejar la subida con barra de progreso
        function handleFormSubmit(event) {
            event.preventDefault();

            const form = event.target;
            const formData = new FormData(form);
            const progressContainer = document.getElementById('progress-container');
            const progressBar = document.getElementById('progress-bar');
            const progressText = document.getElementById('progress-text');
            const submitButton = form.querySelector('button[type="submit"]');

            // Mostrar barra de progreso con animación
            progressContainer.style.display = 'block';
            progressContainer.style.opacity = '0';
            setTimeout(() => {
                progressContainer.style.opacity = '1';
                progressContainer.style.transition = 'opacity 0.3s ease';
            }, 100);

            submitButton.disabled = true;
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Procesando...';

            // Simular progreso de subida y procesamiento
            let progress = 0;
            progressText.textContent = 'Subiendo archivo...';

            const progressInterval = setInterval(() => {
                progress += Math.random() * 15;
                if (progress > 95) progress = 95;

                progressBar.style.width = progress + '%';
                progressBar.textContent = Math.round(progress) + '%';

                if (progress < 30) {
                    progressText.textContent = 'Subiendo archivo...';
                } else if (progress < 60) {
                    progressText.textContent = 'Analizando contenido...';
                } else if (progress < 90) {
                    progressText.textContent = 'Organizando series...';
                } else {
                    progressText.textContent = 'Finalizando proceso...';
                }
            }, 200);

            // Realizar la subida real
            const xhr = new XMLHttpRequest();

            xhr.upload.onprogress = function(e) {
                if (e.lengthComputable) {
                    const realProgress = (e.loaded / e.total) * 50; // 50% para la subida
                    if (realProgress > progress) {
                        progress = realProgress;
                        progressBar.style.width = progress + '%';
                        progressBar.textContent = Math.round(progress) + '%';
                    }
                }
            };

            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    clearInterval(progressInterval);

                    if (xhr.status === 200) {
                        // Completar la barra de progreso
                        progressBar.style.width = '100%';
                        progressBar.textContent = '100%';
                        progressText.textContent = '¡Procesamiento completado!';

                        // Mostrar overlay de carga para la redirección
                        setTimeout(() => {
                            showLoadingOverlay('Redirigiendo...', 'Cargando contenido actualizado');
                            setTimeout(() => {
                                if (xhr.responseURL) {
                                    window.location.href = xhr.responseURL;
                                } else {
                                    window.location.reload();
                                }
                            }, 1000);
                        }, 1000);
                    } else {
                        progressText.textContent = 'Error en el procesamiento';
                        progressBar.style.background = 'var(--error-color)';
                        submitButton.disabled = false;
                        submitButton.innerHTML = '<i class="fas fa-upload"></i> Subir y procesar lista';
                    }
                }
            };

            xhr.open('POST', form.action);
            xhr.send(formData);
        }

        // Función para crear skeleton loading
        function createSkeletonCards(count = 6) {
            const grid = document.querySelector('.series-grid');
            grid.innerHTML = '';

            for (let i = 0; i < count; i++) {
                const skeletonCard = document.createElement('div');
                skeletonCard.className = 'skeleton-card';
                skeletonCard.innerHTML = `
                    <div class="skeleton-poster"></div>
                    <div class="skeleton-info">
                        <div class="skeleton-title"></div>
                        <div class="skeleton-button"></div>
                        <div class="skeleton-button"></div>
                    </div>
                `;
                grid.appendChild(skeletonCard);
            }
        }

        // Función de carga inicial
        function initializePageAnimations() {
            // Mostrar skeleton loading si hay contenido
            const seriesGrid = document.querySelector('.series-grid');
            if (seriesGrid && seriesGrid.children.length > 0) {
                // Animar entrada de tarjetas existentes
                const cards = seriesGrid.querySelectorAll('.series-card');
                cards.forEach((card, index) => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    setTimeout(() => {
                        card.style.transition = 'all 0.6s ease';
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, index * 100);
                });
            }
        }

        // Función para animar cambios de contenido
        function animateContentChange() {
            const contentSection = document.querySelector('.series-grid');
            if (contentSection) {
                contentSection.style.opacity = '0.5';
                contentSection.style.transform = 'scale(0.95)';
                contentSection.style.transition = 'all 0.3s ease';

                setTimeout(() => {
                    contentSection.style.opacity = '1';
                    contentSection.style.transform = 'scale(1)';
                }, 300);
            }
        }
    </script>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <h1><i class="fas fa-film"></i> RGSMedia Series</h1>
        <p class="subtitle">Gestión y descarga de contenido multimedia</p>
    </div>

    <?php if (isset($_GET['success']) && $_GET['success'] == '1'): ?>
        <div class="success-message">
            <i class="fas fa-check-circle"></i>
            Archivo procesado correctamente. Las series han sido organizadas y están disponibles para descargar.
        </div>
    <?php endif; ?>

    <!-- Sección de subida -->
    <div class="section compact">
        <div class="section-header">
            <h2 class="section-title">
                <i class="fas fa-upload"></i>
                Subir Lista M3U
            </h2>
        </div>
        <div class="section-content">
            <form action="web.php" method="post" enctype="multipart/form-data" onsubmit="handleFormSubmit(event)">
                <div class="form-inline">
                    <div class="form-group compact">
                        <label class="form-label" for="lista">
                            <i class="fas fa-file"></i> Archivo M3U
                        </label>
                        <input type="file" id="lista" name="lista" accept=".txt,.m3u" required>
                    </div>
                    <div class="form-group compact">
                        <label class="form-label" for="carpeta">
                            <i class="fas fa-folder"></i> Carpeta
                        </label>
                        <input type="text" id="carpeta" name="carpeta" placeholder="Ej: Series 2024" required>
                    </div>
                </div>
                <button type="submit" style="margin-top: 0.5rem;">
                    <i class="fas fa-upload"></i>
                    Subir y procesar
                </button>
            </form>

            <div id="progress-container" class="progress-container">
                <div class="progress-wrapper">
                    <div id="progress-bar" class="progress-bar">0%</div>
                </div>
                <div id="progress-text" class="progress-text">Preparando...</div>
            </div>
        </div>
    </div>

    <!-- Sección de búsqueda y filtros -->
    <div class="section compact">
        <div class="section-header">
            <h2 class="section-title">
                <i class="fas fa-search"></i>
                Buscar y Filtrar
            </h2>
        </div>
        <div class="section-content">
            <div class="form-inline">
                <div class="form-group compact">
                    <label class="form-label" for="folder-select">
                        <i class="fas fa-folder-open"></i> Carpeta
                    </label>
                    <select id="folder-select" onchange="changeFolderWithAnimation(this.value)">
                        <option value="">Todas las carpetas</option>
                        <?php foreach ($carpetas as $c): $n = basename($c); ?>
                            <option value="<?= $n ?>" <?= $n === $subdir ? 'selected' : '' ?>><?= $n ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="form-group compact">
                    <label class="form-label" for="search">
                        <i class="fas fa-search"></i> Buscar
                        <span id="search-status" style="margin-left: 0.5rem; font-size: 0.75rem; color: var(--accent-color); opacity: 0;">
                            <i class="fas fa-spinner fa-spin"></i> Buscando...
                        </span>
                    </label>
                    <input type="text" id="search" placeholder="Buscar por título..." onkeyup="filterSeries()" oninput="showSearchStatus()">
                </div>
            </div>
        </div>
    </div>

    <!-- Sección de contenido -->
    <div class="section">
        <div class="section-header">
            <h2 class="section-title">
                <i class="fas fa-tv"></i>
                Biblioteca
                <span style="margin-left: auto; font-size: 0.8rem; color: var(--text-secondary);">
                    <?= count($series) + count($peliculas) ?> elementos
                </span>
            </h2>
            <div style="display: flex; gap: 0.375rem;">
                <button onclick="reloadContent()" style="background: var(--accent-color); color: white; border: none; padding: 0.375rem 0.75rem; border-radius: 4px; cursor: pointer; display: flex; align-items: center; gap: 0.375rem; font-size: 0.8rem;">
                    <i class="fas fa-sync-alt"></i>
                    Recargar
                </button>
                <button onclick="animateContentChange()" style="background: var(--info-color); color: white; border: none; padding: 0.375rem 0.75rem; border-radius: 4px; cursor: pointer; display: flex; align-items: center; gap: 0.375rem; font-size: 0.8rem;">
                    <i class="fas fa-magic"></i>
                    Animar
                </button>
            </div>
        </div>
        <div class="section-content">
            <div id="no-results" style="display: none; text-align: center; padding: 3rem; color: var(--text-secondary);">
                <i class="fas fa-search" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                <h3 style="margin-bottom: 0.5rem; color: var(--text-primary);">No se encontraron resultados</h3>
                <p>Intenta con otros términos de búsqueda</p>
            </div>
            <div class="series-grid">
                <?php foreach ($series as $file):
                    $filename = basename($file);
                    $displayName = ucwords(str_replace('_', ' ', pathinfo($filename, PATHINFO_FILENAME)));
                    $info = getSeriesInfo($displayName, $tmdbKey);
                    $tmdbId = $info['id'] ?? null;
                    $poster = $info['poster_path'] ?? null;
                ?>
                <div class="series-card" data-name="<?= $displayName ?>">
                    <div class="series-poster">
                        <?php if ($poster): ?>
                            <img src="https://image.tmdb.org/t/p/w500<?= $poster ?>" alt="<?= htmlspecialchars($displayName) ?>">
                        <?php else: ?>
                            <div class="poster-placeholder">
                                <i class="fas fa-tv"></i>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="series-info">
                        <h3 class="series-title"><?= htmlspecialchars($displayName) ?></h3>
                        <div class="series-actions">
                            <?php if ($tmdbId): ?>
                                <button class="btn-details" onclick="verDetalles('serie', <?= $tmdbId ?>)">
                                    <i class="fas fa-info-circle"></i>
                                    Ver detalles
                                </button>
                            <?php endif; ?>
                            <button class="btn-download" onclick="descargarConPin('<?= $file ?>')">
                                <i class="fas fa-download"></i>
                                Descargar
                            </button>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>

                <?php foreach ($peliculas as $file):
                    $filename = basename($file);
                    $displayName = ucwords(str_replace('_', ' ', pathinfo($filename, PATHINFO_FILENAME)));
                    $info = getMovieInfo($displayName, $tmdbKey);
                    $tmdbId = $info['id'] ?? null;
                    $poster = $info['poster_path'] ?? null;
                ?>
                <div class="series-card" data-name="<?= $displayName ?>">
                    <div class="series-poster">
                        <?php if ($poster): ?>
                            <img src="https://image.tmdb.org/t/p/w500<?= $poster ?>" alt="<?= htmlspecialchars($displayName) ?>">
                        <?php else: ?>
                            <div class="poster-placeholder">
                                <i class="fas fa-film"></i>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="series-info">
                        <h3 class="series-title"><?= htmlspecialchars($displayName) ?></h3>
                        <div class="series-actions">
                            <?php if ($tmdbId): ?>
                                <button class="btn-details" onclick="verDetalles('pelicula', <?= $tmdbId ?>)">
                                    <i class="fas fa-info-circle"></i>
                                    Ver detalles
                                </button>
                            <?php endif; ?>
                            <button class="btn-download" onclick="descargarConPin('<?= $file ?>')">
                                <i class="fas fa-download"></i>
                                Descargar
                            </button>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>

    <!-- MODAL DETALLES -->
    <div id="modalDetalles">
        <div id="modalContent">
            <button class="close-btn" onclick="cerrarModal()">
                <i class="fas fa-times"></i>
                Cerrar
            </button>
            <div id="detallesBody"></div>
        </div>
    </div>

    <script>
        // Inicializar animaciones cuando la página carga
        document.addEventListener('DOMContentLoaded', function() {
            // Mostrar loading inicial si es necesario
            const urlParams = new URLSearchParams(window.location.search);
            const isReload = urlParams.get('reload') === '1';

            if (isReload) {
                showLoadingOverlay('Cargando contenido...', 'Preparando la biblioteca');
                setTimeout(() => {
                    hideLoadingOverlay();
                    initializePageAnimations();
                }, 1500);
            } else {
                // Animación normal de entrada
                setTimeout(() => {
                    initializePageAnimations();
                }, 100);
            }

            // Agregar efecto hover mejorado a las tarjetas
            const cards = document.querySelectorAll('.series-card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // Mejorar la experiencia del modal
            const modal = document.getElementById('modalDetalles');
            if (modal) {
                modal.addEventListener('click', function(e) {
                    if (e.target === modal) {
                        cerrarModal();
                    }
                });
            }

            // Agregar animación al success message si existe
            const successMessage = document.querySelector('.success-message');
            if (successMessage) {
                successMessage.style.opacity = '0';
                successMessage.style.transform = 'translateY(-20px)';
                setTimeout(() => {
                    successMessage.style.transition = 'all 0.5s ease';
                    successMessage.style.opacity = '1';
                    successMessage.style.transform = 'translateY(0)';
                }, 200);
            }
        });

        // Función mejorada para el modal con animaciones
        function verDetalles(tipo, id) {
            if (!id) return;

            const modal = document.getElementById('modalDetalles');
            const modalContent = document.getElementById('modalContent');

            // Mostrar modal con animación
            modal.style.display = 'flex';
            modal.style.opacity = '0';
            modalContent.style.transform = 'scale(0.8)';

            setTimeout(() => {
                modal.style.transition = 'opacity 0.3s ease';
                modalContent.style.transition = 'transform 0.3s ease';
                modal.style.opacity = '1';
                modalContent.style.transform = 'scale(1)';
            }, 10);

            // Mostrar loading en el modal
            document.getElementById('detallesBody').innerHTML = `
                <div style="text-align: center; padding: 2rem;">
                    <div class="loading-spinner" style="width: 40px; height: 40px; margin: 0 auto 1rem;"></div>
                    <p>Cargando detalles...</p>
                </div>
            `;

            fetch('web.php?ajax=detalles&type=' + tipo + '&id=' + id)
                .then(r => r.text())
                .then(html => {
                    setTimeout(() => {
                        document.getElementById('detallesBody').innerHTML = html;
                    }, 500);
                });
        }

        function cerrarModal() {
            const modal = document.getElementById('modalDetalles');
            const modalContent = document.getElementById('modalContent');

            modal.style.opacity = '0';
            modalContent.style.transform = 'scale(0.8)';

            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);
        }

        // Función para mostrar notificaciones animadas
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? 'var(--success-color)' : type === 'error' ? 'var(--error-color)' : 'var(--info-color)'};
                color: white;
                padding: 1rem 1.5rem;
                border-radius: 8px;
                box-shadow: var(--shadow-medium);
                z-index: 10001;
                transform: translateX(100%);
                transition: transform 0.3s ease;
                max-width: 300px;
            `;
            notification.innerHTML = `
                <div style="display: flex; align-items: center; gap: 0.5rem;">
                    <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'exclamation-triangle' : 'info-circle'}"></i>
                    ${message}
                </div>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);

            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }, 3000);
        }
    </script>
</body>
</html>