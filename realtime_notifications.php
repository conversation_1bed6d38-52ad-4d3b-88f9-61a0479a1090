<?php
session_start();
require_once 'config.php';

// Verificar autenticación de admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    http_response_code(401);
    exit('Unauthorized');
}

// Configurar headers para Server-Sent Events
header('Content-Type: text/event-stream');
header('Cache-Control: no-cache');
header('Connection: keep-alive');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Cache-Control');

// Evitar timeout
set_time_limit(0);
ignore_user_abort(false);

$panel = $_GET['panel'] ?? 'admin';
$lastCheck = $_GET['last_check'] ?? date('Y-m-d H:i:s');

// Función para enviar evento SSE
function sendSSE($event, $data, $id = null) {
    if ($id) {
        echo "id: $id\n";
    }
    echo "event: $event\n";
    echo "data: " . json_encode($data) . "\n\n";
    
    if (ob_get_level()) {
        ob_flush();
    }
    flush();
}

// Función para obtener estadísticas de soporte
function getSupportStats($pdo) {
    try {
        $stats = [];
        
        // Tickets abiertos
        $stmt = $pdo->query("SELECT COUNT(*) FROM support_tickets WHERE status = 'open'");
        $stats['tickets_open'] = $stmt->fetchColumn() ?: 0;
        
        // Chats activos
        $stmt = $pdo->query("SELECT COUNT(*) FROM chat_sessions WHERE status = 'active'");
        $stats['chat_active'] = $stmt->fetchColumn() ?: 0;
        
        // Canales pendientes
        $stmt = $pdo->query("SELECT COUNT(*) FROM channel_requests WHERE status = 'pending'");
        $stats['channels_pending'] = $stmt->fetchColumn() ?: 0;
        
        // Activaciones pendientes
        $stmt = $pdo->query("SELECT COUNT(*) FROM activation_codes WHERE status = 'active'");
        $stats['activations_pending'] = $stmt->fetchColumn() ?: 0;
        
        // Descargas de hoy (simulado)
        $stats['downloads_today'] = rand(5, 25);
        
        return $stats;
    } catch (Exception $e) {
        return [
            'tickets_open' => 0,
            'chat_active' => 0,
            'channels_pending' => 0,
            'activations_pending' => 0,
            'downloads_today' => 0
        ];
    }
}

// Función para obtener notificaciones nuevas
function getNewNotifications($pdo, $lastCheck) {
    try {
        $stmt = $pdo->prepare("
            SELECT * FROM admin_notifications 
            WHERE created_at > ? AND is_read = 0 
            ORDER BY created_at DESC 
            LIMIT 10
        ");
        $stmt->execute([$lastCheck]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        return [];
    }
}

// Enviar evento inicial de conexión
sendSSE('connected', [
    'panel' => $panel,
    'timestamp' => time(),
    'message' => 'Conectado al sistema de notificaciones'
]);

$lastStatsCheck = time();
$heartbeatInterval = 30; // segundos
$statsInterval = 10; // segundos

// Loop principal
while (true) {
    // Verificar si la conexión sigue activa
    if (connection_aborted()) {
        break;
    }
    
    $currentTime = time();
    
    try {
        // Enviar estadísticas cada 10 segundos
        if ($currentTime - $lastStatsCheck >= $statsInterval) {
            $stats = getSupportStats($pdo);
            sendSSE('stats_update', [
                'stats' => $stats,
                'timestamp' => $currentTime
            ]);
            $lastStatsCheck = $currentTime;
        }
        
        // Verificar nuevas notificaciones
        $notifications = getNewNotifications($pdo, $lastCheck);
        foreach ($notifications as $notification) {
            sendSSE('notification', $notification, $notification['id']);
        }
        
        // Actualizar último check
        if (!empty($notifications)) {
            $lastCheck = date('Y-m-d H:i:s');
        }
        
        // Heartbeat cada 30 segundos
        if ($currentTime % $heartbeatInterval === 0) {
            sendSSE('heartbeat', [
                'timestamp' => $currentTime,
                'panel' => $panel
            ]);
        }
        
    } catch (Exception $e) {
        sendSSE('error', [
            'message' => 'Error en el servidor: ' . $e->getMessage(),
            'timestamp' => $currentTime
        ]);
    }
    
    // Esperar 2 segundos antes del siguiente ciclo
    sleep(2);
}

// Cerrar conexión
sendSSE('close', [
    'message' => 'Conexión cerrada',
    'timestamp' => time()
]);
?>
