<?php
/**
 * RGS TOOL API REST
 * API para aplicación Android nativa
 * 
 * Endpoints disponibles:
 * GET  /api/auth/login - Autenticación de usuario
 * GET  /api/content/trending - Contenido trending
 * GET  /api/content/search - Búsqueda de contenido
 * POST /api/orders/create - Crear pedido
 * GET  /api/orders/user/{id} - Pedidos de usuario
 * GET  /api/stats - Estadísticas generales
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// Manejar preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Configuración de base de datos
$db_config = [
    'host' => 'localhost',
    'name' => 'u170528143_php',
    'user' => 'u170528143_php',
    'pass' => '&T4v!$=i'
];

// Configuración TMDB
require_once '../tmdb_config.php';

try {
    $pdo = new PDO(
        "mysql:host={$db_config['host']};dbname={$db_config['name']};charset=utf8mb4",
        $db_config['user'],
        $db_config['pass'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
} catch(PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Database connection failed',
        'message' => 'No se pudo conectar a la base de datos'
    ]);
    exit;
}

// Función para responder con JSON
function jsonResponse($data, $status_code = 200) {
    http_response_code($status_code);
    echo json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    exit;
}

// Función para obtener la IP real
function getRealIP() {
    $ip_keys = ['HTTP_CF_CONNECTING_IP', 'HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR'];
    
    foreach ($ip_keys as $key) {
        if (array_key_exists($key, $_SERVER) && !empty($_SERVER[$key])) {
            foreach (explode(',', $_SERVER[$key]) as $ip) {
                $ip = trim($ip);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                    return $ip;
                }
            }
        }
    }
    
    return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
}

// Función para obtener información de geolocalización
function getLocationInfo($ip) {
    if ($ip === '127.0.0.1' || $ip === '::1' || strpos($ip, '192.168.') === 0) {
        return ['country' => 'Local', 'city' => 'Local'];
    }

    $url = "http://ipinfo.io/{$ip}/json";
    $context = stream_context_create([
        'http' => [
            'timeout' => 3,
            'user_agent' => 'Mozilla/5.0 (compatible; RGS-TOOL-API/1.0)'
        ]
    ]);
    
    $response = @file_get_contents($url, false, $context);
    
    if ($response) {
        $data = json_decode($response, true);
        if ($data && is_array($data)) {
            return [
                'country' => $data['country'] ?? 'Unknown',
                'city' => $data['city'] ?? 'Unknown'
            ];
        }
    }
    
    return ['country' => 'Unknown', 'city' => 'Unknown'];
}

// Función para hacer peticiones a TMDB
function makeApiRequest($url) {
    $context = stream_context_create([
        'http' => [
            'timeout' => 10,
            'user_agent' => 'Mozilla/5.0 (compatible; RGS-TOOL-API/1.0)'
        ]
    ]);
    
    $response = @file_get_contents($url, false, $context);
    if ($response === false) {
        return null;
    }
    
    return json_decode($response, true);
}

// Parsear la URL para obtener el endpoint
$request_uri = $_SERVER['REQUEST_URI'];
$path = parse_url($request_uri, PHP_URL_PATH);

// Remover el script name si está presente
$script_name = $_SERVER['SCRIPT_NAME'] ?? '';
if (!empty($script_name) && strpos($path, $script_name) !== false) {
    $path = str_replace($script_name, '', $path);
}

// Manejar tanto /api/ como /series/api/
$path = str_replace('/series/api/', '', $path);
$path = str_replace('/api/', '', $path);

// Limpiar la ruta
$path = trim($path, '/');
$path_parts = empty($path) ? [] : explode('/', $path);

$endpoint = $path_parts[0] ?? '';
$action = $path_parts[1] ?? '';
$param = $path_parts[2] ?? '';

$method = $_SERVER['REQUEST_METHOD'];

// Router principal
switch ($endpoint) {
    case 'auth':
        handleAuth($action, $method, $pdo);
        break;
        
    case 'content':
        handleContent($action, $method, $pdo);
        break;
        
    case 'orders':
        handleOrders($action, $method, $pdo, $param);
        break;
        
    case 'stats':
        handleStats($method, $pdo);
        break;
        
    case 'user':
        handleUser($action, $method, $pdo, $param);
        break;

    case 'fcm':
        handleFCM($action, $method, $pdo);
        break;

    case 'info':
    case '':
        // Información de la API
        jsonResponse([
            'success' => true,
            'api' => [
                'name' => 'RGS TOOL API',
                'version' => '1.0',
                'description' => 'API REST para aplicación Android nativa',
                'timestamp' => date('Y-m-d H:i:s'),
                'endpoints' => [
                    'auth/login' => 'POST - Autenticación de usuario',
                    'content/trending' => 'GET - Contenido trending de TMDB',
                    'content/search' => 'GET - Búsqueda de contenido',
                    'orders/create' => 'POST - Crear nuevo pedido',
                    'orders/user/{id}' => 'GET - Obtener pedidos de usuario',
                    'stats' => 'GET - Estadísticas generales',
                    'user/profile/{id}' => 'GET - Perfil de usuario',
                    'fcm/token' => 'POST - Registrar token FCM'
                ]
            ]
        ]);
        break;

    default:
        jsonResponse([
            'success' => false,
            'error' => 'Invalid endpoint',
            'message' => 'Endpoint no válido',
            'debug' => [
                'requested_endpoint' => $endpoint,
                'request_uri' => $_SERVER['REQUEST_URI'],
                'parsed_path' => $path,
                'path_parts' => $path_parts
            ],
            'available_endpoints' => [
                'auth/login',
                'content/trending',
                'content/search',
                'orders/create',
                'orders/user/{id}',
                'stats',
                'user/profile/{id}',
                'fcm/token'
            ]
        ], 404);
}

// Manejar autenticación
function handleAuth($action, $method, $pdo) {
    if ($action === 'login' && $method === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!isset($input['username']) || !isset($input['password'])) {
            jsonResponse([
                'success' => false,
                'error' => 'Missing credentials',
                'message' => 'Usuario y contraseña requeridos'
            ], 400);
        }
        
        $username = $input['username'];
        $password = $input['password'];
        
        try {
            $stmt = $pdo->prepare("SELECT * FROM users WHERE username = ?");
            $stmt->execute([$username]);
            $user = $stmt->fetch();
            
            if ($user && password_verify($password, $user['password'])) {
                // Generar token simple (en producción usar JWT)
                $token = bin2hex(random_bytes(32));
                
                // Guardar token en base de datos (opcional)
                $stmt = $pdo->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
                $stmt->execute([$user['id']]);
                
                jsonResponse([
                    'success' => true,
                    'message' => 'Login exitoso',
                    'data' => [
                        'user_id' => $user['id'],
                        'username' => $user['username'],
                        'token' => $token,
                        'services' => [
                            'is_cliente_actual' => (bool)$user['is_cliente_actual'],
                            'is_mavistv' => (bool)$user['is_mavistv'],
                            'is_tvdigital' => (bool)$user['is_tvdigital'],
                            'is_limites507' => (bool)$user['is_limites507'],
                            'is_worldtv' => (bool)$user['is_worldtv'],
                            'is_infest84' => (bool)$user['is_infest84'],
                            'is_rogsmediatv' => (bool)$user['is_rogsmediatv'],
                            'is_saul' => (bool)$user['is_saul']
                        ]
                    ]
                ]);
            } else {
                jsonResponse([
                    'success' => false,
                    'error' => 'Invalid credentials',
                    'message' => 'Usuario o contraseña incorrectos'
                ], 401);
            }
            
        } catch(PDOException $e) {
            jsonResponse([
                'success' => false,
                'error' => 'Database error',
                'message' => 'Error en la base de datos'
            ], 500);
        }
    } else {
        jsonResponse([
            'success' => false,
            'error' => 'Method not allowed',
            'message' => 'Método no permitido'
        ], 405);
    }
}

// Manejar contenido
function handleContent($action, $method, $pdo) {
    if ($method !== 'GET') {
        jsonResponse([
            'success' => false,
            'error' => 'Method not allowed',
            'message' => 'Solo se permite GET'
        ], 405);
    }
    
    switch ($action) {
        case 'trending':
            $trending_movies = makeApiRequest(TMDB_BASE_URL . "/trending/movie/week?api_key=" . TMDB_API_KEY . "&language=es-MX");
            $trending_tv = makeApiRequest(TMDB_BASE_URL . "/trending/tv/week?api_key=" . TMDB_API_KEY . "&language=es-MX");
            
            jsonResponse([
                'success' => true,
                'data' => [
                    'movies' => $trending_movies['results'] ?? [],
                    'tv_shows' => $trending_tv['results'] ?? []
                ]
            ]);
            break;
            
        case 'search':
            $query = $_GET['q'] ?? '';
            if (empty($query)) {
                jsonResponse([
                    'success' => false,
                    'error' => 'Missing query',
                    'message' => 'Parámetro de búsqueda requerido'
                ], 400);
            }
            
            $encoded_query = urlencode($query);
            $search_results = makeApiRequest(TMDB_BASE_URL . "/search/multi?api_key=" . TMDB_API_KEY . "&language=es-MX&query=" . $encoded_query);
            
            jsonResponse([
                'success' => true,
                'data' => [
                    'query' => $query,
                    'results' => $search_results['results'] ?? [],
                    'total_results' => $search_results['total_results'] ?? 0
                ]
            ]);
            break;
            
        default:
            jsonResponse([
                'success' => false,
                'error' => 'Invalid action',
                'message' => 'Acción no válida'
            ], 404);
    }
}

// Manejar pedidos
function handleOrders($action, $method, $pdo, $param) {
    switch ($action) {
        case 'create':
            if ($method !== 'POST') {
                jsonResponse([
                    'success' => false,
                    'error' => 'Method not allowed',
                    'message' => 'Solo se permite POST'
                ], 405);
            }

            $input = json_decode(file_get_contents('php://input'), true);

            $required_fields = ['user_id', 'tmdb_id', 'title', 'media_type'];
            foreach ($required_fields as $field) {
                if (!isset($input[$field]) || empty($input[$field])) {
                    jsonResponse([
                        'success' => false,
                        'error' => 'Missing field',
                        'message' => "Campo requerido: $field"
                    ], 400);
                }
            }

            // Convertir username a user_id si es necesario
            $user_id = $input['user_id'];
            if (!is_numeric($user_id)) {
                // Es un username, buscar el ID
                $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ?");
                $stmt->execute([$user_id]);
                $user = $stmt->fetch();

                if (!$user) {
                    jsonResponse([
                        'success' => false,
                        'error' => 'User not found',
                        'message' => "Usuario no encontrado: $user_id"
                    ], 404);
                }

                $user_id = $user['id'];
            }

            $ip = getRealIP();
            $location = getLocationInfo($ip);

            try {
                $stmt = $pdo->prepare("INSERT INTO orders (user_id, tmdb_id, title, media_type, year, country, city, ip_address) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
                $result = $stmt->execute([
                    $user_id,
                    $input['tmdb_id'],
                    $input['title'],
                    $input['media_type'],
                    $input['year'] ?? null,
                    $location['country'],
                    $location['city'],
                    $ip
                ]);

                if ($result) {
                    $order_id = $pdo->lastInsertId();

                    jsonResponse([
                        'success' => true,
                        'message' => 'Pedido creado exitosamente',
                        'data' => [
                            'order_id' => $order_id,
                            'status' => 'Recibido'
                        ]
                    ]);
                } else {
                    jsonResponse([
                        'success' => false,
                        'error' => 'Insert failed',
                        'message' => 'No se pudo crear el pedido'
                    ], 500);
                }

            } catch(PDOException $e) {
                jsonResponse([
                    'success' => false,
                    'error' => 'Database error',
                    'message' => 'Error en la base de datos'
                ], 500);
            }
            break;

        case 'user':
            if ($method !== 'GET') {
                jsonResponse([
                    'success' => false,
                    'error' => 'Method not allowed',
                    'message' => 'Solo se permite GET'
                ], 405);
            }

            if (empty($param)) {
                jsonResponse([
                    'success' => false,
                    'error' => 'Missing user ID',
                    'message' => 'ID de usuario requerido'
                ], 400);
            }

            try {
                $stmt = $pdo->prepare("SELECT * FROM orders WHERE user_id = ? ORDER BY created_at DESC");
                $stmt->execute([$param]);
                $orders = $stmt->fetchAll();

                jsonResponse([
                    'success' => true,
                    'data' => [
                        'user_id' => $param,
                        'orders' => $orders,
                        'total_orders' => count($orders)
                    ]
                ]);

            } catch(PDOException $e) {
                jsonResponse([
                    'success' => false,
                    'error' => 'Database error',
                    'message' => 'Error en la base de datos'
                ], 500);
            }
            break;

        default:
            jsonResponse([
                'success' => false,
                'error' => 'Invalid action',
                'message' => 'Acción no válida'
            ], 404);
    }
}

// Manejar estadísticas
function handleStats($method, $pdo) {
    if ($method !== 'GET') {
        jsonResponse([
            'success' => false,
            'error' => 'Method not allowed',
            'message' => 'Solo se permite GET'
        ], 405);
    }

    try {
        // Estadísticas generales
        $total_orders = $pdo->query("SELECT COUNT(*) FROM orders")->fetchColumn();
        $total_users = $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn();

        // Pedidos por estado
        $orders_by_status = $pdo->query("
            SELECT status, COUNT(*) as count
            FROM orders
            GROUP BY status
        ")->fetchAll();

        // Pedidos recientes (últimos 7 días)
        $recent_orders = $pdo->query("
            SELECT DATE(created_at) as date, COUNT(*) as count
            FROM orders
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
            GROUP BY DATE(created_at)
            ORDER BY date DESC
        ")->fetchAll();

        // Top países
        $top_countries = $pdo->query("
            SELECT country, COUNT(*) as count
            FROM orders
            WHERE country != 'Unknown'
            GROUP BY country
            ORDER BY count DESC
            LIMIT 5
        ")->fetchAll();

        jsonResponse([
            'success' => true,
            'data' => [
                'totals' => [
                    'orders' => (int)$total_orders,
                    'users' => (int)$total_users
                ],
                'orders_by_status' => $orders_by_status,
                'recent_activity' => $recent_orders,
                'top_countries' => $top_countries
            ]
        ]);

    } catch(PDOException $e) {
        jsonResponse([
            'success' => false,
            'error' => 'Database error',
            'message' => 'Error en la base de datos'
        ], 500);
    }
}

// Manejar información de usuario
function handleUser($action, $method, $pdo, $param) {
    if ($method !== 'GET') {
        jsonResponse([
            'success' => false,
            'error' => 'Method not allowed',
            'message' => 'Solo se permite GET'
        ], 405);
    }

    if ($action === 'profile' && !empty($param)) {
        try {
            $stmt = $pdo->prepare("SELECT id, username, created_at, last_login, is_cliente_actual, is_mavistv, is_tvdigital, is_limites507, is_worldtv, is_infest84, is_rogsmediatv, is_saul FROM users WHERE id = ?");
            $stmt->execute([$param]);
            $user = $stmt->fetch();

            if ($user) {
                // Obtener estadísticas del usuario
                $stmt = $pdo->prepare("SELECT COUNT(*) FROM orders WHERE user_id = ?");
                $stmt->execute([$param]);
                $total_orders = $stmt->fetchColumn();

                $stmt = $pdo->prepare("SELECT status, COUNT(*) as count FROM orders WHERE user_id = ? GROUP BY status");
                $stmt->execute([$param]);
                $orders_by_status = $stmt->fetchAll();

                jsonResponse([
                    'success' => true,
                    'data' => [
                        'user' => $user,
                        'stats' => [
                            'total_orders' => (int)$total_orders,
                            'orders_by_status' => $orders_by_status
                        ]
                    ]
                ]);
            } else {
                jsonResponse([
                    'success' => false,
                    'error' => 'User not found',
                    'message' => 'Usuario no encontrado'
                ], 404);
            }

        } catch(PDOException $e) {
            jsonResponse([
                'success' => false,
                'error' => 'Database error',
                'message' => 'Error en la base de datos'
            ], 500);
        }
    } else {
        jsonResponse([
            'success' => false,
            'error' => 'Invalid action',
            'message' => 'Acción no válida'
        ], 404);
    }
}

// Manejar tokens FCM
function handleFCM($action, $method, $pdo) {
    if ($action === 'token' && $method === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);

        if (!isset($input['user_id']) || !isset($input['fcm_token'])) {
            jsonResponse([
                'success' => false,
                'error' => 'Missing data',
                'message' => 'user_id y fcm_token requeridos'
            ], 400);
        }

        // Convertir username a user_id si es necesario
        $user_id = $input['user_id'];
        if (!is_numeric($user_id)) {
            $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ?");
            $stmt->execute([$user_id]);
            $user = $stmt->fetch();

            if (!$user) {
                jsonResponse([
                    'success' => false,
                    'error' => 'User not found',
                    'message' => "Usuario no encontrado: $user_id"
                ], 404);
            }

            $user_id = $user['id'];
        }

        try {
            // Crear tabla FCM tokens si no existe
            $pdo->exec("CREATE TABLE IF NOT EXISTS fcm_tokens (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                fcm_token TEXT NOT NULL,
                device_type VARCHAR(32) DEFAULT 'android',
                app_version VARCHAR(16),
                is_active TINYINT(1) DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY unique_user_token (user_id, fcm_token(255))
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");

            // Insertar o actualizar token
            $stmt = $pdo->prepare("
                INSERT INTO fcm_tokens (user_id, fcm_token, device_type, app_version)
                VALUES (?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE
                    device_type = VALUES(device_type),
                    app_version = VALUES(app_version),
                    is_active = 1,
                    updated_at = CURRENT_TIMESTAMP
            ");

            $result = $stmt->execute([
                $user_id,
                $input['fcm_token'],
                $input['device_type'] ?? 'android',
                $input['app_version'] ?? '1.0.0'
            ]);

            if ($result) {
                jsonResponse([
                    'success' => true,
                    'message' => 'Token FCM registrado exitosamente',
                    'data' => [
                        'user_id' => $user_id,
                        'token_registered' => true
                    ]
                ]);
            } else {
                jsonResponse([
                    'success' => false,
                    'error' => 'Insert failed',
                    'message' => 'No se pudo registrar el token FCM'
                ], 500);
            }

        } catch(PDOException $e) {
            jsonResponse([
                'success' => false,
                'error' => 'Database error',
                'message' => 'Error en la base de datos: ' . $e->getMessage()
            ], 500);
        }
    } else {
        jsonResponse([
            'success' => false,
            'error' => 'Invalid action or method',
            'message' => 'Solo se permite POST /fcm/token'
        ], 405);
    }
}
?>
