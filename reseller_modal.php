<!-- Incluir estilos modernos de modales -->
<link rel="stylesheet" href="modal_styles.css">

<!-- Modal de Revendedor -->
<div id="resellerModal" class="modal" style="display: none;">
    <div class="modal-content reseller-modal">
        <div class="modal-body">
        <div class="modal-header">
            <h3 class="modal-title">
                <i class="fas fa-handshake"></i>
                Programa de Revendedores
            </h3>
            <button class="modal-close" onclick="closeResellerModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <form id="resellerForm" class="reseller-form">
            <!-- Paso 1: Tipo de Solicitud -->
            <div class="form-step active" id="resellerStep1">
                <div class="step-header">
                    <h4><i class="fas fa-route"></i> Tipo de Solicitud</h4>
                    <p>Selecciona si eres nuevo revendedor o quieres renovar</p>
                </div>
                
                <div class="reseller-types">
                    <div class="type-card">
                        <input type="radio" id="newReseller" name="resellerType" value="new" checked>
                        <label for="newReseller" class="type-label">
                            <div class="type-icon">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <h5>Nuevo Revendedor</h5>
                            <p>Quiero unirme al programa de revendedores</p>
                            <div class="type-features">
                                <span>✓ Kit de inicio incluido</span>
                                <span>✓ Capacitación completa</span>
                                <span>✓ Material promocional</span>
                                <span>✓ Soporte dedicado</span>
                            </div>
                        </label>
                    </div>
                    
                    <div class="type-card">
                        <input type="radio" id="renewReseller" name="resellerType" value="renewal">
                        <label for="renewReseller" class="type-label">
                            <div class="type-icon">
                                <i class="fas fa-sync-alt"></i>
                            </div>
                            <h5>Renovar Revendedor</h5>
                            <p>Ya soy revendedor y quiero renovar</p>
                            <div class="type-features">
                                <span>✓ Descuentos por renovación</span>
                                <span>✓ Beneficios adicionales</span>
                                <span>✓ Soporte prioritario</span>
                                <span>✓ Nuevas herramientas</span>
                            </div>
                        </label>
                    </div>
                </div>
                
                <div class="form-actions">
                    <button type="button" class="btn btn-primary" onclick="nextResellerStep(2)">
                        Siguiente <i class="fas fa-arrow-right"></i>
                    </button>
                </div>
            </div>
            
            <!-- Paso 2: Información Personal -->
            <div class="form-step" id="resellerStep2">
                <div class="step-header">
                    <h4><i class="fas fa-user"></i> Información Personal</h4>
                    <p>Completa tus datos personales</p>
                </div>
                
                <div class="form-grid">
                    <div class="form-group">
                        <label for="resellerFirstName">Nombre *</label>
                        <input type="text" id="resellerFirstName" name="firstName" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="resellerLastName">Apellido *</label>
                        <input type="text" id="resellerLastName" name="lastName" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="resellerEmail">Correo Electrónico *</label>
                        <input type="email" id="resellerEmail" name="email" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="resellerPhone">Teléfono *</label>
                        <input type="tel" id="resellerPhone" name="phone" required placeholder="+57 ************">
                    </div>
                    
                    <div class="form-group full-width" id="existingResellerInfo" style="display: none;">
                        <label for="resellerPlatformId">ID de Revendedor Actual *</label>
                        <input type="text" id="resellerPlatformId" name="platformId" placeholder="Tu ID actual como revendedor">
                        <small>Solo si eres revendedor existente</small>
                    </div>
                </div>
                
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="prevResellerStep(1)">
                        <i class="fas fa-arrow-left"></i> Anterior
                    </button>
                    <button type="button" class="btn btn-primary" onclick="nextResellerStep(3)">
                        Siguiente <i class="fas fa-arrow-right"></i>
                    </button>
                </div>
            </div>
            
            <!-- Paso 3: Información del Negocio -->
            <div class="form-step" id="resellerStep3">
                <div class="step-header">
                    <h4><i class="fas fa-building"></i> Información del Negocio</h4>
                    <p>Cuéntanos sobre tu experiencia y planes</p>
                </div>
                
                <div class="business-info">
                    <div class="form-group">
                        <label for="businessExperience">Experiencia en Ventas</label>
                        <select id="businessExperience" name="businessExperience">
                            <option value="">Selecciona tu experiencia</option>
                            <option value="none">Sin experiencia previa</option>
                            <option value="basic">1-2 años</option>
                            <option value="intermediate">3-5 años</option>
                            <option value="advanced">Más de 5 años</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="expectedClients">Clientes Esperados por Mes</label>
                        <select id="expectedClients" name="expectedClients">
                            <option value="">Selecciona una opción</option>
                            <option value="1-10">1-10 clientes</option>
                            <option value="11-25">11-25 clientes</option>
                            <option value="26-50">26-50 clientes</option>
                            <option value="50+">Más de 50 clientes</option>
                        </select>
                    </div>
                    
                    <div class="form-group full-width">
                        <label for="marketingChannels">¿Cómo planeas promocionar el servicio?</label>
                        <div class="checkbox-group">
                            <label class="checkbox-label">
                                <input type="checkbox" name="marketingChannels[]" value="social_media">
                                <span class="checkmark"></span>
                                Redes Sociales
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" name="marketingChannels[]" value="word_of_mouth">
                                <span class="checkmark"></span>
                                Boca a boca
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" name="marketingChannels[]" value="online_ads">
                                <span class="checkmark"></span>
                                Publicidad online
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" name="marketingChannels[]" value="local_business">
                                <span class="checkmark"></span>
                                Negocio local
                            </label>
                        </div>
                    </div>
                    
                    <div class="form-group full-width">
                        <label for="additionalInfo">Información Adicional</label>
                        <textarea id="additionalInfo" name="additionalInfo" rows="4" placeholder="Cuéntanos más sobre tus planes, experiencia previa con IPTV, o cualquier pregunta que tengas..."></textarea>
                    </div>
                </div>
                
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="prevResellerStep(2)">
                        <i class="fas fa-arrow-left"></i> Anterior
                    </button>
                    <button type="button" class="btn btn-primary" onclick="nextResellerStep(4)">
                        Siguiente <i class="fas fa-arrow-right"></i>
                    </button>
                </div>
            </div>
            
            <!-- Paso 4: Comprobante de Pago (si es renovación) -->
            <div class="form-step" id="resellerStep4">
                <div class="step-header">
                    <h4><i class="fas fa-receipt"></i> Comprobante de Pago</h4>
                    <p id="paymentStepDescription">Sube tu comprobante de pago</p>
                </div>
                
                <div class="payment-section">
                    <div id="newResellerPayment" class="payment-info">
                        <div class="info-card">
                            <h5>Información de Pago para Nuevos Revendedores</h5>
                            <p>Te contactaremos para coordinar el pago inicial y enviarte toda la información necesaria.</p>
                            <div class="benefits-list">
                                <div class="benefit-item">
                                    <i class="fas fa-check-circle"></i>
                                    <span>Kit de inicio sin costo adicional</span>
                                </div>
                                <div class="benefit-item">
                                    <i class="fas fa-check-circle"></i>
                                    <span>Capacitación personalizada incluida</span>
                                </div>
                                <div class="benefit-item">
                                    <i class="fas fa-check-circle"></i>
                                    <span>Material promocional gratuito</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div id="renewalResellerPayment" class="payment-info" style="display: none;">
                        <div class="payment-methods">
                            <h5>Métodos de Pago para Renovación:</h5>
                            <div class="payment-grid">
                                <div class="payment-method">
                                    <i class="fas fa-university"></i>
                                    <span>Transferencia Bancaria</span>
                                </div>
                                <div class="payment-method">
                                    <i class="fas fa-mobile-alt"></i>
                                    <span>Nequi / Daviplata</span>
                                </div>
                                <div class="payment-method">
                                    <i class="fas fa-credit-card"></i>
                                    <span>PSE</span>
                                </div>
                                <div class="payment-method">
                                    <i class="fab fa-paypal"></i>
                                    <span>PayPal</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="upload-section">
                            <div class="upload-area" id="resellerUploadArea">
                                <div class="upload-icon">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                </div>
                                <div class="upload-text">
                                    <h4>Arrastra tu comprobante aquí</h4>
                                    <p>o haz clic para seleccionar archivo</p>
                                    <small>Formatos: JPG, PNG, PDF (máx. 5MB)</small>
                                </div>
                                <input type="file" id="resellerPaymentProof" name="paymentProof" accept="image/*,.pdf" style="display: none;">
                            </div>
                            
                            <div id="resellerUploadPreview" class="upload-preview" style="display: none;">
                                <div class="preview-content">
                                    <div class="preview-icon">
                                        <i class="fas fa-file-image"></i>
                                    </div>
                                    <div class="preview-info">
                                        <div class="file-name"></div>
                                        <div class="file-size"></div>
                                    </div>
                                    <button type="button" class="remove-file" onclick="removeResellerUploadedFile()">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="prevResellerStep(3)">
                        <i class="fas fa-arrow-left"></i> Anterior
                    </button>
                    <button type="button" class="btn btn-primary" onclick="nextResellerStep(5)">
                        Siguiente <i class="fas fa-arrow-right"></i>
                    </button>
                </div>
            </div>
            
            <!-- Paso 5: Confirmación -->
            <div class="form-step" id="resellerStep5">
                <div class="step-header">
                    <h4><i class="fas fa-check-circle"></i> Confirmar Solicitud</h4>
                    <p>Revisa los detalles antes de enviar tu solicitud</p>
                </div>
                
                <div class="reseller-summary">
                    <div class="summary-section">
                        <h5>Resumen de Solicitud</h5>
                        <div class="summary-item">
                            <span>Tipo de solicitud:</span>
                            <span id="summaryResellerType">-</span>
                        </div>
                        <div class="summary-item">
                            <span>Nombre completo:</span>
                            <span id="summaryResellerName">-</span>
                        </div>
                        <div class="summary-item">
                            <span>Email:</span>
                            <span id="summaryResellerEmail">-</span>
                        </div>
                        <div class="summary-item">
                            <span>Experiencia:</span>
                            <span id="summaryExperience">-</span>
                        </div>
                        <div class="summary-item">
                            <span>Clientes esperados:</span>
                            <span id="summaryClients">-</span>
                        </div>
                    </div>
                    
                    <div class="terms-section">
                        <label class="checkbox-label">
                            <input type="checkbox" id="resellerTerms" name="acceptTerms" required>
                            <span class="checkmark"></span>
                            Acepto los términos y condiciones del programa de revendedores
                        </label>
                        
                        <label class="checkbox-label">
                            <input type="checkbox" id="resellerPrivacy" name="acceptPrivacy" required>
                            <span class="checkmark"></span>
                            Acepto la política de privacidad y tratamiento de datos
                        </label>
                    </div>
                </div>
                
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="prevResellerStep(4)">
                        <i class="fas fa-arrow-left"></i> Anterior
                    </button>
                    <button type="submit" class="btn btn-success" id="submitResellerBtn">
                        <i class="fas fa-paper-plane"></i> Enviar Solicitud de Revendedor
                    </button>
                </div>
            </div>
        </form>
        
        </div> <!-- Cierre de modal-body -->

        <!-- Indicador de progreso -->
        <div class="progress-indicator">
            <div class="progress-step active" data-step="1">1</div>
            <div class="progress-step" data-step="2">2</div>
            <div class="progress-step" data-step="3">3</div>
            <div class="progress-step" data-step="4">4</div>
            <div class="progress-step" data-step="5">5</div>
        </div>
    </div>
</div>

<style>
/* Estilos específicos para el modal de revendedor */
.reseller-modal {
    max-width: 900px;
}

.reseller-types {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-lg);
    margin-bottom: var(--space-xl);
}

.type-card {
    position: relative;
}

.type-card input[type="radio"] {
    display: none;
}

.type-label {
    display: block;
    padding: var(--space-xl);
    background: var(--gradient-surface);
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-xl);
    cursor: pointer;
    transition: var(--transition-normal);
    text-align: center;
    height: 100%;
}

.type-card input[type="radio"]:checked + .type-label {
    border-color: var(--primary-color);
    background: var(--gradient-elevated);
    box-shadow: var(--shadow-md), var(--shadow-glow);
    transform: translateY(-4px);
}

.type-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-lg);
    font-size: 2rem;
    color: white;
}

.type-features {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-top: var(--space-md);
}

.type-features span {
    font-size: 0.85rem;
    color: var(--text-secondary);
    text-align: left;
}

.checkbox-group {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-md);
    margin-top: var(--space-md);
}

.info-card {
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.3);
    border-radius: var(--radius-lg);
    padding: var(--space-lg);
    text-align: center;
}

.benefits-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-md);
    margin-top: var(--space-lg);
}

.benefit-item {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    color: var(--text-secondary);
}

.benefit-item i {
    color: var(--primary-color);
    font-size: 1.1rem;
}

.reseller-summary {
    background: rgba(255, 255, 255, 0.03);
    border-radius: var(--radius-lg);
    padding: var(--space-lg);
}

@media (max-width: 768px) {
    .reseller-types {
        grid-template-columns: 1fr;
    }
    
    .checkbox-group {
        grid-template-columns: 1fr;
    }
}
</style>
