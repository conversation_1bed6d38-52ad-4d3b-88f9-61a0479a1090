<?php
// Sistema de coincidencias automáticas entre pedidos y contenido M3U
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();

if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Error de conexión: " . $e->getMessage());
}

// Función para limpiar títulos para comparación
function cleanForMatch($title) {
    $clean = strtolower($title);
    $clean = preg_replace('/[^\w\s]/', ' ', $clean);
    $clean = preg_replace('/\s+/', ' ', $clean);
    $clean = trim($clean);
    return $clean;
}

// Función para calcular similitud
function calculateSimilarity($title1, $title2) {
    // Validar que los títulos no estén vacíos
    if (empty($title1) || empty($title2)) {
        return 0;
    }

    $clean1 = cleanForMatch($title1);
    $clean2 = cleanForMatch($title2);

    // Validar que después de limpiar no estén vacíos
    if (empty($clean1) || empty($clean2)) {
        return 0;
    }

    if ($clean1 === $clean2) return 100;
    if (strpos($clean1, $clean2) !== false || strpos($clean2, $clean1) !== false) return 85;

    similar_text($clean1, $clean2, $percent);
    return round($percent, 2);
}

// Función para detectar si un título es de una serie (contiene patrones de episodios)
function isSeriesEpisode($title) {
    // Patrones comunes de series (más completos)
    $patterns = [
        '/s\d+\s*e\d+/i',           // S01E01, S1E1, S01 E01, S1 E1
        '/s\d+\s*ep\d+/i',          // S01EP01, S1 EP1
        '/\d+x\d+/i',               // 1x01, 1x1
        '/season\s*\d+/i',          // Season 1, Season 01
        '/temporada\s*\d+/i',       // Temporada 1
        '/cap[ií]tulo\s*\d+/i',     // Capítulo 1
        '/ep\s*\d+/i',              // Ep 1, Ep01
        '/episode\s*\d+/i',         // Episode 1
        '/\s\d+\s*-\s*\d+/i',       // " 1 - 01", " 01-01"
        '/\[\d+x\d+\]/i',           // [1x01]
        '/\(\d+x\d+\)/i',           // (1x01)
        '/\s\d{1,2}x\d{1,2}\s/i',   // " 1x01 "
        '/\s\d{1,2}\.\d{1,2}\s/i',  // " 1.01 "
        '/\sT\d+\s*E\d+/i',         // T01E01, T1 E1
        '/\sT\d+\s*Cap\d+/i'        // T01Cap01, T1 Cap1
    ];

    foreach ($patterns as $pattern) {
        if (preg_match($pattern, $title)) {
            return true;
        }
    }

    return false;
}

// Función para extraer el nombre base de una serie
function extractSeriesBaseName($title) {
    // Remover patrones de episodios y temporadas
    $clean_title = $title;

    // Patrones a remover (más completos)
    $patterns = [
        '/s\d+\s*e\d+.*$/i',           // S01E01, S01 E01 y todo lo que sigue
        '/s\d+\s*ep\d+.*$/i',          // S01EP01, S01 EP01 y todo lo que sigue
        '/\d+x\d+.*$/i',               // 1x01 y todo lo que sigue
        '/season\s*\d+.*$/i',          // Season 1 y todo lo que sigue
        '/temporada\s*\d+.*$/i',       // Temporada 1 y todo lo que sigue
        '/cap[ií]tulo\s*\d+.*$/i',     // Capítulo 1 y todo lo que sigue
        '/ep\s*\d+.*$/i',              // Ep 1 y todo lo que sigue
        '/episode\s*\d+.*$/i',         // Episode 1 y todo lo que sigue
        '/\s*-\s*\d+.*$/i',            // - 01 y todo lo que sigue
        '/\s*\(\d+\).*$/i',            // (01) y todo lo que sigue
        '/\s*\[\d+\].*$/i',            // [01] y todo lo que sigue
        '/\s\d+\s*-\s*\d+.*$/i',       // " 1 - 01" y todo lo que sigue
        '/\[\d+x\d+\].*$/i',           // [1x01] y todo lo que sigue
        '/\(\d+x\d+\).*$/i',           // (1x01) y todo lo que sigue
        '/\s\d{1,2}x\d{1,2}.*$/i',     // " 1x01" y todo lo que sigue
        '/\s\d{1,2}\.\d{1,2}.*$/i',    // " 1.01" y todo lo que sigue
        '/\sT\d+\s*E\d+.*$/i',         // T01E01, T1 E1 y todo lo que sigue
        '/\sT\d+\s*Cap\d+.*$/i',       // T01Cap01, T1 Cap1 y todo lo que sigue
        '/\s*\d{4}\s*$/i',             // Año al final (ej: " 2023")
        '/\s*\(\d{4}\)\s*$/i'          // Año entre paréntesis al final
    ];

    foreach ($patterns as $pattern) {
        $clean_title = preg_replace($pattern, '', $clean_title);
    }

    // Limpiar espacios extra y caracteres especiales al final
    $clean_title = trim($clean_title);
    $clean_title = rtrim($clean_title, ' -_.');

    return trim($clean_title);
}

// Función para buscar coincidencias automáticamente con agrupación de series
function findOrderMatches($pdo) {
    // Obtener pedidos recientes sin coincidencias procesadas
    $stmt = $pdo->query("
        SELECT DISTINCT o.id, o.title, o.status, o.created_at, u.username
        FROM orders o
        LEFT JOIN users u ON o.user_id = u.id
        WHERE o.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        AND o.title IS NOT NULL
        AND o.title != ''
        AND TRIM(o.title) != ''
        AND LENGTH(TRIM(o.title)) > 2
        ORDER BY o.created_at DESC
        LIMIT 50
    ");
    $orders = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Obtener todo el contenido M3U activo
    $stmt = $pdo->query("
        SELECT c.*, l.name as list_name
        FROM m3u_content c
        LEFT JOIN m3u_lists l ON c.list_id = l.id
        WHERE l.is_active = 1
        AND c.title IS NOT NULL
        AND c.title != ''
        AND TRIM(c.title) != ''
        AND LENGTH(TRIM(c.title)) > 2
    ");
    $content = $stmt->fetchAll(PDO::FETCH_ASSOC);

    $matches = [];

    foreach ($orders as $order) {
        // Validar que el pedido tenga título válido
        if (empty($order['title']) || strlen(trim($order['title'])) < 3) {
            continue;
        }

        $order_matches = [];
        $series_groups = []; // Para agrupar series

        foreach ($content as $item) {
            // Validar que el contenido tenga título válido
            if (empty($item['title']) || strlen(trim($item['title'])) < 3) {
                continue;
            }

            $similarity = calculateSimilarity($order['title'], $item['title']);

            if ($similarity >= 70) {
                // Verificar si es una serie
                if (isSeriesEpisode($item['title'])) {
                    // Es una serie, agrupar por nombre base
                    $series_base = extractSeriesBaseName($item['title']);
                    $series_key = strtolower(trim($series_base));

                    if (!isset($series_groups[$series_key])) {
                        $series_groups[$series_key] = [
                            'series_name' => $series_base,
                            'episodes' => [],
                            'best_similarity' => $similarity,
                            'match_type' => $similarity >= 95 ? 'exact' : ($similarity >= 80 ? 'high' : 'partial'),
                            'lists' => [],
                            'total_episodes' => 0
                        ];
                    }

                    // Agregar episodio al grupo
                    $series_groups[$series_key]['episodes'][] = $item;
                    $series_groups[$series_key]['total_episodes']++;

                    // Actualizar mejor similitud
                    if ($similarity > $series_groups[$series_key]['best_similarity']) {
                        $series_groups[$series_key]['best_similarity'] = $similarity;
                        $series_groups[$series_key]['match_type'] = $similarity >= 95 ? 'exact' : ($similarity >= 80 ? 'high' : 'partial');
                    }

                    // Agregar lista si no existe
                    if (!in_array($item['list_name'], $series_groups[$series_key]['lists'])) {
                        $series_groups[$series_key]['lists'][] = $item['list_name'];
                    }

                } else {
                    // Es una película individual
                    $order_matches[] = [
                        'content' => $item,
                        'similarity' => $similarity,
                        'match_type' => $similarity >= 95 ? 'exact' : ($similarity >= 80 ? 'high' : 'partial'),
                        'is_series' => false
                    ];
                }
            }
        }

        // Convertir grupos de series a matches
        foreach ($series_groups as $series_group) {
            $order_matches[] = [
                'series_name' => $series_group['series_name'],
                'episodes' => $series_group['episodes'],
                'similarity' => $series_group['best_similarity'],
                'match_type' => $series_group['match_type'],
                'lists' => $series_group['lists'],
                'total_episodes' => $series_group['total_episodes'],
                'is_series' => true
            ];
        }

        // Ordenar por similitud
        usort($order_matches, function($a, $b) {
            return $b['similarity'] <=> $a['similarity'];
        });

        if (!empty($order_matches)) {
            $matches[] = [
                'order' => $order,
                'matches' => array_slice($order_matches, 0, 15) // Máximo 15 coincidencias por pedido
            ];
        }
    }

    return $matches;
}

// Función para generar archivo M3U de serie completa
function generateSeriesM3U($pdo, $series_title, $list_id = null) {
    // Buscar episodios que contengan el nombre de la serie
    $where_clause = "(c.title LIKE ? OR c.clean_title LIKE ?) AND l.is_active = 1";
    $search_pattern = "%$series_title%";
    $params = [$search_pattern, $search_pattern];

    if ($list_id) {
        $where_clause .= " AND c.list_id = ?";
        $params[] = $list_id;
    }

    $stmt = $pdo->prepare("
        SELECT c.*, l.name as list_name
        FROM m3u_content c
        LEFT JOIN m3u_lists l ON c.list_id = l.id
        WHERE $where_clause
        ORDER BY c.title
    ");
    $stmt->execute($params);
    $all_results = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Filtrar solo episodios de series (que tengan patrones de episodios)
    $episodes = [];
    foreach ($all_results as $item) {
        if (isSeriesEpisode($item['title'])) {
            $base_name = extractSeriesBaseName($item['title']);
            // Verificar que el nombre base coincida con el título buscado
            if (stripos($base_name, $series_title) !== false || stripos($series_title, $base_name) !== false) {
                $episodes[] = $item;
            }
        }
    }

    if (empty($episodes)) {
        return null;
    }

    // Generar contenido M3U
    $m3u_content = "#EXTM3U\n";
    $m3u_content .= "#EXTINF:-1,Serie Completa: " . $series_title . "\n";
    $m3u_content .= "# Generado por RGS TOOL - " . date('Y-m-d H:i:s') . "\n";
    $m3u_content .= "# Total de episodios: " . count($episodes) . "\n";
    if (!empty($episodes)) {
        $m3u_content .= "# Lista origen: " . $episodes[0]['list_name'] . "\n";
    }
    $m3u_content .= "\n";

    foreach ($episodes as $episode) {
        $m3u_content .= "#EXTINF:-1," . $episode['title'] . "\n";
        $m3u_content .= $episode['url'] . "\n\n";
    }

    return [
        'content' => $m3u_content,
        'filename' => preg_replace('/[^a-zA-Z0-9_-]/', '_', $series_title) . '_completa.m3u',
        'episodes_count' => count($episodes)
    ];
}

// Procesar descarga de serie completa
if (isset($_GET['download_series'])) {
    $series_title = $_GET['series_title'];
    $list_id = $_GET['list_id'] ?? null;
    
    $series_data = generateSeriesM3U($pdo, $series_title, $list_id);
    
    if ($series_data) {
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . $series_data['filename'] . '"');
        header('Content-Length: ' . strlen($series_data['content']));
        echo $series_data['content'];
        exit;
    } else {
        $error_message = "No se encontraron episodios para la serie: $series_title";
    }
}

// Obtener coincidencias
$matches = findOrderMatches($pdo);

// Estadísticas
$total_orders_with_matches = count($matches);
$total_matches = array_sum(array_map(fn($m) => count($m['matches']), $matches));
$exact_matches = 0;
$high_matches = 0;
$series_matches = 0;
$movie_matches = 0;

foreach ($matches as $match) {
    foreach ($match['matches'] as $m) {
        if ($m['match_type'] === 'exact') $exact_matches++;
        elseif ($m['match_type'] === 'high') $high_matches++;

        if (isset($m['is_series']) && $m['is_series']) {
            $series_matches++;
        } else {
            $movie_matches++;
        }
    }
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 Coincidencias Encontradas - RogsMediaTV</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            /* Paleta de colores profesional para soporte técnico */
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #1e293b;
            --dark-bg: #0f172a;
            --darker-bg: #020617;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --accent-color: #10b981;
            --accent-dark: #059669;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --success-color: #10b981;
            --info-color: #06b6d4;
            --danger-color: #ef4444;
            --border-color: #334155;
            --border-light: #475569;
            --gradient-primary: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            --gradient-secondary: linear-gradient(135deg, #10b981 0%, #059669 100%);
            --gradient-dark: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
            --gradient-bg: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            --shadow-light: 0 2px 8px rgba(0,0,0,0.3);
            --shadow-medium: 0 4px 16px rgba(0,0,0,0.4);
            --shadow-heavy: 0 8px 32px rgba(0,0,0,0.6);
            --border-radius: 12px;
            --border-radius-lg: 16px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--primary-color);
            color: var(--text-primary);
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            background: linear-gradient(135deg, var(--accent-color), #28a745);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--secondary-color);
            padding: 1.5rem;
            border-radius: 12px;
            border: 1px solid var(--border-color);
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--accent-color);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .matches-container {
            display: grid;
            gap: 1.5rem;
        }

        .order-card {
            background: var(--secondary-color);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 1.5rem;
        }

        .order-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .order-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--accent-color);
            flex: 1;
        }

        .order-meta {
            display: flex;
            gap: 1rem;
            color: var(--text-secondary);
            font-size: 0.9rem;
            flex-wrap: wrap;
        }

        .matches-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .match-item {
            background: var(--primary-color);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
        }

        .match-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 0.5rem;
        }

        .match-title {
            font-weight: 600;
            color: var(--text-primary);
            font-size: 0.9rem;
            flex: 1;
            margin-right: 1rem;
        }

        .similarity-badge {
            padding: 0.2rem 0.6rem;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 600;
            white-space: nowrap;
        }

        .similarity-exact {
            background: rgba(40, 167, 69, 0.2);
            color: var(--success-color);
        }

        .similarity-high {
            background: rgba(255, 193, 7, 0.2);
            color: var(--warning-color);
        }

        .similarity-partial {
            background: rgba(23, 162, 184, 0.2);
            color: var(--info-color);
        }

        .match-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
            font-size: 0.8rem;
            color: var(--text-secondary);
        }

        .match-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .btn {
            padding: 0.4rem 0.8rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8rem;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.3rem;
            transition: all 0.3s ease;
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-info {
            background: var(--info-color);
            color: white;
        }

        .btn-warning {
            background: var(--warning-color);
            color: var(--primary-color);
        }

        .btn:hover {
            transform: translateY(-1px);
            opacity: 0.9;
        }

        .back-link {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--accent-color);
            text-decoration: none;
            margin-bottom: 2rem;
            font-weight: 500;
        }

        .back-link:hover {
            color: #3bc73c;
        }

        .no-matches {
            text-align: center;
            padding: 3rem;
            color: var(--text-secondary);
        }

        .no-matches i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .error {
            background: rgba(220, 53, 69, 0.2);
            color: var(--danger-color);
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }

        /* Modal para mostrar episodios */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.8);
        }

        .modal-content {
            background-color: var(--secondary-color);
            margin: 5% auto;
            padding: 2rem;
            border-radius: var(--border-radius-lg);
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
            border: 1px solid var(--border-color);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .modal-title {
            font-size: 1.5rem;
            color: var(--accent-color);
            font-weight: 600;
        }

        .close {
            color: var(--text-secondary);
            font-size: 2rem;
            font-weight: bold;
            cursor: pointer;
            transition: var(--transition);
        }

        .close:hover {
            color: var(--text-primary);
        }

        .episodes-list {
            display: grid;
            gap: 0.5rem;
        }

        .episode-item {
            background: var(--dark-bg);
            padding: 1rem;
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .episode-title {
            flex: 1;
            font-size: 0.9rem;
            color: var(--text-primary);
        }

        .episode-actions {
            display: flex;
            gap: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="admin.php" class="back-link">
            <i class="fas fa-arrow-left"></i>
            Volver al Panel Admin
        </a>

        <div class="header">
            <h1><i class="fas fa-bullseye"></i> Coincidencias Encontradas</h1>
            <p>Pedidos de clientes que coinciden con contenido disponible en tus listas M3U</p>
        </div>

        <?php if (isset($error_message)): ?>
        <div class="error"><?php echo htmlspecialchars($error_message); ?></div>
        <?php endif; ?>

        <!-- Estadísticas -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?php echo $total_orders_with_matches; ?></div>
                <div class="stat-label">Pedidos con Coincidencias</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $total_matches; ?></div>
                <div class="stat-label">Total de Coincidencias</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $series_matches; ?></div>
                <div class="stat-label">📺 Series Encontradas</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $movie_matches; ?></div>
                <div class="stat-label">🎬 Películas Encontradas</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $exact_matches; ?></div>
                <div class="stat-label">Coincidencias Exactas</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $high_matches; ?></div>
                <div class="stat-label">Coincidencias Altas</div>
            </div>
        </div>

        <!-- Coincidencias -->
        <?php if (empty($matches)): ?>
        <div class="no-matches">
            <i class="fas fa-search-minus"></i>
            <h3>No se encontraron coincidencias</h3>
            <p>No hay pedidos recientes que coincidan con el contenido disponible en tus listas M3U</p>
            <p style="margin-top: 1rem;">
                <a href="m3u_search.php" class="btn btn-info">
                    <i class="fas fa-search"></i>
                    Buscar Manualmente
                </a>
            </p>
        </div>
        <?php else: ?>
        <div class="matches-container">
            <?php foreach ($matches as $match): ?>
            <div class="order-card">
                <div class="order-header">
                    <div class="order-title"><?php echo htmlspecialchars($match['order']['title']); ?></div>
                    <div class="order-meta">
                        <span><i class="fas fa-user"></i> <?php echo htmlspecialchars($match['order']['username'] ?? 'Usuario'); ?></span>
                        <span><i class="fas fa-calendar"></i> <?php echo date('d/m/Y', strtotime($match['order']['created_at'])); ?></span>
                        <span><i class="fas fa-tag"></i> <?php echo ucfirst($match['order']['status']); ?></span>
                        <span><i class="fas fa-bullseye"></i> <?php echo count($match['matches']); ?> coincidencia(s)</span>
                    </div>
                </div>

                <div class="matches-grid">
                    <?php foreach ($match['matches'] as $m): ?>
                    <div class="match-item">
                        <?php if (isset($m['is_series']) && $m['is_series']): ?>
                            <!-- Serie agrupada -->
                            <div class="match-header">
                                <div class="match-title">
                                    <i class="fas fa-tv" style="color: var(--accent-color); margin-right: 0.5rem;"></i>
                                    <?php echo htmlspecialchars($m['series_name']); ?>
                                    <span style="font-size: 0.8rem; color: var(--text-secondary); margin-left: 0.5rem;">
                                        (<?php echo $m['total_episodes']; ?> episodios)
                                    </span>
                                </div>
                                <div class="similarity-badge similarity-<?php echo $m['match_type']; ?>">
                                    <?php echo $m['similarity']; ?>%
                                </div>
                            </div>

                            <div class="match-meta">
                                <span><i class="fas fa-list"></i> <?php echo implode(', ', $m['lists']); ?></span>
                                <span><i class="fas fa-tv"></i> 📺 Serie TV</span>
                                <span><i class="fas fa-film"></i> <?php echo $m['total_episodes']; ?> episodios disponibles</span>
                            </div>

                            <div class="match-actions">
                                <a href="?download_series=1&series_title=<?php echo urlencode($m['series_name']); ?>"
                                   class="btn btn-warning">
                                    <i class="fas fa-download"></i>
                                    Descargar Serie Completa
                                </a>

                                <a href="m3u_search.php?q=<?php echo urlencode($m['series_name']); ?>" class="btn btn-info">
                                    <i class="fas fa-search"></i>
                                    Ver Episodios
                                </a>

                                <button class="btn btn-success" onclick="showEpisodes('<?php echo addslashes($m['series_name']); ?>', <?php echo htmlspecialchars(json_encode($m['episodes'])); ?>)">
                                    <i class="fas fa-list"></i>
                                    Ver Lista (<?php echo $m['total_episodes']; ?>)
                                </button>
                            </div>
                        <?php else: ?>
                            <!-- Película individual -->
                            <div class="match-header">
                                <div class="match-title">
                                    <i class="fas fa-film" style="color: var(--warning-color); margin-right: 0.5rem;"></i>
                                    <?php echo htmlspecialchars($m['content']['title']); ?>
                                </div>
                                <div class="similarity-badge similarity-<?php echo $m['match_type']; ?>">
                                    <?php echo $m['similarity']; ?>%
                                </div>
                            </div>

                            <div class="match-meta">
                                <span><i class="fas fa-list"></i> <?php echo htmlspecialchars($m['content']['list_name']); ?></span>
                                <span><i class="fas fa-tag"></i> 🎬 Película</span>
                                <?php if ($m['content']['year']): ?>
                                <span><i class="fas fa-calendar"></i> <?php echo $m['content']['year']; ?></span>
                                <?php endif; ?>
                            </div>

                            <div class="match-actions">
                                <button class="btn btn-success" onclick="copyUrl('<?php echo addslashes($m['content']['url']); ?>')">
                                    <i class="fas fa-copy"></i>
                                    Copiar URL
                                </button>

                                <a href="m3u_search.php?q=<?php echo urlencode($m['content']['title']); ?>" class="btn btn-info">
                                    <i class="fas fa-search"></i>
                                    Ver Más
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
    </div>

    <!-- Modal para mostrar episodios -->
    <div id="episodesModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title" id="modalTitle">Episodios de la Serie</h2>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            <div id="episodesList" class="episodes-list">
                <!-- Los episodios se cargarán aquí -->
            </div>
        </div>
    </div>

    <script>
        function copyUrl(url) {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(url).then(function() {
                    showMessage('✅ URL copiada al portapapeles');
                }).catch(function(err) {
                    fallbackCopy(url);
                });
            } else {
                fallbackCopy(url);
            }
        }

        function fallbackCopy(text) {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            document.body.appendChild(textArea);
            textArea.select();
            
            try {
                document.execCommand('copy');
                showMessage('✅ URL copiada al portapapeles');
            } catch (err) {
                showMessage('❌ Error al copiar URL');
            }
            
            document.body.removeChild(textArea);
        }

        function showMessage(message) {
            const div = document.createElement('div');
            div.style.cssText = 'position:fixed;top:20px;right:20px;background:#10b981;color:white;padding:1rem;border-radius:8px;z-index:1000;';
            div.textContent = message;
            document.body.appendChild(div);

            setTimeout(() => {
                document.body.removeChild(div);
            }, 3000);
        }

        function showEpisodes(seriesName, episodes) {
            const modal = document.getElementById('episodesModal');
            const modalTitle = document.getElementById('modalTitle');
            const episodesList = document.getElementById('episodesList');

            modalTitle.textContent = `Episodios de: ${seriesName} (${episodes.length} episodios)`;

            // Limpiar lista anterior
            episodesList.innerHTML = '';

            // Agregar episodios
            episodes.forEach((episode, index) => {
                const episodeDiv = document.createElement('div');
                episodeDiv.className = 'episode-item';

                episodeDiv.innerHTML = `
                    <div class="episode-title">
                        <strong>${index + 1}.</strong> ${episode.title}
                        <div style="font-size: 0.8rem; color: var(--text-secondary); margin-top: 0.2rem;">
                            Lista: ${episode.list_name}
                        </div>
                    </div>
                    <div class="episode-actions">
                        <button class="btn btn-success" onclick="copyUrl('${episode.url.replace(/'/g, "\\'")}')">
                            <i class="fas fa-copy"></i>
                            Copiar
                        </button>
                    </div>
                `;

                episodesList.appendChild(episodeDiv);
            });

            modal.style.display = 'block';
        }

        function closeModal() {
            document.getElementById('episodesModal').style.display = 'none';
        }

        // Cerrar modal al hacer clic fuera de él
        window.onclick = function(event) {
            const modal = document.getElementById('episodesModal');
            if (event.target === modal) {
                closeModal();
            }
        }
    </script>
</body>
</html>
