# 🛡️ Sistema de Seguridad Geográfica

## Descripción
Se ha implementado un sistema de seguridad geográfica que restringe el acceso a los archivos de login únicamente a usuarios ubicados en países de Latinoamérica y Estados Unidos.

## Archivos Modificados

### 1. `security_config.php` (NUEVO)
- Configuración principal del sistema de seguridad
- Lista de países permitidos
- Funciones de geolocalización IP
- Sistema de logging de accesos

### 2. `login.php` (MODIFICADO)
- Agregada verificación geográfica al inicio
- Sin cambios en la interfaz de usuario
- Bloqueo automático de países no autorizados

### 3. `admin_login.php` (MODIFICADO)
- Agregada verificación geográfica al inicio
- Información adicional sobre restricciones geográficas
- Sin cambios en la funcionalidad de login

### 4. `test_geolocation.php` (NUEVO - TEMPORAL)
- Archivo de prueba para verificar la funcionalidad
- **ELIMINAR DESPUÉS DE VERIFICAR QUE TODO FUNCIONA**

## Países Permitidos

### Estados Unidos
- US - Estados Unidos

### Latinoamérica
- AR - Argentina
- BO - Bolivia
- BR - Brasil
- CL - Chile
- CO - Colombia
- CR - Costa Rica
- CU - Cuba
- DO - República Dominicana
- EC - Ecuador
- SV - El Salvador
- GT - Guatemala
- HN - Honduras
- MX - México
- NI - Nicaragua
- PA - Panamá
- PY - Paraguay
- PE - Perú
- PR - Puerto Rico
- UY - Uruguay
- VE - Venezuela

## Características de Seguridad

### ✅ Funcionalidades Implementadas
- **Detección automática de IP**: Obtiene la IP real del usuario incluso detrás de proxies/CDN
- **Geolocalización en tiempo real**: Utiliza API gratuita (ip-api.com) para determinar el país
- **Bloqueo automático**: Usuarios de países no autorizados son bloqueados inmediatamente
- **Logging completo**: Todos los accesos (permitidos y bloqueados) son registrados
- **Fallback seguro**: En caso de error de API, permite acceso por defecto
- **Soporte para desarrollo local**: IPs locales (127.0.0.1, 192.168.x.x) son permitidas

### 🔒 Medidas de Seguridad
- **Sin cambios en la interfaz**: La experiencia del usuario autorizado no cambia
- **Bloqueo transparente**: Usuarios no autorizados ven una página de error clara
- **Logging de seguridad**: Archivos `access_log.txt` y `blocked_access.txt`
- **Resistente a proxies**: Detecta IPs reales detrás de CDN/proxies

## Archivos de Log

### `access_log.txt`
Registra todos los intentos de acceso con:
- Fecha y hora
- IP del usuario
- País detectado
- Estado (GRANTED/DENIED)

### `blocked_access.txt`
Registra específicamente los accesos bloqueados con información detallada.

## Configuración

### Modificar Países Permitidos
Editar el array `$allowed_countries` en `security_config.php`:

```php
$allowed_countries = [
    'US', // Estados Unidos
    'MX', // México
    // Agregar más códigos de país según necesidad
];
```

### Cambiar API de Geolocalización
Si se desea usar otra API, modificar la función `getCountryFromIP()` en `security_config.php`.

## Pruebas

### 1. Verificar Funcionalidad
1. Acceder a `test_geolocation.php`
2. Verificar que muestre la IP y país correctos
3. Confirmar que el estado sea "ACCESO PERMITIDO"

### 2. Probar Bloqueo
Para probar el bloqueo, temporalmente:
1. Remover tu país del array `$allowed_countries`
2. Acceder a `login.php` o `admin_login.php`
3. Verificar que aparezca la página de "Acceso Restringido"
4. Restaurar la configuración original

### 3. Verificar Logs
- Revisar `access_log.txt` para ver registros de acceso
- Revisar `blocked_access.txt` para ver intentos bloqueados

## Mantenimiento

### Archivos a Eliminar Después de Pruebas
- `test_geolocation.php` (archivo temporal de pruebas)

### Monitoreo Recomendado
- Revisar logs periódicamente
- Monitorear intentos de acceso desde países no autorizados
- Verificar que la API de geolocalización funcione correctamente

## Notas Importantes

### ⚠️ Consideraciones
- **Desarrollo Local**: Las IPs locales (127.0.0.1, etc.) son tratadas como US para permitir desarrollo
- **API Gratuita**: ip-api.com tiene límites de uso (1000 requests/mes gratis)
- **Fallback**: Si la API falla, se permite acceso por defecto para evitar bloqueos accidentales
- **Precisión**: La geolocalización IP no es 100% precisa, especialmente con VPNs/proxies

### 🔧 Troubleshooting
- Si usuarios legítimos son bloqueados, verificar la configuración de países
- Si la API falla frecuentemente, considerar usar MaxMind GeoLite2 (local)
- Para mayor seguridad, cambiar el fallback a bloquear en lugar de permitir

## Contacto
Para modificaciones o problemas con el sistema de seguridad, contactar al administrador del sistema.
