<!-- 🎨 COMPONENTES MODERNOS PARA INDEX.PHP -->
<!-- Mejoras de layout y componentes user-friendly -->

<!-- ===== HEADER MEJORADO ===== -->
<div class="modern-header-improvements">
    <!-- Breadcrumb Navigation -->
    <nav class="breadcrumb-nav" aria-label="Navegación">
        <ol class="breadcrumb">
            <li><a href="#"><i class="fas fa-home"></i> Inicio</a></li>
            <li class="current">Catálogo</li>
        </ol>
    </nav>
    
    <!-- Quick Actions Bar -->
    <div class="quick-actions">
        <button class="quick-action" data-tooltip="Búsqueda rápida (Ctrl+K)">
            <i class="fas fa-search"></i>
        </button>
        <button class="quick-action" data-tooltip="Favoritos">
            <i class="fas fa-heart"></i>
        </button>
        <button class="quick-action" data-tooltip="Historial">
            <i class="fas fa-history"></i>
        </button>
        <button class="quick-action" data-tooltip="Configuración">
            <i class="fas fa-cog"></i>
        </button>
    </div>
</div>

<!-- ===== FILTROS AVANZADOS ===== -->
<div class="advanced-filters">
    <div class="filter-container">
        <h3 class="filter-title">
            <i class="fas fa-filter"></i>
            Filtros
        </h3>
        
        <div class="filter-group">
            <label class="filter-label">Tipo de contenido</label>
            <div class="filter-options">
                <button class="filter-btn active" data-filter="all">
                    <i class="fas fa-th"></i> Todos
                </button>
                <button class="filter-btn" data-filter="movie">
                    <i class="fas fa-film"></i> Películas
                </button>
                <button class="filter-btn" data-filter="tv">
                    <i class="fas fa-tv"></i> Series
                </button>
            </div>
        </div>
        
        <div class="filter-group">
            <label class="filter-label">Género</label>
            <select class="filter-select">
                <option value="">Todos los géneros</option>
                <option value="action">Acción</option>
                <option value="comedy">Comedia</option>
                <option value="drama">Drama</option>
                <option value="horror">Terror</option>
                <option value="sci-fi">Ciencia Ficción</option>
            </select>
        </div>
        
        <div class="filter-group">
            <label class="filter-label">Año</label>
            <div class="year-range">
                <input type="range" class="year-slider" min="1990" max="2024" value="2020">
                <span class="year-display">2020+</span>
            </div>
        </div>
        
        <div class="filter-group">
            <label class="filter-label">Calificación</label>
            <div class="rating-filter">
                <div class="stars">
                    <i class="fas fa-star" data-rating="1"></i>
                    <i class="fas fa-star" data-rating="2"></i>
                    <i class="fas fa-star" data-rating="3"></i>
                    <i class="fas fa-star" data-rating="4"></i>
                    <i class="fas fa-star" data-rating="5"></i>
                </div>
                <span class="rating-text">4+ estrellas</span>
            </div>
        </div>
    </div>
</div>

<!-- ===== VISTA DE GRID MEJORADA ===== -->
<div class="view-controls">
    <div class="view-options">
        <button class="view-btn active" data-view="grid">
            <i class="fas fa-th"></i>
            <span>Grid</span>
        </button>
        <button class="view-btn" data-view="list">
            <i class="fas fa-list"></i>
            <span>Lista</span>
        </button>
        <button class="view-btn" data-view="compact">
            <i class="fas fa-th-large"></i>
            <span>Compacto</span>
        </button>
    </div>
    
    <div class="sort-options">
        <select class="sort-select">
            <option value="popularity">Más populares</option>
            <option value="rating">Mejor calificados</option>
            <option value="release_date">Más recientes</option>
            <option value="title">Alfabético</option>
        </select>
    </div>
    
    <div class="grid-size-control">
        <label for="grid-size">Tamaño:</label>
        <input type="range" id="grid-size" min="150" max="300" value="200" class="grid-slider">
    </div>
</div>

<!-- ===== TARJETA DE PELÍCULA MEJORADA ===== -->
<div class="modern-movie-card">
    <div class="card-container">
        <div class="card-image">
            <img src="placeholder.jpg" alt="Título de película" loading="lazy">
            <div class="card-overlay">
                <div class="card-actions">
                    <button class="action-btn primary">
                        <i class="fas fa-plus"></i>
                        Solicitar
                    </button>
                    <button class="action-btn secondary">
                        <i class="fas fa-info-circle"></i>
                        Detalles
                    </button>
                </div>
                <div class="card-info">
                    <div class="rating">
                        <i class="fas fa-star"></i>
                        <span>8.5</span>
                    </div>
                    <div class="year">2024</div>
                </div>
            </div>
            <div class="card-badges">
                <span class="badge new">Nuevo</span>
                <span class="badge hd">HD</span>
            </div>
        </div>
        <div class="card-content">
            <h3 class="card-title">Título de la Película</h3>
            <p class="card-description">Breve descripción de la película...</p>
            <div class="card-meta">
                <span class="genre">Acción</span>
                <span class="duration">2h 15m</span>
            </div>
        </div>
    </div>
</div>

<!-- ===== MODAL DE DETALLES MEJORADO ===== -->
<div class="modern-modal" id="detailsModal">
    <div class="modal-backdrop"></div>
    <div class="modal-container">
        <div class="modal-header">
            <h2 class="modal-title">Detalles de la Película</h2>
            <button class="modal-close">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <div class="details-grid">
                <div class="details-poster">
                    <img src="placeholder.jpg" alt="Poster">
                </div>
                <div class="details-info">
                    <h3 class="details-title">Título de la Película</h3>
                    <div class="details-meta">
                        <span class="meta-item">
                            <i class="fas fa-calendar"></i>
                            2024
                        </span>
                        <span class="meta-item">
                            <i class="fas fa-clock"></i>
                            2h 15m
                        </span>
                        <span class="meta-item">
                            <i class="fas fa-star"></i>
                            8.5/10
                        </span>
                    </div>
                    <p class="details-overview">
                        Descripción completa de la película con todos los detalles relevantes...
                    </p>
                    <div class="details-genres">
                        <span class="genre-tag">Acción</span>
                        <span class="genre-tag">Aventura</span>
                        <span class="genre-tag">Ciencia Ficción</span>
                    </div>
                    <div class="details-actions">
                        <button class="btn-primary">
                            <i class="fas fa-plus"></i>
                            Solicitar Película
                        </button>
                        <button class="btn-secondary">
                            <i class="fas fa-heart"></i>
                            Agregar a Favoritos
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- ===== SISTEMA DE NOTIFICACIONES MEJORADO ===== -->
<div class="modern-notifications">
    <div class="notification-container">
        <div class="notification-header">
            <h3>
                <i class="fas fa-bell"></i>
                Notificaciones
                <span class="notification-count">3</span>
            </h3>
            <div class="notification-actions">
                <button class="btn-text" onclick="markAllAsRead()">
                    Marcar todas como leídas
                </button>
                <button class="btn-text" onclick="clearNotifications()">
                    Limpiar
                </button>
            </div>
        </div>
        <div class="notification-list">
            <div class="notification-item unread">
                <div class="notification-icon success">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="notification-content">
                    <h4>Película disponible</h4>
                    <p>Tu solicitud de "Avengers: Endgame" está lista</p>
                    <span class="notification-time">Hace 5 minutos</span>
                </div>
                <button class="notification-dismiss">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <div class="notification-item">
                <div class="notification-icon info">
                    <i class="fas fa-info-circle"></i>
                </div>
                <div class="notification-content">
                    <h4>Nuevo contenido agregado</h4>
                    <p>Se han agregado 15 películas nuevas al catálogo</p>
                    <span class="notification-time">Hace 2 horas</span>
                </div>
                <button class="notification-dismiss">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- ===== BARRA DE PROGRESO DE CARGA ===== -->
<div class="loading-progress">
    <div class="progress-bar">
        <div class="progress-fill"></div>
    </div>
    <div class="loading-text">Cargando contenido...</div>
</div>

<!-- ===== FLOATING ACTION BUTTON ===== -->
<div class="fab-container">
    <button class="fab main-fab">
        <i class="fas fa-plus"></i>
    </button>
    <div class="fab-menu">
        <button class="fab mini-fab" data-tooltip="Solicitar película">
            <i class="fas fa-film"></i>
        </button>
        <button class="fab mini-fab" data-tooltip="Solicitar serie">
            <i class="fas fa-tv"></i>
        </button>
        <button class="fab mini-fab" data-tooltip="Soporte técnico">
            <i class="fas fa-headset"></i>
        </button>
    </div>
</div>

<!-- ===== SKELETON LOADERS ===== -->
<div class="skeleton-grid">
    <div class="skeleton-card">
        <div class="skeleton-image"></div>
        <div class="skeleton-content">
            <div class="skeleton-title"></div>
            <div class="skeleton-text"></div>
            <div class="skeleton-text short"></div>
        </div>
    </div>
</div>

<!-- ===== EMPTY STATES ===== -->
<div class="empty-state">
    <div class="empty-icon">
        <i class="fas fa-search"></i>
    </div>
    <h3 class="empty-title">No se encontraron resultados</h3>
    <p class="empty-description">
        Intenta con otros términos de búsqueda o explora nuestro catálogo completo
    </p>
    <button class="btn-primary">
        <i class="fas fa-refresh"></i>
        Explorar catálogo
    </button>
</div>

<!-- ===== BACK TO TOP BUTTON ===== -->
<button class="back-to-top" id="backToTop">
    <i class="fas fa-chevron-up"></i>
</button>

<!-- ===== ESTILOS CSS PARA LOS COMPONENTES ===== -->
<style>
/* Breadcrumb Navigation */
.breadcrumb-nav {
    padding: 1rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.breadcrumb {
    display: flex;
    list-style: none;
    gap: 0.5rem;
    align-items: center;
}

.breadcrumb li:not(:last-child)::after {
    content: '/';
    margin-left: 0.5rem;
    color: var(--text-secondary);
}

.breadcrumb a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition-fast);
}

.breadcrumb a:hover {
    color: var(--primary-color);
}

.breadcrumb .current {
    color: var(--text-primary);
    font-weight: 500;
}

/* Quick Actions */
.quick-actions {
    display: flex;
    gap: 0.5rem;
}

.quick-action {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-lg);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    cursor: pointer;
    transition: var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
}

.quick-action:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

/* Advanced Filters */
.advanced-filters {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: var(--radius-xl);
    padding: var(--space-lg);
    margin-bottom: var(--space-xl);
}

.filter-group {
    margin-bottom: var(--space-lg);
}

.filter-label {
    display: block;
    margin-bottom: var(--space-sm);
    font-weight: 500;
    color: var(--text-primary);
}

.filter-options {
    display: flex;
    gap: var(--space-sm);
    flex-wrap: wrap;
}

.filter-btn {
    padding: var(--space-sm) var(--space-md);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition-normal);
    display: flex;
    align-items: center;
    gap: var(--space-xs);
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--gradient-primary);
    border-color: var(--primary-color);
    color: white;
}

/* Modern Movie Card */
.modern-movie-card {
    position: relative;
    border-radius: var(--radius-xl);
    overflow: hidden;
    transition: var(--transition-normal);
}

.card-badges {
    position: absolute;
    top: var(--space-sm);
    left: var(--space-sm);
    z-index: 2;
    display: flex;
    gap: var(--space-xs);
}

.badge {
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-full);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.badge.new {
    background: var(--gradient-secondary);
    color: white;
}

.badge.hd {
    background: var(--gradient-primary);
    color: white;
}

/* FAB (Floating Action Button) */
.fab-container {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    z-index: 1000;
}

.fab {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    border: none;
    cursor: pointer;
    box-shadow: var(--shadow-lg);
    transition: var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
}

.main-fab {
    background: var(--gradient-primary);
    color: white;
    font-size: 1.5rem;
}

.main-fab:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-xl);
}

.mini-fab {
    width: 40px;
    height: 40px;
    background: var(--secondary-color);
    color: var(--text-primary);
    margin-bottom: var(--space-sm);
    opacity: 0;
    transform: scale(0);
    transition: var(--transition-normal);
}

.fab-container:hover .mini-fab {
    opacity: 1;
    transform: scale(1);
}

/* Back to Top */
.back-to-top {
    position: fixed;
    bottom: 2rem;
    left: 2rem;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: var(--gradient-primary);
    border: none;
    color: white;
    cursor: pointer;
    opacity: 0;
    transform: translateY(100px);
    transition: var(--transition-normal);
    z-index: 1000;
}

.back-to-top.show {
    opacity: 1;
    transform: translateY(0);
}

.back-to-top:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

/* Skeleton Loaders */
.skeleton-card {
    background: rgba(255, 255, 255, 0.03);
    border-radius: var(--radius-xl);
    overflow: hidden;
    animation: pulse 1.5s ease-in-out infinite alternate;
}

.skeleton-image {
    aspect-ratio: 2/3;
    background: rgba(255, 255, 255, 0.1);
}

.skeleton-content {
    padding: var(--space-md);
}

.skeleton-title,
.skeleton-text {
    height: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-sm);
    margin-bottom: var(--space-sm);
}

.skeleton-text.short {
    width: 60%;
}

/* Empty States */
.empty-state {
    text-align: center;
    padding: var(--space-2xl);
    color: var(--text-secondary);
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: var(--space-lg);
    opacity: 0.5;
}

.empty-title {
    font-size: 1.5rem;
    margin-bottom: var(--space-md);
    color: var(--text-primary);
}

.empty-description {
    margin-bottom: var(--space-xl);
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}
</style>
