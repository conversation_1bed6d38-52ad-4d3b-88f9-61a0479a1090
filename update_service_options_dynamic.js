// Script para cargar servicios dinámicamente en service_options.php

document.addEventListener('DOMContentLoaded', function() {
    // Solo cargar servicios dinámicos si la tabla existe y funciona
    // Si no, mantener el contenido estático
    console.log('🎯 service_options.php cargado - verificando si usar contenido dinámico...');

    // Esperar un poco para que el DOM se estabilice
    setTimeout(() => {
        loadDynamicServices();
    }, 1000);
});

async function loadDynamicServices() {
    try {
        console.log('🔄 Intentando cargar configuraciones de servicios...');

        // Verificar si existe el contenedor
        const servicesGrid = document.querySelector('.services-grid');
        if (!servicesGrid) {
            console.log('⚠️ No se encontró .services-grid, manteniendo contenido estático');
            return;
        }

        const response = await fetch('api_service_config.php?action=get_public');

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        console.log('📡 Respuesta de API:', data);

        if (data.success && data.configs && data.configs.length > 0) {
            console.log('✅ Configuraciones cargadas, renderizando servicios dinámicos...');
            renderServices(data.configs);
        } else {
            console.warn('⚠️ No se encontraron configuraciones válidas, manteniendo contenido estático');
            // No hacer nada, mantener el contenido estático
        }
    } catch (error) {
        console.error('❌ Error cargando configuraciones:', error);
        console.log('🔄 Manteniendo servicios estáticos...');
        // No hacer nada, mantener el contenido estático
    }
}

function renderServices(configs) {
    const servicesGrid = document.getElementById('servicesGrid');
    if (!servicesGrid) return;
    
    // Limpiar contenido actual
    servicesGrid.innerHTML = '';
    
    configs.forEach(config => {
        const serviceCard = createServiceCard(config);
        servicesGrid.appendChild(serviceCard);
    });
}

function createServiceCard(config) {
    const card = document.createElement('div');
    card.className = 'service-card';
    
    // Determinar icono según el tipo
    const icons = {
        'trial': 'fas fa-gift',
        'purchase': 'fas fa-star',
        'renewal': 'fas fa-refresh',
        'reseller': 'fas fa-users'
    };
    
    const icon = icons[config.service_type] || 'fas fa-star';
    
    // Crear badge de promoción si existe
    const promoBadge = config.promotion_badge ? 
        `<div class="promo-badge">${config.promotion_badge}</div>` : '';
    
    // Crear precio con descuento si existe
    const priceHTML = config.original_price && config.discount_percentage > 0 ? 
        `<div class="service-price">
            <span class="price-original">$${config.original_price}</span>
            <span class="price-current">${config.price}</span>
            <span class="price-discount">${config.discount_percentage}% OFF</span>
        </div>` :
        `<div class="service-price">
            <span class="price-current">${config.price}</span>
        </div>`;
    
    // Crear lista de características
    const featuresHTML = config.features.map(feature => 
        `<li><i class="fas fa-check"></i> ${feature}</li>`
    ).join('');
    
    // Determinar acción del botón
    const buttonAction = getButtonAction(config.service_type);
    
    card.innerHTML = `
        ${promoBadge}
        <div class="service-header">
            <div class="service-icon">
                <i class="${icon}"></i>
            </div>
            <h2 class="service-title">${config.title}</h2>
            <p class="service-description">${config.subtitle || config.description}</p>
        </div>
        
        ${priceHTML}
        
        <div class="service-features">
            <ul class="features-list">
                ${featuresHTML}
            </ul>
        </div>
        
        ${config.promotion_text ? 
            `<div class="service-promotion">
                <i class="fas fa-fire"></i>
                <span>${config.promotion_text}</span>
            </div>` : ''
        }
        
        <div class="service-action">
            <button onclick="${buttonAction}" class="btn btn-primary">
                <i class="${getButtonIcon(config.service_type)}"></i>
                ${config.button_text}
            </button>
        </div>
    `;
    
    return card;
}

function getButtonAction(serviceType) {
    switch (serviceType) {
        case 'trial':
        case 'purchase':
            return 'openServiceRequestModal()';
        case 'renewal':
            return 'openRenewalModal()';
        case 'reseller':
            return 'openResellerModal()';
        default:
            return 'openServiceRequestModal()';
    }
}

function getButtonIcon(serviceType) {
    switch (serviceType) {
        case 'trial':
            return 'fas fa-gift';
        case 'purchase':
            return 'fas fa-rocket';
        case 'renewal':
            return 'fas fa-refresh';
        case 'reseller':
            return 'fas fa-handshake';
        default:
            return 'fas fa-star';
    }
}

function createResellerCard() {
    const card = document.createElement('div');
    card.className = 'service-card';
    
    card.innerHTML = `
        <div class="service-header">
            <div class="service-icon">
                <i class="fas fa-users"></i>
            </div>
            <h2 class="service-title">Ser Revendedor</h2>
            <p class="service-description">
                Únete a nuestro programa de revendedores y genera ingresos adicionales
            </p>
        </div>
        
        <div class="service-price">
            <span class="price-current">Comisiones hasta 40%</span>
        </div>
        
        <div class="service-features">
            <ul class="features-list">
                <li><i class="fas fa-check"></i> Panel de administración exclusivo</li>
                <li><i class="fas fa-check"></i> Comisiones competitivas</li>
                <li><i class="fas fa-check"></i> Soporte técnico prioritario</li>
                <li><i class="fas fa-check"></i> Material promocional incluido</li>
                <li><i class="fas fa-check"></i> Capacitación completa</li>
            </ul>
        </div>
        
        <div class="service-promotion">
            <i class="fas fa-crown"></i>
            <span>¡Oportunidad de negocio exclusiva!</span>
        </div>
        
        <div class="service-action">
            <button onclick="openResellerModal()" class="btn btn-primary">
                <i class="fas fa-handshake"></i>
                Solicitar Información
            </button>
        </div>
    `;
    
    return card;
}

function showFallbackServices() {
    const servicesGrid = document.getElementById('servicesGrid');
    if (!servicesGrid) return;
    
    servicesGrid.innerHTML = `
        <div class="service-card">
            <div class="service-header">
                <div class="service-icon">
                    <i class="fas fa-star"></i>
                </div>
                <h2 class="service-title">Nuevo Servicio</h2>
                <p class="service-description">
                    Solicita tu primer servicio IPTV con prueba gratuita o activación directa
                </p>
            </div>
            <div class="service-features">
                <ul class="features-list">
                    <li><i class="fas fa-check"></i> Prueba gratuita de 24-48 horas</li>
                    <li><i class="fas fa-check"></i> Más de 5000 canales HD/4K</li>
                    <li><i class="fas fa-check"></i> Compatible con todos los dispositivos</li>
                    <li><i class="fas fa-check"></i> Soporte técnico incluido</li>
                </ul>
            </div>
            <div class="service-action">
                <button onclick="openServiceRequestModal()" class="btn btn-primary">
                    <i class="fas fa-rocket"></i>
                    Solicitar Nuevo Servicio
                </button>
            </div>
        </div>
        
        <div class="service-card">
            <div class="promo-badge">¡Promoción!</div>
            <div class="service-header">
                <div class="service-icon">
                    <i class="fas fa-refresh"></i>
                </div>
                <h2 class="service-title">Renovar Servicio</h2>
                <p class="service-description">
                    Renueva tu servicio existente con descuentos especiales
                </p>
            </div>
            <div class="service-features">
                <ul class="features-list">
                    <li><i class="fas fa-check"></i> Descuentos por renovación</li>
                    <li><i class="fas fa-check"></i> Planes flexibles</li>
                    <li><i class="fas fa-check"></i> Sin interrupciones</li>
                    <li><i class="fas fa-check"></i> Soporte prioritario</li>
                </ul>
            </div>
            <div class="service-action">
                <button onclick="openRenewalModal()" class="btn btn-primary">
                    <i class="fas fa-refresh"></i>
                    Renovar Mi Servicio
                </button>
            </div>
        </div>
        
        <div class="service-card">
            <div class="service-header">
                <div class="service-icon">
                    <i class="fas fa-users"></i>
                </div>
                <h2 class="service-title">Ser Revendedor</h2>
                <p class="service-description">
                    Únete a nuestro programa de revendedores y obtén beneficios exclusivos
                </p>
            </div>
            <div class="service-features">
                <ul class="features-list">
                    <li><i class="fas fa-check"></i> Precios especiales para revendedores</li>
                    <li><i class="fas fa-check"></i> Panel de gestión de clientes</li>
                    <li><i class="fas fa-check"></i> Soporte técnico prioritario</li>
                    <li><i class="fas fa-check"></i> Comisiones atractivas</li>
                    <li><i class="fas fa-check"></i> Material promocional incluido</li>
                </ul>
            </div>
            <div class="service-action">
                <button onclick="openResellerModal()" class="btn btn-secondary">
                    <i class="fas fa-handshake"></i>
                    Solicitar Ser Revendedor
                </button>
            </div>
        </div>
    `;
}

// Agregar estilos CSS adicionales para los nuevos elementos
const additionalStyles = `
    <style>
        .service-price {
            text-align: center;
            margin: 1.5rem 0;
            padding: 1rem;
            background: rgba(16, 185, 129, 0.1);
            border-radius: 12px;
            border: 1px solid rgba(16, 185, 129, 0.2);
        }
        
        .price-current {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--primary-color);
            display: block;
        }
        
        .price-original {
            font-size: 1rem;
            color: var(--text-secondary);
            text-decoration: line-through;
            display: block;
            margin-bottom: 0.5rem;
        }
        
        .price-discount {
            font-size: 0.9rem;
            color: #dc2626;
            font-weight: 600;
            background: rgba(220, 38, 38, 0.1);
            padding: 0.25rem 0.5rem;
            border-radius: 6px;
            display: inline-block;
            margin-top: 0.5rem;
        }
        
        .service-promotion {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem;
            background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(217, 119, 6, 0.1) 100%);
            border: 1px solid rgba(245, 158, 11, 0.2);
            border-radius: 8px;
            margin: 1rem 0;
            color: #f59e0b;
            font-weight: 600;
            text-align: center;
            justify-content: center;
        }
        
        .features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .features-list li {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.5rem 0;
            color: var(--text-secondary);
        }
        
        .features-list li i {
            color: var(--primary-color);
            font-size: 0.9rem;
        }
        
        .loading-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 3rem;
            color: var(--text-secondary);
        }
        
        .loading-placeholder i {
            font-size: 2rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>
`;

// Agregar estilos al head
document.head.insertAdjacentHTML('beforeend', additionalStyles);
