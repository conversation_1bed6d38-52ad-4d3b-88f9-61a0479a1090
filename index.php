<?php
// Habilitar reporte de errores para debugging (comentado después de solucionar)
// error_reporting(E_ALL);
// ini_set('display_errors', 1);
ini_set('log_errors', 1);

session_start();

// Log de debugging para verificar sesión
error_log("INDEX.PHP - Session data: " . print_r($_SESSION, true));

// Verificar si el usuario está logueado
if (!isset($_SESSION['user_id'])) {
    error_log("INDEX.PHP - Usuario no logueado, redirigiendo a login.php");
    header('Location: login.php');
    exit;
}

error_log("INDEX.PHP - Usuario logueado: ID=" . $_SESSION['user_id'] . ", Username=" . ($_SESSION['username'] ?? 'N/A'));

$user_id = $_SESSION['user_id'];
$username = $_SESSION['username'] ?? '';

// Configuración MySQL
$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

$api_key = "201066b4b17391d478e55247f43eed64";
$base_url = "https://api.themoviedb.org/3";
$image_base_url = "https://image.tmdb.org/t/p/w300";

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Crear tabla de pedidos si no existe
    $pdo->exec("CREATE TABLE IF NOT EXISTS orders (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        tmdb_id INT NOT NULL,
        title VARCHAR(255) NOT NULL,
        media_type VARCHAR(32) NOT NULL,
        year VARCHAR(8),
        country VARCHAR(64),
        city VARCHAR(64),
        ip_address VARCHAR(45),
        status VARCHAR(32) DEFAULT 'Recibido',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        notif_seen TINYINT(1) DEFAULT 0,
        status_notif_seen TINYINT(1) DEFAULT 0
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");

    // Verificar y agregar columnas si no existen
    $columns_to_check = ['updated_at', 'status_notif_seen'];
    foreach ($columns_to_check as $column) {
        $stmt = $pdo->prepare("SHOW COLUMNS FROM orders LIKE ?");
        $stmt->execute([$column]);
        if ($stmt->rowCount() == 0) {
            if ($column === 'updated_at') {
                $pdo->exec("ALTER TABLE orders ADD COLUMN $column DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP");
            } else {
                $pdo->exec("ALTER TABLE orders ADD COLUMN $column TINYINT(1) DEFAULT 0");
            }
        }
    }

} catch(PDOException $e) {
    error_log("Error de base de datos: " . $e->getMessage());
    die("Error de conexión a la base de datos.");
}

// Función para obtener información de geolocalización
function getLocationInfo($ip) {
    // Si es IP local, devolver valores por defecto
    if ($ip === '127.0.0.1' || $ip === '::1' || strpos($ip, '192.168.') === 0 || strpos($ip, '10.') === 0) {
        return ['country' => 'Local', 'city' => 'Local'];
    }

    $url = "http://ipinfo.io/{$ip}/json";
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 3); // Reducir timeout
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 2);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; RGS-TOOL/1.0)');

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    if ($error) {
        error_log("Error en geolocalización: $error");
        return ['country' => 'Unknown', 'city' => 'Unknown'];
    }

    if ($httpCode == 200 && $response) {
        $data = json_decode($response, true);
        if ($data && is_array($data)) {
            return [
                'country' => isset($data['country']) ? $data['country'] : 'Unknown',
                'city' => isset($data['city']) ? $data['city'] : 'Unknown'
            ];
        }
    }

    return ['country' => 'Unknown', 'city' => 'Unknown'];
}

// Función para obtener la IP real del visitante
function getRealIP() {
    $ip_keys = ['HTTP_CF_CONNECTING_IP', 'HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_FORWARDED', 'HTTP_X_CLUSTER_CLIENT_IP', 'HTTP_FORWARDED_FOR', 'HTTP_FORWARDED', 'REMOTE_ADDR'];
    
    foreach ($ip_keys as $key) {
        if (array_key_exists($key, $_SERVER) === true) {
            foreach (explode(',', $_SERVER[$key]) as $ip) {
                $ip = trim($ip);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                    return $ip;
                }
            }
        }
    }
    
    return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
}

// Registrar la visita actual
function recordVisit($pdo) {
    $ip = getRealIP();
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';
    $page_url = $_SERVER['REQUEST_URI'] ?? '/';
    
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM visits WHERE ip_address = ? AND visit_date > (NOW() - INTERVAL 30 MINUTE)");
    $stmt->execute([$ip]);
    $recent_visits = $stmt->fetchColumn();
    
    if ($recent_visits == 0) {
        $location = getLocationInfo($ip);
        
        $stmt = $pdo->prepare("INSERT INTO visits (ip_address, country, city, user_agent, page_url) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute([$ip, $location['country'], $location['city'], $user_agent, $page_url]);
        
        updateStats($pdo);
    }
}

// Función para actualizar estadísticas
function updateStats($pdo) {
    $total_visits = $pdo->query("SELECT COUNT(*) FROM visits")->fetchColumn();
    $unique_visitors = $pdo->query("SELECT COUNT(DISTINCT ip_address) FROM visits")->fetchColumn();
    
    $stmt = $pdo->prepare("INSERT INTO stats (id, total_visits, unique_visitors, last_updated) VALUES (1, ?, ?, NOW())
        ON DUPLICATE KEY UPDATE total_visits = VALUES(total_visits), unique_visitors = VALUES(unique_visitors), last_updated = NOW()");
    $stmt->execute([$total_visits, $unique_visitors]);
}

// Función para obtener estadísticas
function getStats($pdo) {
    try {
        $stmt = $pdo->query("SELECT * FROM stats WHERE id = 1");
        $general_stats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$general_stats) {
            $general_stats = ['total_visits' => 0, 'unique_visitors' => 0];
        }
        
        $countries = $pdo->query("
            SELECT country, COUNT(*) as count 
            FROM visits 
            WHERE country != 'Unknown' 
            GROUP BY country 
            ORDER BY count DESC 
            LIMIT 5
        ")->fetchAll(PDO::FETCH_ASSOC);
        
        $daily_visits = $pdo->query("
            SELECT DATE(visit_date) as date, COUNT(*) as count 
            FROM visits 
            WHERE visit_date >= (NOW() - INTERVAL 7 DAY)
            GROUP BY DATE(visit_date) 
            ORDER BY date DESC
        ")->fetchAll(PDO::FETCH_ASSOC);
        
        return [
            'general' => $general_stats,
            'countries' => $countries,
            'daily' => $daily_visits
        ];
    } catch(PDOException $e) {
        return [
            'general' => ['total_visits' => 0, 'unique_visitors' => 0],
            'countries' => [],
            'daily' => []
        ];
    }
}

// Registrar la visita actual
if (isset($pdo)) {
    recordVisit($pdo);
    $stats = getStats($pdo);
}

// Función para hacer peticiones a la API
function makeApiRequest($url) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    $response = curl_exec($ch);
    curl_close($ch);
    return json_decode($response, true);
}

// Obtener contenido popular y próximos estrenos
$trending_movies = makeApiRequest($base_url . "/trending/movie/week?api_key=" . $api_key . "&language=es-MX");
$trending_tv = makeApiRequest($base_url . "/trending/tv/week?api_key=" . $api_key . "&language=es-MX");
$upcoming_movies = makeApiRequest($base_url . "/movie/upcoming?api_key=" . $api_key . "&language=es-MX&region=MX");

// Búsqueda si se envió el formulario
$search_results = [];
if (isset($_GET['query']) && !empty($_GET['query'])) {
    $query = urlencode($_GET['query']);
    $search_results = makeApiRequest($base_url . "/search/multi?api_key=" . $api_key . "&language=es-MX&query=" . $query);
}

// Registrar pedido si se envía el formulario
if (isset($_POST['pedido_tmdb_id'])) {
    error_log("Pedido recibido - POST data: " . print_r($_POST, true));

    $tmdb_id = $_POST['pedido_tmdb_id'];
    $title = $_POST['pedido_title'];
    $media_type = $_POST['pedido_media_type'];
    $year = $_POST['pedido_year'];

    error_log("Datos del pedido - TMDB ID: $tmdb_id, Title: $title, Type: $media_type, Year: $year, User ID: $user_id");

    // Obtener información de geolocalización
    $ip = getRealIP();
    $location = getLocationInfo($ip);

    error_log("Información de ubicación - IP: $ip, Country: " . $location['country'] . ", City: " . $location['city']);

    try {
        // Insertar pedido en la base de datos
        $stmt = $pdo->prepare("INSERT INTO orders (user_id, tmdb_id, title, media_type, year, country, city, ip_address) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
        $result = $stmt->execute([
            $user_id,
            $tmdb_id,
            $title,
            $media_type,
            $year,
            $location['country'],
            $location['city'],
            $ip
        ]);

        if ($result) {
            $order_id = $pdo->lastInsertId();
            error_log("Pedido insertado exitosamente con ID: $order_id");

            $_SESSION['pedido_exitoso'] = [
                'tmdb_id' => $tmdb_id,
                'title' => $title,
                'media_type' => $media_type,
                'year' => $year
            ];

            // Mantener query de búsqueda si existe
            $redirect_url = 'index.php?pedido=ok';
            if (!empty($_GET['query'])) {
                $redirect_url .= '&query=' . urlencode($_GET['query']);
            } elseif (!empty($_POST['query'])) {
                $redirect_url .= '&query=' . urlencode($_POST['query']);
            }
            header('Location: ' . $redirect_url);
            exit;
        } else {
            error_log("Error: No se pudo insertar el pedido");
            $error_pedido = "Error al procesar el pedido. Inténtalo de nuevo.";
        }

    } catch(PDOException $e) {
        error_log("Error al insertar pedido: " . $e->getMessage());
        error_log("SQL State: " . $e->getCode());
        $error_pedido = "Error al procesar el pedido. Inténtalo de nuevo.";
    }
}

// Mostrar modal de éxito si hay un pedido exitoso
$pedido_exitoso = false;
if (isset($_GET['pedido']) && $_GET['pedido'] === 'ok' && isset($_SESSION['pedido_exitoso'])) {
    $pedido_data = $_SESSION['pedido_exitoso'];
    $pedido_exitoso = true;
    unset($_SESSION['pedido_exitoso']);
    // Mantener query de búsqueda si existe
    $search_query = $_GET['query'] ?? '';
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🛠️ RGS TOOL - Contenido IPTV</title>
    <meta name="description" content="Descubre y solicita películas y series para tu servicio IPTV. Explora contenido popular y próximos estrenos.">
    
    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🛠️</text></svg>">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        :root {
            /* Paleta de colores negro y rojo */
            --primary-color: #dc2626;
            --primary-dark: #b91c1c;
            --primary-light: #f87171;
            --secondary-color: #1f1f1f;
            --dark-bg: #000000;
            --darker-bg: #0a0a0a;
            --surface: #2a2a2a;
            --surface-light: #3a3a3a;

            /* Colores de texto */
            --text-primary: #ffffff;
            --text-secondary: #d1d5db;
            --text-muted: #9ca3af;

            /* Colores de acento en rojo */
            --accent-color: #dc2626;
            --accent-secondary: #ef4444;
            --warning-color: #f59e0b;
            --danger-color: #dc2626;
            --success-color: #dc2626;
            --support-color: #dc2626;

            /* Gradientes negro y rojo */
            --gradient-primary: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            --gradient-secondary: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            --gradient-surface: linear-gradient(135deg, #1f1f1f 0%, #2a2a2a 100%);
            --gradient-hero: linear-gradient(135deg, #000000 0%, #1f1f1f 50%, #2a2a2a 100%);

            /* Sombras simples sin 3D */
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
            --shadow-md: 0 2px 4px 0 rgba(0, 0, 0, 0.4);
            --shadow-lg: 0 4px 8px 0 rgba(0, 0, 0, 0.5);
            --shadow-xl: 0 8px 16px 0 rgba(0, 0, 0, 0.6);
            --shadow-glow: 0 0 10px rgba(220, 38, 38, 0.3);

            /* Espaciado consistente */
            --space-xs: 0.25rem;
            --space-sm: 0.5rem;
            --space-md: 1rem;
            --space-lg: 1.5rem;
            --space-xl: 2rem;
            --space-2xl: 3rem;

            /* Border radius simple */
            --radius-sm: 4px;
            --radius-md: 6px;
            --radius-lg: 8px;
            --radius-xl: 10px;
            --radius-full: 50px;

            /* Transiciones simples */
            --transition-fast: 150ms ease;
            --transition-normal: 300ms ease;
            --transition-slow: 500ms ease;

            /* Compatibilidad con variables anteriores */
            --border-color: #3a3a3a;
            --border-light: #4a4a4a;
            --border-radius: var(--radius-lg);
            --border-radius-lg: var(--radius-xl);
            --transition: var(--transition-normal);
            --shadow-light: var(--shadow-md);
            --shadow-medium: var(--shadow-lg);
            --shadow-heavy: var(--shadow-xl);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gradient-hero);
            background-attachment: fixed;
            color: var(--text-primary);
            line-height: 1.6;
            overflow-x: hidden;
            min-height: 100vh;
            font-feature-settings: "kern" 1, "liga" 1;
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Header simple */
        .header {
            background: var(--dark-bg);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 1000;
            border-bottom: 1px solid var(--border-color);
            box-shadow: var(--shadow-md);
        }

        .header-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1.5rem;
            font-weight: 800;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-decoration: none;
            letter-spacing: -0.025em;
        }

        .logo i {
            font-size: 2rem;
        }

        /* Búsqueda Moderna */
        .search-container {
            flex: 1;
            max-width: 500px;
            position: relative;
        }

        .search-form {
            display: flex;
            background: var(--surface);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            overflow: hidden;
            transition: var(--transition-normal);
        }

        .search-form:focus-within {
            background: var(--surface-light);
            border-color: var(--primary-color);
            box-shadow: var(--shadow-glow);
        }

        .search-input {
            flex: 1;
            padding: 0.75rem 1.5rem;
            background: transparent;
            border: none;
            color: var(--text-primary);
            font-size: 1rem;
            outline: none;
        }

        .search-input::placeholder {
            color: var(--text-secondary);
        }

        .search-btn {
            padding: 0.75rem 1.5rem;
            background: var(--gradient-primary);
            border: none;
            color: white;
            cursor: pointer;
            transition: var(--transition);
            font-weight: 500;
            border-radius: 0 50px 50px 0;
        }

        .search-btn:hover {
            background: var(--primary-dark);
            box-shadow: var(--shadow-light);
        }

        /* User Menu */
        .user-menu {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            flex-wrap: wrap;
            justify-content: flex-end;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: white;
            font-weight: 500;
            padding: 0.6rem 1rem;
            background: var(--gradient-primary);
            border-radius: var(--radius-lg);
            border: 1px solid var(--primary-color);
            box-shadow: var(--shadow-sm);
            white-space: nowrap;
        }

        .nav-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 0.6rem 1rem;
            background: var(--surface);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            color: var(--text-primary);
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition-normal);
            box-shadow: var(--shadow-sm);
            position: relative;
            overflow: hidden;
            white-space: nowrap;
            min-width: fit-content;
        }

        .nav-btn:hover {
            background: var(--surface-light);
            border-color: var(--primary-color);
            box-shadow: var(--shadow-md);
            color: var(--primary-color);
        }

        .nav-btn.admin-btn {
            background: var(--surface);
            border-color: var(--warning-color);
            color: var(--warning-color);
            box-shadow: var(--shadow-sm);
        }

        .nav-btn.admin-btn:hover {
            background: var(--warning-color);
            border-color: var(--warning-color);
            color: white;
            box-shadow: var(--shadow-md);
        }

        .nav-btn.logout-btn {
            background: var(--surface);
            border-color: var(--danger-color);
            color: var(--danger-color);
            box-shadow: var(--shadow-sm);
        }

        .nav-btn.logout-btn:hover {
            background: var(--danger-color);
            border-color: var(--danger-color);
            color: white;
            box-shadow: var(--shadow-md);
        }

        .nav-btn.support-btn {
            background: var(--gradient-primary);
            border-color: var(--primary-color);
            color: white;
            box-shadow: var(--shadow-sm);
        }

        .nav-btn.support-btn:hover {
            background: var(--primary-dark);
            border-color: var(--primary-color);
            color: white;
            box-shadow: var(--shadow-md);
        }

        /* Botón de notificaciones especial */
        .nav-btn.notification-btn {
            position: relative;
        }

        .notification-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background: var(--danger-color);
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            animation: pulse 2s infinite;
            box-shadow: 0 0 10px rgba(239, 68, 68, 0.5);
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        /* Botón de solicitar servicio especial */
        .nav-btn.service-request-btn {
            background: var(--gradient-primary);
            border-color: var(--primary-color);
            color: white;
            box-shadow: var(--shadow-sm), var(--shadow-glow);
            position: relative;
            overflow: hidden;
        }

        .nav-btn.service-request-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: var(--transition-slow);
        }

        .nav-btn.service-request-btn:hover {
            background: var(--gradient-primary);
            transform: var(--transform-hover) scale(1.05);
            box-shadow: var(--shadow-elevated), var(--shadow-glow);
            filter: var(--brightness-hover);
        }

        .nav-btn.service-request-btn:hover::before {
            left: 100%;
        }

        /* Notification Styles */
        .notification-btn {
            position: relative;
        }

        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: var(--gradient-secondary);
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            animation: pulse 2s ease-in-out infinite;
            border: 2px solid var(--dark-bg);
            box-shadow: var(--shadow-md);
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .notification-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.8);
            z-index: 2000;
            display: none;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(8px) saturate(180%);
        }

        .notification-modal.show {
            display: flex;
            animation: fadeIn 0.3s ease;
        }

        .notification-content {
            background: var(--gradient-surface);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: var(--radius-xl);
            padding: 2rem;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: var(--shadow-xl);
        }

        .notification-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .notification-item {
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid rgba(255, 255, 255, 0.08);
            border-radius: var(--radius-lg);
            border-left: 3px solid var(--primary-color);
            padding: 1rem;
            margin-bottom: 1rem;
            transition: var(--transition-normal);
        }

        .notification-item:hover {
            background: rgba(255, 255, 255, 0.06);
            transform: translateX(4px);
            box-shadow: var(--shadow-md);
        }

        .notification-item.new {
            border-left-color: var(--accent-color);
            background: rgba(70, 211, 105, 0.1);
        }

        .notification-item[data-type="ticket"] {
            border-left-color: var(--support-color);
        }

        .notification-item[data-type="ticket"].new {
            border-left-color: var(--support-color);
            background: rgba(233, 30, 99, 0.1);
        }

        .notification-title {
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }

        .notification-action {
            margin-top: 0.5rem;
            padding-top: 0.5rem;
            border-top: 1px solid rgba(255,255,255,0.1);
        }

        .notification-action a {
            display: inline-flex;
            align-items: center;
            gap: 0.3rem;
            transition: var(--transition);
        }

        .notification-action a:hover {
            opacity: 0.8;
        }

        .notification-status {
            display: inline-flex;
            align-items: center;
            gap: 0.3rem;
            padding: 0.2rem 0.6rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .status-recibido { background: rgba(74, 144, 226, 0.2); color: #4a90e2; }
        .status-en-cola { background: rgba(255, 193, 7, 0.2); color: #ffc107; }
        .status-procesando { background: rgba(255, 152, 0, 0.2); color: #ff9800; }
        .status-listo { background: rgba(76, 175, 80, 0.2); color: #4caf50; }
        .status-disponible { background: rgba(156, 39, 176, 0.2); color: #9c27b0; }

        .notification-time {
            color: var(--text-secondary);
            font-size: 0.8rem;
        }



        .service-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            background: var(--gradient-primary);
            color: white;
            border-radius: var(--border-radius);
            font-weight: 500;
            font-size: 0.9rem;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.3);
            border-radius: var(--border-radius);
            color: var(--accent-color);
        }

        .status-dot {
            width: 8px;
            height: 8px;
            background: var(--accent-color);
            border-radius: 50%;
            animation: pulse 2s infinite;
        }



        /* Tarjetas de Servicios de Soporte */
        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-bottom: 3rem;
        }

        .service-card {
            background: var(--surface);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            padding: 1.5rem;
            transition: var(--transition-normal);
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .service-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--gradient-primary);
            transform: scaleX(0);
            transition: var(--transition-normal);
        }

        .service-card:hover {
            background: var(--surface-light);
            border-color: var(--primary-color);
            box-shadow: var(--shadow-lg);
        }

        .service-card:hover::before {
            transform: scaleX(1);
        }

        .service-icon {
            width: 48px;
            height: 48px;
            background: var(--gradient-primary);
            border-radius: var(--border-radius);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
            color: white;
            font-size: 1.5rem;
        }

        .service-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }

        .service-description {
            color: var(--text-secondary);
            font-size: 0.9rem;
            line-height: 1.5;
            margin-bottom: 1rem;
        }

        .service-action {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--primary-color);
            font-weight: 500;
            font-size: 0.9rem;
            text-decoration: none;
            transition: var(--transition);
        }

        .service-action:hover {
            color: var(--accent-color);
            transform: translateX(4px);
        }

        /* Navigation Tabs Mejoradas */
        .nav-tabs {
            background: rgba(15, 23, 42, 0.8);
            backdrop-filter: blur(20px);
            padding: 1.5rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-top: 1rem;
        }

        .nav-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 1.5rem;
        }

        .tab-buttons {
            display: flex;
            gap: 0.75rem;
            overflow-x: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
            padding: 0.25rem;
            align-items: center;
        }

        .tab-buttons::-webkit-scrollbar {
            display: none;
        }

        .tab-btn {
            padding: 0.75rem 1.5rem;
            background: var(--surface);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            color: var(--text-secondary);
            cursor: pointer;
            transition: var(--transition-normal);
            white-space: nowrap;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            position: relative;
            overflow: hidden;
            box-shadow: var(--shadow-sm);
            min-width: fit-content;
            text-align: center;
        }

        .tab-btn::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--gradient-primary);
            transform: scaleX(0);
            transition: var(--transition-normal);
        }

        .tab-btn:hover {
            background: var(--surface-light);
            border-color: var(--primary-color);
            color: var(--text-primary);
            box-shadow: var(--shadow-md);
        }

        .tab-btn.active {
            background: var(--gradient-primary);
            border-color: var(--primary-color);
            color: white;
            box-shadow: var(--shadow-lg);
        }

        .tab-btn.active::before {
            transform: scaleX(1);
        }

        /* Content Sections */
        .content-section {
            padding: 3rem 0;
            background: var(--darker-bg);
        }

        .section-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 1.5rem;
        }

        .section-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 2rem;
        }

        .section-title {
            font-size: 1.8rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .section-title i {
            color: var(--primary-color);
        }

        /* Grid de Películas/Series */
        .movies-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 1.5rem;
        }

        .movie-card {
            background: var(--surface);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            overflow: hidden;
            transition: var(--transition-normal);
            position: relative;
            cursor: pointer;
        }

        .movie-card:hover {
            background: var(--surface-light);
            border-color: var(--primary-color);
            box-shadow: var(--shadow-lg);
        }

        .movie-poster {
            position: relative;
            aspect-ratio: 2/3;
            overflow: hidden;
        }

        .movie-poster img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: var(--transition);
        }

        .movie-card:hover .movie-poster img {
            opacity: 0.9;
        }

        .movie-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
                to bottom,
                transparent 0%,
                rgba(0, 0, 0, 0.3) 40%,
                rgba(0, 0, 0, 0.8) 100%
            );
            backdrop-filter: blur(2px);
            opacity: 0.3;
            transition: var(--transition-normal);
            display: flex;
            align-items: flex-end;
            padding: 1rem;
            cursor: pointer;
        }

        .movie-card:hover .movie-overlay {
            opacity: 1;
            backdrop-filter: blur(4px);
        }

        .movie-actions {
            display: flex;
            gap: 0.5rem;
            width: 100%;
        }

        .action-btn {
            flex: 1;
            padding: 0.6rem;
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: var(--radius-lg);
            cursor: pointer;
            transition: var(--transition-normal);
            font-weight: 500;
            font-size: 0.85rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.3rem;
            backdrop-filter: var(--blur-backdrop);
            box-shadow: var(--shadow-sm);
            position: relative;
            overflow: hidden;
            white-space: nowrap;
        }

        .btn-order {
            background: var(--gradient-primary);
            color: white;
            border-color: var(--primary-color);
        }

        .btn-order::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
            opacity: 0;
            transition: var(--transition-fast);
        }

        .btn-order:hover::before {
            opacity: 1;
        }

        .btn-order:hover {
            background: var(--primary-dark);
            box-shadow: var(--shadow-md);
        }

        .btn-details {
            background: var(--surface);
            color: white;
            border-color: var(--border-color);
        }

        .btn-details::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
            opacity: 0;
            transition: var(--transition-fast);
        }

        .btn-details:hover::before {
            opacity: 1;
        }

        .btn-details:hover {
            background: var(--surface-light);
            box-shadow: var(--shadow-md);
            border-color: var(--primary-color);
        }

        .movie-info {
            padding: 1rem;
        }

        .movie-title {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            line-height: 1.3;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .movie-meta {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 0.5rem;
            font-size: 0.85rem;
            color: var(--text-secondary);
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 0.3rem;
        }

        .meta-item i {
            color: var(--primary-color);
        }

        .movie-overview {
            font-size: 0.85rem;
            color: var(--text-secondary);
            line-height: 1.4;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        /* Modal Moderno */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            z-index: 2000;
            backdrop-filter: blur(5px);
        }

        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .modal-content {
            background: var(--secondary-color);
            border-radius: 12px;
            padding: 2rem;
            max-width: 500px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            border: 1px solid var(--border-color);
            box-shadow: var(--shadow-heavy);
            animation: slideUp 0.3s ease;
        }

        @keyframes slideUp {
            from { transform: translateY(50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .modal-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .modal-title {
            font-size: 1.5rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .close-btn {
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: 1.5rem;
            cursor: pointer;
            transition: var(--transition);
            padding: 0.5rem;
            border-radius: var(--border-radius);
        }

        .close-btn:hover {
            color: var(--text-primary);
            background: rgba(255,255,255,0.1);
        }

        .modal-body {
            margin-bottom: 1.5rem;
        }

        .modal-footer {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
        }

        .modal-btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: var(--transition);
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-cancel {
            background: var(--border-color);
            color: var(--text-primary);
        }

        .btn-cancel:hover {
            background: #555;
        }

        .btn-confirm {
            background: var(--gradient-primary);
            color: white;
        }

        .btn-confirm:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        /* Loading States */
        .loading {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .spinner {
            width: 16px;
            height: 16px;
            border: 2px solid transparent;
            border-top: 2px solid currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Estilos específicos para el modal de confirmación de pedido */
        #orderConfirmModal .modal-content {
            max-width: 500px;
            margin: 2rem auto;
        }

        #orderConfirmModal .modal-body h4 {
            font-size: 1.3rem;
            font-weight: 600;
        }

        #orderConfirmModal .modal-footer {
            gap: 1rem;
        }

        #orderConfirmModal .btn-confirm {
            background: var(--success-color);
            flex: 2;
        }

        #orderConfirmModal .btn-confirm:hover {
            background: #3bc55a;
            transform: translateY(-2px);
        }

        #orderConfirmModal .btn-cancel {
            flex: 1;
        }

        /* ===== COMPONENTES MODERNOS ===== */

        /* Filtros Avanzados */
        .advanced-filters {
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid rgba(255, 255, 255, 0.08);
            border-radius: var(--radius-xl);
            padding: var(--space-lg);
            margin-bottom: var(--space-xl);
            backdrop-filter: blur(10px);
            animation: slideDown 0.3s ease;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .filter-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--space-lg);
            align-items: start;
        }

        .filter-title {
            grid-column: 1 / -1;
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--space-md);
            display: flex;
            align-items: center;
            gap: var(--space-sm);
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: var(--space-sm);
        }

        .filter-label {
            font-weight: 500;
            color: var(--text-primary);
            font-size: 0.9rem;
        }

        .filter-options {
            display: flex;
            gap: var(--space-sm);
            flex-wrap: wrap;
        }

        .filter-btn {
            padding: var(--space-sm) var(--space-md);
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: var(--radius-lg);
            color: var(--text-secondary);
            cursor: pointer;
            transition: var(--transition-normal);
            display: flex;
            align-items: center;
            gap: var(--space-xs);
            font-size: 0.9rem;
            font-weight: 500;
        }

        .filter-btn:hover,
        .filter-btn.active {
            background: var(--gradient-primary);
            border-color: var(--primary-color);
            color: white;
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .year-range {
            display: flex;
            align-items: center;
            gap: var(--space-md);
        }

        .year-slider {
            flex: 1;
            height: 6px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: var(--radius-full);
            outline: none;
            -webkit-appearance: none;
        }

        .year-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 20px;
            height: 20px;
            background: var(--gradient-primary);
            border-radius: 50%;
            cursor: pointer;
            box-shadow: var(--shadow-md);
        }

        .year-slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            background: var(--gradient-primary);
            border-radius: 50%;
            cursor: pointer;
            border: none;
            box-shadow: var(--shadow-md);
        }

        .year-display {
            background: var(--gradient-primary);
            color: white;
            padding: var(--space-xs) var(--space-sm);
            border-radius: var(--radius-md);
            font-weight: 600;
            font-size: 0.9rem;
            min-width: 60px;
            text-align: center;
        }

        .btn-clear-filters {
            padding: var(--space-sm) var(--space-md);
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.3);
            border-radius: var(--radius-lg);
            color: var(--danger-color);
            cursor: pointer;
            transition: var(--transition-normal);
            display: flex;
            align-items: center;
            gap: var(--space-xs);
            font-weight: 500;
        }

        .btn-clear-filters:hover {
            background: rgba(239, 68, 68, 0.2);
            border-color: var(--danger-color);
            transform: translateY(-2px);
        }

        /* Floating Action Button con Menú */
        .fab-container {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .fab {
            width: 56px;
            height: 56px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            box-shadow: var(--shadow-lg);
            transition: var(--transition-normal);
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--gradient-primary);
            color: white;
            font-size: 1.5rem;
            position: relative;
        }

        .fab:hover {
            transform: scale(1.1);
            box-shadow: var(--shadow-xl);
        }

        .fab.active {
            transform: rotate(45deg);
        }

        .fab-menu {
            position: absolute;
            bottom: 70px;
            right: 0;
            display: flex;
            flex-direction: column;
            gap: var(--space-sm);
            opacity: 0;
            visibility: hidden;
            transform: translateY(20px);
            transition: var(--transition-normal);
        }

        .fab-container.open .fab-menu {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .mini-fab {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            box-shadow: var(--shadow-md);
            transition: var(--transition-normal);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            text-decoration: none;
            position: relative;
        }

        .mini-fab:hover {
            transform: scale(1.1);
            box-shadow: var(--shadow-lg);
        }

        .mini-fab.tickets {
            background: var(--gradient-secondary);
        }

        .mini-fab.chat {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        }

        .mini-fab.tutorial {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
        }

        /* Tooltips para mini FABs */
        .mini-fab::before {
            content: attr(data-tooltip);
            position: absolute;
            right: 60px;
            top: 50%;
            transform: translateY(-50%);
            background: var(--secondary-color);
            color: var(--text-primary);
            padding: var(--space-xs) var(--space-sm);
            border-radius: var(--radius-md);
            font-size: 0.8rem;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: var(--transition-fast);
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .mini-fab:hover::before {
            opacity: 1;
            visibility: visible;
        }

        /* Back to Top Button */
        .back-to-top {
            position: fixed;
            bottom: 2rem;
            left: 2rem;
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: var(--gradient-primary);
            border: none;
            color: white;
            cursor: pointer;
            opacity: 0;
            transform: translateY(100px);
            transition: var(--transition-normal);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .back-to-top.show {
            opacity: 1;
            transform: translateY(0);
        }

        .back-to-top:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
        }

        /* Animaciones de entrada */
        .animate-on-scroll {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .animate-on-scroll.animate-in {
            opacity: 1;
            transform: translateY(0);
        }

        /* Skeleton Loaders */
        .skeleton-card {
            background: rgba(255, 255, 255, 0.03);
            border-radius: var(--radius-xl);
            overflow: hidden;
            animation: pulse 1.5s ease-in-out infinite alternate;
        }

        .skeleton-image {
            aspect-ratio: 2/3;
            background: rgba(255, 255, 255, 0.1);
        }

        .skeleton-content {
            padding: var(--space-md);
        }

        .skeleton-title,
        .skeleton-text {
            height: 1rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: var(--radius-sm);
            margin-bottom: var(--space-sm);
        }

        .skeleton-text.short {
            width: 60%;
        }

        /* Toast Notifications */
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--gradient-surface);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: var(--radius-lg);
            padding: 1rem;
            box-shadow: var(--shadow-xl);
            z-index: 10000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            backdrop-filter: blur(10px);
            max-width: 400px;
        }

        .toast.show {
            transform: translateX(0);
        }

        .toast-content {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--text-primary);
        }

        .toast-success { border-left: 3px solid var(--success-color); }
        .toast-error { border-left: 3px solid var(--danger-color); }
        .toast-warning { border-left: 3px solid var(--warning-color); }
        .toast-info { border-left: 3px solid var(--accent-secondary); }

        /* Responsive Design */
        @media (max-width: 768px) {
            .header-container {
                flex-direction: column;
                gap: 1rem;
            }

            .search-container {
                order: 3;
                width: 100%;
            }

            .user-menu {
                order: 2;
                justify-content: center;
                width: 100%;
                gap: 0.5rem;
                flex-wrap: wrap;
                padding: 0.5rem;
            }

            .nav-btn {
                padding: 0.5rem 0.75rem;
                font-size: 0.8rem;
                gap: 0.4rem;
                min-width: 44px;
                flex: 0 0 auto;
            }

            .nav-btn span {
                display: none;
            }

            .nav-btn i {
                font-size: 1rem;
            }

            .user-info {
                padding: 0.5rem 0.75rem;
                font-size: 0.85rem;
                order: -1;
                width: 100%;
                justify-content: center;
                margin-bottom: 0.5rem;
                font-size: 0.85rem;
            }

            .movies-grid {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
                gap: 1rem;
            }

            .services-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }



            .support-access-btn {
                flex-direction: column;
                text-align: center;
                gap: 1rem;
                padding: 1.25rem;
            }

            .support-icon {
                width: 50px;
                height: 50px;
                font-size: 1.5rem;
            }

            .movie-overlay {
                opacity: 1 !important; /* Siempre visible en móviles */
                background: linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.8) 70%);
            }
        }

        /* Asegurar que los botones sean siempre clickeables */
        @media (max-width: 1024px) {
            .movie-overlay {
                opacity: 0.7 !important; /* Más visible en tablets y móviles */
            }
        }

            .modal-content {
                margin: 1rem;
                padding: 1.5rem;
            }

            .modal-footer {
                flex-direction: column;
            }



            .section-title {
                font-size: 1.5rem;
            }
        }

        @media (max-width: 480px) {
            .movies-grid {
                grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            }

            .services-grid {
                gap: 0.75rem;
            }

            .service-card {
                padding: 1rem;
            }

            .service-icon {
                width: 40px;
                height: 40px;
                font-size: 1.25rem;
            }

            .tab-buttons {
                justify-content: flex-start;
                padding-bottom: 0.5rem;
            }

            .tab-btn {
                padding: 0.6rem 1rem;
                font-size: 0.85rem;
                min-width: auto;
                flex-shrink: 0;
                gap: 0.4rem;
            }

            .tab-btn i {
                font-size: 0.9rem;
            }



            .support-access-btn {
                padding: 1rem;
                gap: 0.75rem;
            }

            .support-content h3 {
                font-size: 1.1rem;
            }

            .support-content p {
                font-size: 0.85rem;
            }
        }

        /* Animaciones adicionales */
        .fade-in {
            animation: fadeInUp 0.6s ease forwards;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .stagger-animation .movie-card {
            opacity: 0;
            animation: fadeInUp 0.6s ease forwards;
        }

        .stagger-animation .movie-card:nth-child(1) { animation-delay: 0.1s; }
        .stagger-animation .movie-card:nth-child(2) { animation-delay: 0.2s; }
        .stagger-animation .movie-card:nth-child(3) { animation-delay: 0.3s; }
        .stagger-animation .movie-card:nth-child(4) { animation-delay: 0.4s; }
        .stagger-animation .movie-card:nth-child(5) { animation-delay: 0.5s; }
        .stagger-animation .movie-card:nth-child(6) { animation-delay: 0.6s; }

        /* Efectos de hover mejorados */
        .movie-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent, rgba(37, 99, 235, 0.1), transparent);
            opacity: 0;
            transition: var(--transition);
            z-index: 1;
        }

        .movie-card:hover::before {
            opacity: 1;
        }

        /* Scrollbar personalizado */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--dark-bg);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--primary-dark);
        }

        /* ===== MEJORAS RESPONSIVE PARA COMPONENTES MODERNOS ===== */
        @media (max-width: 768px) {
            /* Filtros responsive */
            .filter-container {
                grid-template-columns: 1fr;
                gap: var(--space-md);
            }

            .filter-options {
                justify-content: center;
            }

            .year-range {
                flex-direction: column;
                gap: var(--space-sm);
                align-items: stretch;
            }

            /* FAB responsive */
            .fab-container {
                bottom: 1rem;
                right: 1rem;
            }

            .fab {
                width: 48px;
                height: 48px;
                font-size: 1.2rem;
            }

            .back-to-top {
                bottom: 1rem;
                left: 1rem;
                width: 40px;
                height: 40px;
            }

            /* Toast responsive */
            .toast {
                top: 10px;
                right: 10px;
                left: 10px;
                max-width: none;
                transform: translateY(-100%);
            }

            .toast.show {
                transform: translateY(0);
            }

            /* Tabs con filtros */
            .tab-buttons {
                overflow-x: auto;
                scrollbar-width: none;
                -ms-overflow-style: none;
                padding: 0 1rem;
            }

            .tab-buttons::-webkit-scrollbar {
                display: none;
            }

            /* Mejoras para botones en móvil */
            .movie-btn {
                min-height: 44px;
                padding: 0.6rem;
                font-size: 0.9rem;
            }

            .movie-actions {
                flex-direction: column;
                gap: 0.5rem;
            }

            /* Mejoras para tarjetas de película */
            .movie-card {
                min-height: auto;
            }

            .movie-info {
                padding: 0.75rem;
            }

            .movie-title {
                font-size: 0.9rem;
                line-height: 1.3;
            }
        }

        @media (max-width: 480px) {
            .filter-btn {
                padding: var(--space-xs) var(--space-sm);
                font-size: 0.8rem;
            }

            .filter-title {
                font-size: 1rem;
            }

            .year-display {
                min-width: 50px;
                font-size: 0.8rem;
            }

            .btn-clear-filters {
                padding: var(--space-xs) var(--space-sm);
                font-size: 0.8rem;
            }

            /* Tab buttons para móviles muy pequeños */
            .tab-btn {
                padding: 0.5rem 0.75rem;
                font-size: 0.8rem;
                gap: 0.3rem;
                border-radius: var(--radius-lg);
            }

            .tab-btn i {
                font-size: 0.8rem;
            }

            .tab-buttons {
                gap: 0.5rem;
                padding: 0.5rem;
            }

            /* Asegurar que el texto no se corte */
            .tab-btn {
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                max-width: none;
                flex: 0 0 auto;
            }

            /* Action buttons responsive */
            .action-btn {
                padding: 0.5rem 0.4rem;
                font-size: 0.8rem;
                gap: 0.25rem;
                min-width: 60px;
            }

            .action-btn i {
                font-size: 0.9rem;
            }

            .movie-actions {
                gap: 0.4rem;
            }

            /* User menu para móviles muy pequeños */
            .user-menu {
                gap: 0.4rem;
                padding: 0.4rem;
            }

            .nav-btn {
                padding: 0.4rem 0.6rem;
                font-size: 0.75rem;
                min-width: 40px;
                border-radius: var(--radius-lg);
            }

            .nav-btn i {
                font-size: 0.9rem;
            }

            .user-info {
                padding: 0.4rem 0.6rem;
                font-size: 0.8rem;
            }

            .user-info span {
                max-width: 150px;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }
    </style>
</head>
<body>
    <!-- Header Moderno -->
    <header class="header">
        <div class="header-container">
            <a href="index.php" class="logo">
                <i class="fas fa-headset"></i>
                <span>RGS TOOL</span>
            </a>

            <div class="search-container">
                <form method="GET" class="search-form">
                    <input
                        type="text"
                        name="query"
                        class="search-input"
                        placeholder="Buscar contenido, ayuda, documentación..."
                        value="<?php echo htmlspecialchars($_GET['query'] ?? ''); ?>"
                        autocomplete="off"
                    >
                    <button type="submit" class="search-btn">
                        <i class="fas fa-search"></i>
                    </button>
                </form>
            </div>

            <div class="user-menu">
                <div class="user-info">
                    <i class="fas fa-user"></i>
                    <span>Hola, <?php echo htmlspecialchars($username); ?></span>
                </div>

                <!-- Botón de Notificaciones -->
                <button id="notificationBtn" class="nav-btn notification-btn" onclick="checkNotifications()">
                    <i class="fas fa-bell"></i>
                    <span>Notificaciones</span>
                    <span id="notificationBadge" class="notification-badge" style="display: none;">0</span>
                </button>

                <a href="index2.php" class="nav-btn support-btn">
                    <i class="fas fa-headset"></i>
                    <span>Soporte</span>
                </a>

                <a href="pedidos.php" class="nav-btn">
                    <i class="fas fa-shopping-cart"></i>
                    <span>Mis Pedidos</span>
                </a>

                <a href="service_options.php" class="nav-btn service-request-btn">
                    <i class="fas fa-hand-holding-heart"></i>
                    <span>Solicitar Servicio</span>
                </a>
                <?php
                // Verificar si el usuario es admin (puedes ajustar esta lógica según tus necesidades)
                $stmt = $pdo->prepare("SELECT username FROM users WHERE id = ? AND username IN ('admin', 'Infest84')");
                $stmt->execute([$user_id]);
                $is_admin = $stmt->fetch();
                if ($is_admin):
                ?>
                <a href="admin_login.php" class="nav-btn admin-btn">
                    <i class="fas fa-cog"></i>
                    <span>Admin</span>
                </a>
                <?php endif; ?>
                <a href="logout.php" class="nav-btn logout-btn">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Salir</span>
                </a>
            </div>
        </div>
    </header>



    <!-- Navigation Tabs -->
    <nav class="nav-tabs">
        <div class="nav-container">
            <div class="tab-buttons">
                <button class="tab-btn active" data-section="trending-movies">
                    <i class="fas fa-film"></i>
                    Películas Populares
                </button>
                <button class="tab-btn" data-section="trending-tv">
                    <i class="fas fa-tv"></i>
                    Series Populares
                </button>
                <button class="tab-btn" data-section="upcoming">
                    <i class="fas fa-calendar-plus"></i>
                    Próximos Estrenos
                </button>
                <?php if (!empty($_GET['query'])): ?>
                <button class="tab-btn" data-section="search-results">
                    <i class="fas fa-search"></i>
                    Resultados de Búsqueda
                </button>
                <?php endif; ?>
                <button class="tab-btn" id="toggleFilters" onclick="toggleAdvancedFilters()">
                    <i class="fas fa-filter"></i>
                    Filtros
                </button>
            </div>
        </div>
    </nav>

    <!-- Filtros Avanzados -->
    <div class="advanced-filters" style="display: none;" id="advancedFilters">
        <div class="section-container">
            <div class="filter-container">
                <h3 class="filter-title">
                    <i class="fas fa-filter"></i>
                    Filtros Avanzados
                </h3>

                <div class="filter-group">
                    <label class="filter-label">Tipo de contenido</label>
                    <div class="filter-options">
                        <button class="filter-btn active" data-filter="all">
                            <i class="fas fa-th"></i> Todos
                        </button>
                        <button class="filter-btn" data-filter="movie">
                            <i class="fas fa-film"></i> Películas
                        </button>
                        <button class="filter-btn" data-filter="tv">
                            <i class="fas fa-tv"></i> Series
                        </button>
                    </div>
                </div>

                <div class="filter-group">
                    <label class="filter-label">Año</label>
                    <div class="year-range">
                        <input type="range" class="year-slider" id="yearSlider" min="1990" max="2024" value="2020">
                        <span class="year-display" id="yearDisplay">2020+</span>
                    </div>
                </div>

                <div class="filter-group">
                    <button class="btn-clear-filters" onclick="clearAllFilters()">
                        <i class="fas fa-times"></i>
                        Limpiar filtros
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Resultados de Búsqueda -->
    <?php if (!empty($_GET['query']) && !empty($search_results['results'])): ?>
    <section class="content-section" id="search-results">
        <div class="section-container">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-search"></i>
                    Resultados para "<?php echo htmlspecialchars($_GET['query']); ?>"
                </h2>
                <span class="results-count"><?php echo count($search_results['results']); ?> resultados</span>
            </div>
            <div class="movies-grid stagger-animation">
                <?php foreach ($search_results['results'] as $item):
                    $media_type = $item['media_type'] ?? ($item['first_air_date'] ?? null ? 'tv' : 'movie');
                    $title = $item['title'] ?? $item['name'] ?? 'Sin título';
                    $release_date = $item['release_date'] ?? $item['first_air_date'] ?? '';
                    $year = $release_date ? date('Y', strtotime($release_date)) : 'N/A';
                    $poster_path = $item['poster_path'] ? $image_base_url . $item['poster_path'] : 'https://via.placeholder.com/300x450/333/fff?text=Sin+Imagen';
                    $vote_average = $item['vote_average'] ?? 0;
                ?>
                <div class="movie-card" data-id="<?php echo $item['id']; ?>" data-type="<?php echo $media_type; ?>">
                    <div class="movie-poster">
                        <img src="<?php echo $poster_path; ?>" alt="<?php echo htmlspecialchars($title); ?>" loading="lazy">
                        <div class="movie-overlay">
                            <div class="movie-actions">
                                <button class="action-btn btn-order" onclick="openOrderModal(<?php echo $item['id']; ?>, '<?php echo addslashes($title); ?>', '<?php echo $media_type; ?>', '<?php echo $year; ?>')">
                                    <i class="fas fa-shopping-cart"></i>
                                    Pedir
                                </button>
                                <button class="action-btn btn-details" onclick="showDetails(<?php echo $item['id']; ?>, '<?php echo $media_type; ?>')">
                                    <i class="fas fa-info-circle"></i>
                                    Detalles
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="movie-info">
                        <h3 class="movie-title"><?php echo htmlspecialchars($title); ?></h3>
                        <div class="movie-meta">
                            <div class="meta-item">
                                <i class="fas fa-calendar"></i>
                                <span><?php echo $year; ?></span>
                            </div>
                            <?php if ($vote_average > 0): ?>
                            <div class="meta-item">
                                <i class="fas fa-star"></i>
                                <span><?php echo number_format($vote_average, 1); ?></span>
                            </div>
                            <?php endif; ?>
                            <div class="meta-item">
                                <i class="fas fa-<?php echo $media_type === 'movie' ? 'film' : 'tv'; ?>"></i>
                                <span><?php echo $media_type === 'movie' ? 'Película' : 'Serie'; ?></span>
                            </div>
                        </div>
                        <?php if (!empty($item['overview'])): ?>
                        <p class="movie-overview"><?php echo htmlspecialchars($item['overview']); ?></p>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>
    <?php elseif (!empty($_GET['query'])): ?>
    <section class="content-section">
        <div class="section-container">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-search"></i>
                    Sin resultados para "<?php echo htmlspecialchars($_GET['query']); ?>"
                </h2>
            </div>
            <p style="text-align: center; color: var(--text-secondary); font-size: 1.1rem;">
                No se encontraron resultados. Intenta con otros términos de búsqueda.
            </p>
        </div>
    </section>
    <?php endif; ?>

    <!-- Películas Trending -->
    <section class="content-section" id="trending-movies" <?php echo !empty($_GET['query']) ? 'style="display: none;"' : ''; ?>>
        <div class="section-container">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-film"></i>
                    Películas Populares
                </h2>
                <p style="font-size: 0.9rem; color: var(--text-secondary); margin: 0.5rem 0 0 0;">
                    💡 Presiona dos veces para pedir
                </p>
            </div>
            <div class="movies-grid stagger-animation">
                <?php if (!empty($trending_movies['results'])): ?>
                    <?php foreach ($trending_movies['results'] as $movie):
                        $title = $movie['title'] ?? 'Sin título';
                        $release_date = $movie['release_date'] ?? '';
                        $year = $release_date ? date('Y', strtotime($release_date)) : 'N/A';
                        $poster_path = $movie['poster_path'] ? $image_base_url . $movie['poster_path'] : 'https://via.placeholder.com/300x450/333/fff?text=Sin+Imagen';
                        $vote_average = $movie['vote_average'] ?? 0;
                    ?>
                    <div class="movie-card" data-id="<?php echo $movie['id']; ?>" data-type="movie">
                        <div class="movie-poster">
                            <img src="<?php echo $poster_path; ?>" alt="<?php echo htmlspecialchars($title); ?>" loading="lazy">
                            <div class="movie-overlay">
                                <div class="movie-actions">
                                    <button class="action-btn btn-order" onclick="openOrderModal(<?php echo $movie['id']; ?>, '<?php echo addslashes($title); ?>', 'movie', '<?php echo $year; ?>')">
                                        <i class="fas fa-shopping-cart"></i>
                                        Pedir
                                    </button>
                                    <button class="action-btn btn-details" onclick="showDetails(<?php echo $movie['id']; ?>, 'movie')">
                                        <i class="fas fa-info-circle"></i>
                                        Detalles
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="movie-info">
                            <h3 class="movie-title"><?php echo htmlspecialchars($title); ?></h3>
                            <div class="movie-meta">
                                <div class="meta-item">
                                    <i class="fas fa-calendar"></i>
                                    <span><?php echo $year; ?></span>
                                </div>
                                <?php if ($vote_average > 0): ?>
                                <div class="meta-item">
                                    <i class="fas fa-star"></i>
                                    <span><?php echo number_format($vote_average, 1); ?></span>
                                </div>
                                <?php endif; ?>
                                <div class="meta-item">
                                    <i class="fas fa-film"></i>
                                    <span>Película</span>
                                </div>
                            </div>
                            <?php if (!empty($movie['overview'])): ?>
                            <p class="movie-overview"><?php echo htmlspecialchars($movie['overview']); ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Series Trending -->
    <section class="content-section" id="trending-tv" style="display: none;">
        <div class="section-container">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-tv"></i>
                    Series Populares
                </h2>
            </div>
            <div class="movies-grid stagger-animation">
                <?php if (!empty($trending_tv['results'])): ?>
                    <?php foreach ($trending_tv['results'] as $show):
                        $title = $show['name'] ?? 'Sin título';
                        $release_date = $show['first_air_date'] ?? '';
                        $year = $release_date ? date('Y', strtotime($release_date)) : 'N/A';
                        $poster_path = $show['poster_path'] ? $image_base_url . $show['poster_path'] : 'https://via.placeholder.com/300x450/333/fff?text=Sin+Imagen';
                        $vote_average = $show['vote_average'] ?? 0;
                    ?>
                    <div class="movie-card" data-id="<?php echo $show['id']; ?>" data-type="tv">
                        <div class="movie-poster">
                            <img src="<?php echo $poster_path; ?>" alt="<?php echo htmlspecialchars($title); ?>" loading="lazy">
                            <div class="movie-overlay">
                                <div class="movie-actions">
                                    <button class="action-btn btn-order" onclick="openOrderModal(<?php echo $show['id']; ?>, '<?php echo addslashes($title); ?>', 'tv', '<?php echo $year; ?>')">
                                        <i class="fas fa-shopping-cart"></i>
                                        Pedir
                                    </button>
                                    <button class="action-btn btn-details" onclick="showDetails(<?php echo $show['id']; ?>, 'tv')">
                                        <i class="fas fa-info-circle"></i>
                                        Detalles
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="movie-info">
                            <h3 class="movie-title"><?php echo htmlspecialchars($title); ?></h3>
                            <div class="movie-meta">
                                <div class="meta-item">
                                    <i class="fas fa-calendar"></i>
                                    <span><?php echo $year; ?></span>
                                </div>
                                <?php if ($vote_average > 0): ?>
                                <div class="meta-item">
                                    <i class="fas fa-star"></i>
                                    <span><?php echo number_format($vote_average, 1); ?></span>
                                </div>
                                <?php endif; ?>
                                <div class="meta-item">
                                    <i class="fas fa-tv"></i>
                                    <span>Serie</span>
                                </div>
                            </div>
                            <?php if (!empty($show['overview'])): ?>
                            <p class="movie-overview"><?php echo htmlspecialchars($show['overview']); ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Próximos Estrenos -->
    <section class="content-section" id="upcoming" style="display: none;">
        <div class="section-container">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-calendar-plus"></i>
                    Próximos Estrenos
                </h2>
            </div>
            <div class="movies-grid stagger-animation">
                <?php if (!empty($upcoming_movies['results'])): ?>
                    <?php foreach ($upcoming_movies['results'] as $movie):
                        $title = $movie['title'] ?? 'Sin título';
                        $release_date = $movie['release_date'] ?? '';
                        $year = $release_date ? date('Y', strtotime($release_date)) : 'N/A';
                        $poster_path = $movie['poster_path'] ? $image_base_url . $movie['poster_path'] : 'https://via.placeholder.com/300x450/333/fff?text=Sin+Imagen';
                        $vote_average = $movie['vote_average'] ?? 0;
                    ?>
                    <div class="movie-card" data-id="<?php echo $movie['id']; ?>" data-type="movie">
                        <div class="movie-poster">
                            <img src="<?php echo $poster_path; ?>" alt="<?php echo htmlspecialchars($title); ?>" loading="lazy">
                            <div class="movie-overlay">
                                <div class="movie-actions">
                                    <button class="action-btn btn-order" onclick="openOrderModal(<?php echo $movie['id']; ?>, '<?php echo addslashes($title); ?>', 'movie', '<?php echo $year; ?>')">
                                        <i class="fas fa-shopping-cart"></i>
                                        Pedir
                                    </button>
                                    <button class="action-btn btn-details" onclick="showDetails(<?php echo $movie['id']; ?>, 'movie')">
                                        <i class="fas fa-info-circle"></i>
                                        Detalles
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="movie-info">
                            <h3 class="movie-title"><?php echo htmlspecialchars($title); ?></h3>
                            <div class="movie-meta">
                                <div class="meta-item">
                                    <i class="fas fa-calendar"></i>
                                    <span><?php echo $year; ?></span>
                                </div>
                                <?php if ($vote_average > 0): ?>
                                <div class="meta-item">
                                    <i class="fas fa-star"></i>
                                    <span><?php echo number_format($vote_average, 1); ?></span>
                                </div>
                                <?php endif; ?>
                                <div class="meta-item">
                                    <i class="fas fa-film"></i>
                                    <span>Película</span>
                                </div>
                            </div>
                            <?php if (!empty($movie['overview'])): ?>
                            <p class="movie-overview"><?php echo htmlspecialchars($movie['overview']); ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Modal de Pedido -->
    <div id="orderModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-shopping-cart"></i>
                    Confirmar Pedido
                </h3>
                <button class="close-btn" onclick="closeOrderModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div id="orderDetails">
                    <!-- Los detalles del pedido se llenarán con JavaScript -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="modal-btn btn-cancel" onclick="closeOrderModal()">
                    <i class="fas fa-times"></i>
                    Cancelar
                </button>
                <button type="button" class="modal-btn btn-confirm" onclick="confirmOrder()">
                    <i class="fas fa-check"></i>
                    Confirmar Pedido
                </button>
            </div>
        </div>
    </div>

    <!-- Modal de Detalles -->
    <div id="detailsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-info-circle"></i>
                    Detalles
                </h3>
                <button class="close-btn" onclick="closeDetailsModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div id="movieDetails">
                    <div class="loading">
                        <div class="spinner"></div>
                        Cargando detalles...
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="modal-btn btn-cancel" onclick="closeDetailsModal()">
                    <i class="fas fa-times"></i>
                    Cerrar
                </button>
                <a id="tmdbLink" href="#" target="_blank" class="modal-btn btn-confirm">
                    <i class="fas fa-external-link-alt"></i>
                    Ver en TMDB
                </a>
            </div>
        </div>
    </div>

    <?php if (isset($error_pedido)): ?>
    <!-- Modal de Error -->
    <div id="errorModal" class="modal show">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-exclamation-triangle" style="color: var(--warning-color);"></i>
                    Error al Procesar Pedido
                </h3>
                <button class="close-btn" onclick="closeErrorModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div style="text-align: center; margin-bottom: 1.5rem;">
                    <p style="font-size: 1.1rem; margin-bottom: 1rem; color: var(--warning-color);">
                        <?php echo htmlspecialchars($error_pedido); ?>
                    </p>
                    <p style="color: var(--text-secondary);">
                        Por favor, inténtalo de nuevo. Si el problema persiste, contacta al administrador.
                    </p>
                </div>
            </div>
            <div class="modal-footer">
                <button class="modal-btn btn-confirm" onclick="closeErrorModal()">
                    <i class="fas fa-check"></i>
                    Entendido
                </button>
            </div>
        </div>
    </div>

    <script>
        function closeErrorModal() {
            document.getElementById('errorModal').classList.remove('show');
            document.body.style.overflow = 'auto';
        }
    </script>
    <?php endif; ?>

    <?php if ($pedido_exitoso): ?>
    <!-- Modal de Éxito -->
    <div id="successModal" class="modal show">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-check-circle" style="color: var(--accent-color);"></i>
                    ¡Pedido Registrado Exitosamente!
                </h3>
                <button class="close-btn" onclick="closeSuccessModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div style="text-align: center; margin-bottom: 1.5rem;">
                    <p style="font-size: 1.1rem; margin-bottom: 1rem;">
                        Tu pedido de <strong>"<?php echo htmlspecialchars($pedido_data['title']); ?>"</strong> ha sido registrado y enviado al administrador.
                    </p>
                    <div style="background: var(--dark-bg); padding: 1.5rem; border-radius: var(--border-radius); border-left: 4px solid var(--accent-color); margin-bottom: 1.5rem;">
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 1rem;">
                            <div>
                                <strong style="color: var(--text-secondary); font-size: 0.9rem;">Tipo:</strong>
                                <p style="color: var(--text-primary); margin: 0;"><?php echo $pedido_data['media_type'] === 'movie' ? 'Película' : 'Serie'; ?></p>
                            </div>
                            <div>
                                <strong style="color: var(--text-secondary); font-size: 0.9rem;">Año:</strong>
                                <p style="color: var(--text-primary); margin: 0;"><?php echo $pedido_data['year']; ?></p>
                            </div>
                            <div>
                                <strong style="color: var(--text-secondary); font-size: 0.9rem;">ID TMDB:</strong>
                                <p style="color: var(--text-primary); margin: 0;"><?php echo $pedido_data['tmdb_id']; ?></p>
                            </div>
                        </div>
                    </div>

                    <div style="background: rgba(37, 211, 102, 0.1); padding: 1rem; border-radius: var(--border-radius); border: 1px solid rgba(37, 211, 102, 0.3); margin-bottom: 1.5rem;">
                        <p style="color: var(--accent-color); font-weight: 600; margin-bottom: 0.5rem;">
                            <i class="fas fa-info-circle"></i> ¿Quieres enviar también por WhatsApp?
                        </p>
                        <p style="color: var(--text-secondary); font-size: 0.9rem; margin-bottom: 1rem;">
                            Puedes enviar los detalles de tu pedido por WhatsApp para mayor seguimiento.
                        </p>
                        <button onclick="sendWhatsApp()" class="modal-btn" style="background: #25d366; color: white; width: 100%;">
                            <i class="fab fa-whatsapp"></i>
                            Enviar por WhatsApp
                        </button>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="modal-btn btn-cancel" onclick="closeSuccessModal()">
                    <i class="fas fa-times"></i>
                    Cerrar
                </button>
                <a href="pedidos.php" class="modal-btn btn-confirm">
                    <i class="fas fa-list"></i>
                    Ver Mis Pedidos
                </a>
            </div>
        </div>
    </div>

    <script>
        function sendWhatsApp() {
            const title = "<?php echo addslashes($pedido_data['title']); ?>";
            const mediaType = "<?php echo $pedido_data['media_type'] === 'movie' ? 'Película' : 'Serie'; ?>";
            const year = "<?php echo $pedido_data['year']; ?>";
            const tmdbId = "<?php echo $pedido_data['tmdb_id']; ?>";
            const username = "<?php echo addslashes($username); ?>";

            const message = `🎬 *PEDIDO DE ${mediaType.toUpperCase()}*

📽️ *Título:* ${title}
📅 *Año:* ${year}
👤 *Usuario:* ${username}

🔗 *Ver en TMDB:*
https://www.themoviedb.org/<?php echo $pedido_data['media_type']; ?>/${tmdbId}?language=es-MX

---
Pedido realizado desde RGS TOOL`;

            const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`;
            window.open(whatsappUrl, '_blank');
        }
    </script>
    <?php endif; ?>

    <!-- Modal de Notificaciones -->
    <div id="notificationModal" class="notification-modal">
        <div class="notification-content">
            <div class="notification-header">
                <h3 class="modal-title">
                    <i class="fas fa-bell"></i>
                    Notificaciones
                </h3>
                <button class="close-btn" onclick="closeNotificationModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="notificationList">
                <div class="loading">
                    <div class="spinner"></div>
                    Cargando notificaciones...
                </div>
            </div>
            <div class="modal-footer">
                <button class="modal-btn btn-cancel" onclick="closeNotificationModal()">
                    <i class="fas fa-times"></i>
                    Cerrar
                </button>
                <button class="modal-btn btn-confirm" onclick="markAllNotificationsRead()">
                    <i class="fas fa-check"></i>
                    Marcar como Leídas
                </button>
            </div>
        </div>
    </div>

    <script>
        // Variables globales
        let currentOrderData = {};
        let notificationCheckInterval;
        let hasUnreadNotifications = false;

        // Función de prueba global para debugging
        window.testOrderFunction = function() {
            console.log('🧪 Testing order function manually...');
            try {
                openOrderModal(12345, 'Test Movie', 'movie', '2023');
            } catch (error) {
                console.error('❌ Error in test:', error);
            }
        };

        // Función para verificar el estado del sistema
        window.debugOrderSystem = function() {
            console.log('🔍 DEBUGGING ORDER SYSTEM:');
            console.log('📋 Form exists:', !!document.getElementById('orderForm'));
            console.log('🔧 Function exists:', typeof openOrderModal === 'function');
            console.log('🔘 Button count:', document.querySelectorAll('.btn-order').length);
            console.log('👤 User ID from PHP:', '<?php echo $user_id; ?>');

            const firstButton = document.querySelector('.btn-order');
            if (firstButton) {
                console.log('🔘 First button onclick:', firstButton.getAttribute('onclick'));
                console.log('🔘 First button visible:', firstButton.offsetParent !== null);
            }
        };

        // Función para cambiar entre tabs
        function switchTab(sectionId) {
            // Ocultar todas las secciones
            document.querySelectorAll('.content-section').forEach(section => {
                section.style.display = 'none';
            });

            // Mostrar la sección seleccionada
            const targetSection = document.getElementById(sectionId);
            if (targetSection) {
                targetSection.style.display = 'block';

                // Reiniciar animaciones
                const grid = targetSection.querySelector('.movies-grid');
                if (grid) {
                    grid.classList.remove('stagger-animation');
                    setTimeout(() => {
                        grid.classList.add('stagger-animation');
                    }, 50);
                }
            }

            // Actualizar botones activos
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-section="${sectionId}"]`).classList.add('active');
        }

        // Event listeners para los tabs
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const sectionId = btn.getAttribute('data-section');
                switchTab(sectionId);
            });
        });

        // Función para abrir modal de pedido
        function openOrderModal(tmdbId, title, mediaType, year) {
            console.log('🎬 openOrderModal called with:', { tmdbId, title, mediaType, year });

            // Verificar que los elementos del formulario existen
            const form = document.getElementById('orderForm');
            const tmdbInput = document.getElementById('pedido_tmdb_id');
            const titleInput = document.getElementById('pedido_title');
            const typeInput = document.getElementById('pedido_media_type');
            const yearInput = document.getElementById('pedido_year');

            if (!form || !tmdbInput || !titleInput || !typeInput || !yearInput) {
                console.error('❌ Missing form elements!');
                showCustomAlert('Error: Elementos del formulario no encontrados. Recarga la página.');
                return;
            }

            // Llenar los datos del modal
            currentOrderData = { tmdbId, title, mediaType, year };

            // Mostrar modal de confirmación personalizado
            showOrderConfirmationModal(tmdbId, title, mediaType, year);
        }

        // Función para mostrar modal de confirmación personalizado
        function showOrderConfirmationModal(tmdbId, title, mediaType, year) {
            // Crear modal dinámicamente
            const modalHtml = `
                <div id="orderConfirmModal" class="modal show">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3 class="modal-title">
                                <i class="fas fa-shopping-cart"></i>
                                Confirmar Pedido
                            </h3>
                            <button class="close-btn" onclick="closeOrderConfirmModal()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="modal-body">
                            <div style="text-align: center; margin-bottom: 1.5rem;">
                                <h4 style="color: var(--primary-color); margin-bottom: 1rem;">${title}</h4>
                                <p style="color: var(--text-secondary); margin-bottom: 1rem;">
                                    ${mediaType === 'movie' ? 'Película' : 'Serie'} • ${year}
                                </p>
                                <p style="font-size: 1.1rem; margin-bottom: 1rem;">
                                    ¿Deseas hacer el pedido de este contenido?
                                </p>
                                <p style="color: var(--text-secondary); font-size: 0.9rem;">
                                    Se enviará una solicitud al administrador para que agregue este contenido al servidor.
                                </p>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button class="modal-btn btn-cancel" onclick="closeOrderConfirmModal()">
                                <i class="fas fa-times"></i>
                                Cancelar
                            </button>
                            <button class="modal-btn btn-confirm" onclick="confirmOrderSubmission()">
                                <i class="fas fa-check"></i>
                                Confirmar Pedido
                            </button>
                        </div>
                    </div>
                </div>
            `;

            // Agregar modal al DOM
            document.body.insertAdjacentHTML('beforeend', modalHtml);
            document.body.style.overflow = 'hidden';
        }

        // Función para cerrar modal de confirmación
        function closeOrderConfirmModal() {
            const modal = document.getElementById('orderConfirmModal');
            if (modal) {
                modal.remove();
                document.body.style.overflow = 'auto';
            }
        }

        // Función para confirmar y enviar el pedido
        function confirmOrderSubmission() {
            try {
                console.log('✅ User confirmed, processing order...');

                const form = document.getElementById('orderForm');
                const tmdbInput = document.getElementById('pedido_tmdb_id');
                const titleInput = document.getElementById('pedido_title');
                const typeInput = document.getElementById('pedido_media_type');
                const yearInput = document.getElementById('pedido_year');

                // Fill hidden form
                tmdbInput.value = currentOrderData.tmdbId;
                titleInput.value = currentOrderData.title;
                typeInput.value = currentOrderData.mediaType;
                yearInput.value = currentOrderData.year;

                console.log('📝 Form values set:', {
                    tmdb_id: tmdbInput.value,
                    title: titleInput.value,
                    media_type: typeInput.value,
                    year: yearInput.value
                });

                // Cerrar modal
                closeOrderConfirmModal();

                // Submit form
                console.log('🚀 Submitting form...');
                form.submit();
                console.log('✅ Form.submit() called');

            } catch (error) {
                console.error('❌ Error in confirmOrderSubmission:', error);
                showCustomAlert('Error al procesar el pedido: ' + error.message);
            }
        }

        // Función para mostrar alertas personalizadas
        function showCustomAlert(message) {
            const alertHtml = `
                <div id="customAlert" class="modal show">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3 class="modal-title">
                                <i class="fas fa-exclamation-triangle"></i>
                                Aviso
                            </h3>
                        </div>
                        <div class="modal-body">
                            <p style="text-align: center; margin: 1rem 0;">${message}</p>
                        </div>
                        <div class="modal-footer">
                            <button class="modal-btn btn-confirm" onclick="closeCustomAlert()">
                                <i class="fas fa-check"></i>
                                Entendido
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', alertHtml);
            document.body.style.overflow = 'hidden';
        }

        // Función para cerrar alerta personalizada
        function closeCustomAlert() {
            const alert = document.getElementById('customAlert');
            if (alert) {
                alert.remove();
                document.body.style.overflow = 'auto';
            }
        }

        function showDetails(tmdbId, mediaType) {
            window.open(`https://www.themoviedb.org/${mediaType}/${tmdbId}?language=es-MX`, '_blank');
        }

        // Función para cerrar modal de éxito
        function closeSuccessModal() {
            console.log('🔄 Closing success modal...');
            const modal = document.getElementById('successModal');
            if (modal) {
                modal.classList.remove('show');
                document.body.style.overflow = 'auto';
                console.log('✅ Success modal closed');
            } else {
                console.error('❌ Success modal not found');
            }
        }

        // Función para cerrar modal de pedido
        function closeOrderModal() {
            console.log('🔄 Closing order modal...');
            const modal = document.getElementById('orderModal');
            if (modal) {
                modal.classList.remove('show');
                document.body.style.overflow = 'auto';
                console.log('✅ Order modal closed');
            }
        }

        // Función para cerrar modal de detalles
        function closeDetailsModal() {
            console.log('🔄 Closing details modal...');
            const modal = document.getElementById('detailsModal');
            if (modal) {
                modal.classList.remove('show');
                document.body.style.overflow = 'auto';
                console.log('✅ Details modal closed');
            }
        }

        // Función para confirmar pedido (del modal)
        function confirmOrder() {
            console.log('🔄 Confirming order...');
            // Esta función se usaría si tuviéramos un modal de confirmación
            // Por ahora, cerrar el modal
            closeOrderModal();
        }

        // Update success modal to include WhatsApp option
        <?php if ($pedido_exitoso): ?>
        function showWhatsAppConfirm() {
            // Ya no mostrar confirmación automática de WhatsApp
            // El usuario puede usar el botón de WhatsApp en el modal de éxito si lo desea
            console.log('WhatsApp option available in success modal');
        }

        // Auto trigger WhatsApp confirm after successful order - DISABLED
        // document.addEventListener('DOMContentLoaded', () => {
        //     setTimeout(showWhatsAppConfirm, 1000);
        // });
        <?php endif; ?>

        // Cerrar modales al hacer clic fuera
        document.querySelectorAll('.modal').forEach(modal => {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.classList.remove('show');
                    document.body.style.overflow = 'auto';
                }
            });
        });

        // Cerrar modales con Escape
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                document.querySelectorAll('.modal.show').forEach(modal => {
                    modal.classList.remove('show');
                });
                document.body.style.overflow = 'auto';
            }
        });

        // Lazy loading para imágenes
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src || img.src;
                        img.classList.remove('lazy');
                        observer.unobserve(img);
                    }
                });
            });

            document.querySelectorAll('img[loading="lazy"]').forEach(img => {
                imageObserver.observe(img);
            });
        }

        // Animación de entrada para las cards
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const cardObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observar todas las movie cards
        document.querySelectorAll('.movie-card').forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            cardObserver.observe(card);
        });

        // Mejorar la experiencia de búsqueda
        const searchInput = document.querySelector('.search-input');
        if (searchInput) {
            let searchTimeout;

            searchInput.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                const query = e.target.value.trim();

                if (query.length >= 3) {
                    searchTimeout = setTimeout(() => {
                        // Aquí podrías implementar búsqueda en tiempo real con AJAX
                        console.log('Búsqueda en tiempo real:', query);
                    }, 500);
                }
            });
        }

        // Funciones de Notificaciones (Sistema Actualizado)
        async function checkUserNotifications() {
            try {
                // Verificar notificaciones de solicitudes de servicio
                const serviceResponse = await fetch('get_client_notifications.php');
                const serviceData = await serviceResponse.json();

                // Verificar notificaciones de tickets (sistema existente)
                const ticketResponse = await fetch('api_notifications.php?action=check_user_notifications');
                const ticketData = await ticketResponse.json();

                let totalCount = 0;

                // Contar notificaciones de servicios
                if (serviceData.success && serviceData.unread_count > 0) {
                    totalCount += serviceData.unread_count;
                }

                // Contar notificaciones de tickets
                if (ticketData.has_notifications && ticketData.count > 0) {
                    totalCount += ticketData.count;
                }

                if (totalCount > 0) {
                    document.getElementById('notificationBadge').textContent = totalCount;
                    document.getElementById('notificationBadge').style.display = 'flex';
                    hasUnreadNotifications = true;
                } else {
                    document.getElementById('notificationBadge').style.display = 'none';
                    hasUnreadNotifications = false;
                }
            } catch (error) {
                console.error('Error checking notifications:', error);
                // En caso de error, ocultar badge
                document.getElementById('notificationBadge').style.display = 'none';
            }
        }

        async function checkNotifications() {
            document.getElementById('notificationModal').classList.add('show');
            document.body.style.overflow = 'hidden';

            try {
                // Obtener notificaciones de solicitudes de servicio
                const serviceResponse = await fetch('get_client_notifications.php');
                const serviceData = await serviceResponse.json();

                // Obtener notificaciones de tickets (sistema existente)
                const ticketResponse = await fetch('api_notifications.php?action=get_user_notifications');
                const ticketData = await ticketResponse.json();

                const notificationList = document.getElementById('notificationList');
                let allNotifications = [];

                // Procesar notificaciones de servicios
                if (serviceData.success && serviceData.notifications) {
                    serviceData.notifications.forEach(notification => {
                        allNotifications.push({
                            type: 'service',
                            id: notification.id,
                            title: notification.title,
                            message: notification.message,
                            created_at: notification.created_at,
                            is_read: notification.is_read,
                            notification_type: notification.type,
                            request_id: notification.request_id,
                            credentials: notification.credentials,
                            service_info: notification.service_info
                        });
                    });
                }

                // Procesar notificaciones de tickets
                if (ticketData.notifications && ticketData.notifications.length > 0) {
                    ticketData.notifications.forEach(notification => {
                        allNotifications.push({
                            type: 'ticket',
                            id: notification.id,
                            title: `Ticket #${notification.id}`,
                            message: notification.title,
                            created_at: notification.updated_at,
                            is_read: notification.status_notif_seen == 1,
                            status: notification.status
                        });
                    });
                }

                if (allNotifications.length > 0) {
                    // Ordenar por fecha (más recientes primero)
                    allNotifications.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

                    notificationList.innerHTML = allNotifications.map(notification => {
                        const isNew = !notification.is_read ? 'new' : '';
                        const timeAgo = getTimeAgo(notification.created_at);

                        // Determinar el icono según el tipo de notificación
                        let icon = 'fas fa-circle';
                        let actionText = '';
                        let statusText = '';
                        let statusClass = '';

                        if (notification.type === 'service') {
                            // Notificaciones de servicios IPTV
                            switch (notification.notification_type) {
                                case 'service_activated':
                                    icon = 'fas fa-check-circle';
                                    statusText = 'Servicio Activado';
                                    statusClass = 'status-completed';
                                    break;
                                case 'credentials_sent':
                                    icon = 'fas fa-key';
                                    statusText = 'Credenciales Enviadas';
                                    statusClass = 'status-completed';
                                    break;
                                case 'renewal_reminder':
                                    icon = 'fas fa-clock';
                                    statusText = 'Recordatorio de Renovación';
                                    statusClass = 'status-pending';
                                    break;
                                case 'payment_received':
                                    icon = 'fas fa-credit-card';
                                    statusText = 'Pago Recibido';
                                    statusClass = 'status-completed';
                                    break;
                                default:
                                    icon = 'fas fa-hand-holding-heart';
                                    statusText = 'Mensaje del Servicio';
                                    statusClass = 'status-info';
                            }

                            actionText = `<div class="notification-action">
                                <a href="service_request_confirmation.php?id=${notification.request_id}" style="color: var(--primary-color); text-decoration: none; font-size: 0.8rem;">
                                    <i class="fas fa-external-link-alt"></i> Ver solicitud
                                </a>
                            </div>`;

                            // Mostrar credenciales si están disponibles
                            if (notification.credentials) {
                                actionText += `<div class="notification-credentials" style="margin-top: 0.5rem; padding: 0.5rem; background: rgba(16, 185, 129, 0.1); border-radius: 4px; font-size: 0.8rem;">
                                    <strong>Credenciales:</strong><br>
                                    Usuario: ${notification.credentials.username || 'N/A'}<br>
                                    Contraseña: ${notification.credentials.password || 'N/A'}
                                </div>`;
                            }

                        } else if (notification.type === 'ticket') {
                            // Notificaciones de tickets (sistema existente)
                            icon = 'fas fa-ticket-alt';
                            statusText = notification.status || 'Ticket';
                            statusClass = `status-${notification.status ? notification.status.toLowerCase().replace(' ', '-') : 'pending'}`;
                            actionText = `<div class="notification-action">
                                <a href="user_tickets.php" style="color: var(--primary-color); text-decoration: none; font-size: 0.8rem;">
                                    <i class="fas fa-external-link-alt"></i> Ver ticket
                                </a>
                            </div>`;
                        } else {
                            // Notificaciones de pedidos (sistema existente)
                            icon = 'fas fa-film';
                            statusText = notification.status || 'Pedido';
                            statusClass = `status-${notification.status ? notification.status.toLowerCase().replace(' ', '-') : 'pending'}`;
                        }

                        return `
                            <div class="notification-item ${isNew}" data-id="${notification.id}" data-type="${notification.type}">
                                <div class="notification-title">${notification.title}</div>
                                <div class="notification-message" style="color: var(--text-secondary); font-size: 0.9rem; margin: 0.5rem 0;">
                                    ${notification.message}
                                </div>
                                <div class="notification-status ${statusClass}">
                                    <i class="${icon}"></i>
                                    ${statusText}
                                </div>
                                <div class="notification-time">
                                    <i class="fas fa-clock"></i>
                                    ${timeAgo}
                                </div>
                                ${actionText}
                            </div>
                        `;
                    }).join('');
                } else {
                    notificationList.innerHTML = `
                        <div style="text-align: center; padding: 2rem; color: var(--text-secondary);">
                            <i class="fas fa-bell-slash" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                            <p>No tienes notificaciones nuevas</p>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Error loading notifications:', error);
                document.getElementById('notificationList').innerHTML = `
                    <div style="text-align: center; padding: 2rem; color: var(--error-color);">
                        <i class="fas fa-exclamation-triangle" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                        <p>Error al cargar notificaciones</p>
                    </div>
                `;
            }
        }

        function closeNotificationModal() {
            document.getElementById('notificationModal').classList.remove('show');
            document.body.style.overflow = 'auto';
        }

        async function markAllNotificationsRead() {
            try {
                // Marcar notificaciones de servicios como leídas
                const serviceNotifications = document.querySelectorAll('.notification-item[data-type="service"]');
                for (const notification of serviceNotifications) {
                    const notificationId = notification.dataset.id;
                    if (notificationId) {
                        await fetch('mark_notification_read.php', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                            },
                            body: `notification_id=${notificationId}`
                        });
                    }
                }

                // Marcar notificaciones de pedidos como leídas
                const ordersResponse = await fetch('api_notifications.php?action=mark_user_notifications_seen', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: ''
                });

                // Marcar notificaciones de tickets como leídas
                const ticketsResponse = await fetch('api_notifications.php?action=mark_ticket_notifications_seen', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: ''
                });

                // Actualizar UI
                document.getElementById('notificationBadge').style.display = 'none';
                hasUnreadNotifications = false;
                closeNotificationModal();

                // Actualizar la lista de notificaciones
                document.getElementById('notificationList').innerHTML = `
                    <div style="text-align: center; padding: 2rem; color: var(--text-secondary);">
                        <i class="fas fa-bell-slash" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                        <p>No tienes notificaciones nuevas</p>
                    </div>
                `;

                // Mostrar mensaje de éxito
                showToast('Todas las notificaciones marcadas como leídas', 'success');

            } catch (error) {
                console.error('Error marking notifications as read:', error);
                showToast('Error al marcar notificaciones como leídas', 'error');
            }
        }

        function getTimeAgo(dateString) {
            const now = new Date();
            const date = new Date(dateString);
            const diffInSeconds = Math.floor((now - date) / 1000);

            if (diffInSeconds < 60) return 'Hace un momento';
            if (diffInSeconds < 3600) return `Hace ${Math.floor(diffInSeconds / 60)} minutos`;
            if (diffInSeconds < 86400) return `Hace ${Math.floor(diffInSeconds / 3600)} horas`;
            return `Hace ${Math.floor(diffInSeconds / 86400)} días`;
        }

        // Inicialización
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🚀 Page loaded, initializing...');
            console.log('👤 PHP User ID: <?php echo $user_id; ?>');
            console.log('👤 PHP Username: <?php echo addslashes($username); ?>');

            // Verificar si hay errores de pedido
            <?php if (isset($error_pedido)): ?>
            console.error('❌ PHP Error detected: <?php echo addslashes($error_pedido); ?>');
            <?php endif; ?>

            // Verificar si hay pedido exitoso
            <?php if ($pedido_exitoso): ?>
            console.log('✅ Successful order detected');
            <?php endif; ?>

            // Verificar elementos críticos del formulario
            const form = document.getElementById('orderForm');
            if (!form) {
                console.error('❌ CRITICAL: Order form not found!');
            } else {
                console.log('✅ Order form found');
            }

            // Verificar que la función openOrderModal está disponible
            if (typeof openOrderModal === 'function') {
                console.log('✅ openOrderModal function is available');
            } else {
                console.error('❌ CRITICAL: openOrderModal function not found!');
            }

            // Contar botones de pedido
            const orderButtons = document.querySelectorAll('.btn-order');
            console.log(`🔘 Found ${orderButtons.length} order buttons`);

            // Verificar algunos botones específicos
            orderButtons.forEach((button, index) => {
                if (index < 3) { // Solo los primeros 3 para no saturar la consola
                    console.log(`🔘 Button ${index + 1}:`, {
                        onclick: button.getAttribute('onclick'),
                        visible: button.offsetParent !== null,
                        disabled: button.disabled
                    });
                }
            });

            // Test manual de la función
            console.log('🧪 Testing openOrderModal function manually...');
            try {
                // No ejecutar, solo verificar que se puede llamar
                console.log('✅ openOrderModal can be called (test passed)');
            } catch (error) {
                console.error('❌ Error testing openOrderModal:', error);
            }

            // Si hay resultados de búsqueda, mostrar esa sección
            <?php if (!empty($_GET['query']) && !empty($search_results['results'])): ?>
            switchTab('search-results');
            document.querySelector('[data-section="search-results"]').classList.add('active');
            document.querySelector('[data-section="trending-movies"]').classList.remove('active');
            <?php endif; ?>

            // Iniciar verificación de notificaciones
            checkUserNotifications();
            notificationCheckInterval = setInterval(checkUserNotifications, 30000); // Cada 30 segundos

            // Agregar event listeners adicionales a los botones de pedido
            document.querySelectorAll('.btn-order').forEach((button, index) => {
                button.addEventListener('click', function(event) {
                    console.log(`🖱️ Order button ${index + 1} clicked!`);
                    console.log('🖱️ Event details:', {
                        target: event.target,
                        onclick: this.getAttribute('onclick'),
                        preventDefault: 'Not called yet'
                    });

                    // No prevenir el evento, dejar que el onclick funcione
                    console.log('🖱️ Allowing onclick to execute...');
                });
            });

            // Solución alternativa: Agregar event listeners a las tarjetas de película
            document.querySelectorAll('.movie-card').forEach((card, index) => {
                // Agregar un indicador visual de que es clickeable
                card.style.cursor = 'pointer';

                // Agregar click simple en el área del overlay
                const overlay = card.querySelector('.movie-overlay');
                if (overlay) {
                    overlay.addEventListener('click', function(event) {
                        // Si el clic no fue en un botón específico, activar el pedido
                        if (!event.target.closest('.btn-details')) {
                            const orderButton = card.querySelector('.btn-order');
                            if (orderButton) {
                                console.log(`🖱️ Click on overlay ${index + 1}, triggering order button`);
                                const onclickAttr = orderButton.getAttribute('onclick');
                                if (onclickAttr) {
                                    // Ejecutar el onclick manualmente
                                    eval(onclickAttr);
                                }
                            }
                        }
                    });
                }

                // Mantener doble click como alternativa
                card.addEventListener('dblclick', function(event) {
                    const orderButton = this.querySelector('.btn-order');
                    if (orderButton) {
                        console.log(`🖱️ Double-click on card ${index + 1}, triggering order button`);
                        const onclickAttr = orderButton.getAttribute('onclick');
                        if (onclickAttr) {
                            // Ejecutar el onclick manualmente
                            eval(onclickAttr);
                        }
                    }
                });
            });

            // Agregar efectos de hover mejorados
            document.querySelectorAll('.movie-card').forEach(card => {
                card.addEventListener('mouseenter', () => {
                    card.style.zIndex = '10';
                });

                card.addEventListener('mouseleave', () => {
                    card.style.zIndex = '1';
                });
            });
        });

        // Limpiar interval al salir de la página
        window.addEventListener('beforeunload', () => {
            if (notificationCheckInterval) {
                clearInterval(notificationCheckInterval);
            }
        });
    </script>

    <!-- Place this form at the end of the body tag, before the closing </body> -->
    <form id="orderForm" method="POST" style="position: absolute; visibility: hidden;">
        <input type="hidden" name="pedido_tmdb_id" id="pedido_tmdb_id">
        <input type="hidden" name="pedido_title" id="pedido_title">
        <input type="hidden" name="pedido_media_type" id="pedido_media_type">
        <input type="hidden" name="pedido_year" id="pedido_year">
        <?php if (!empty($_GET['query'])): ?>
        <input type="hidden" name="query" value="<?php echo htmlspecialchars($_GET['query']); ?>">
        <?php endif; ?>
    </form>

    <!-- Floating Action Button con Menú -->
    <div class="fab-container" id="fabContainer">
        <button class="fab" onclick="toggleFabMenu()" title="Acciones rápidas">
            <i class="fas fa-plus"></i>
        </button>
        <div class="fab-menu">
            <a href="user_tickets.php" class="mini-fab tickets" data-tooltip="Gestión de Tickets">
                <i class="fas fa-ticket-alt"></i>
            </a>
            <a href="index2.php" class="mini-fab chat" data-tooltip="Chat y Soporte">
                <i class="fas fa-comments"></i>
            </a>
            <a href="#" class="mini-fab tutorial" data-tooltip="Tutoriales" onclick="openTutorials(event)">
                <i class="fas fa-graduation-cap"></i>
            </a>
        </div>
    </div>

    <!-- Back to Top Button -->
    <button class="back-to-top" id="backToTop" onclick="scrollToTop()">
        <i class="fas fa-chevron-up"></i>
    </button>

    <script>
        // ===== FUNCIONALIDADES MODERNAS =====

        // Inicializar funcionalidades modernas
        document.addEventListener('DOMContentLoaded', function() {
            initModernFeatures();
        });

        function initModernFeatures() {
            initScrollEffects();
            initKeyboardNavigation();
            initAdvancedSearch();
            initAnimationsOnScroll();
            initFilterFunctionality();
        }

        // ===== EFECTOS DE SCROLL =====
        function initScrollEffects() {
            const backToTop = document.getElementById('backToTop');

            window.addEventListener('scroll', function() {
                if (window.pageYOffset > 300) {
                    backToTop.classList.add('show');
                } else {
                    backToTop.classList.remove('show');
                }
            });
        }

        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        // ===== NAVEGACIÓN POR TECLADO =====
        function initKeyboardNavigation() {
            document.addEventListener('keydown', function(e) {
                // Esc para cerrar modales
                if (e.key === 'Escape') {
                    closeAllModals();
                }

                // Ctrl/Cmd + K para enfocar búsqueda
                if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                    e.preventDefault();
                    const searchInput = document.querySelector('.search-input');
                    if (searchInput) {
                        searchInput.focus();
                        searchInput.select();
                    }
                }
            });
        }

        function closeAllModals() {
            document.querySelectorAll('.modal').forEach(modal => {
                modal.classList.remove('show');
            });
            document.getElementById('notificationModal').classList.remove('show');
            document.body.style.overflow = 'auto';
        }

        // ===== BÚSQUEDA AVANZADA =====
        function initAdvancedSearch() {
            const searchInput = document.querySelector('.search-input');
            const searchForm = document.querySelector('.search-form');

            if (searchInput && searchForm) {
                searchForm.addEventListener('submit', function(e) {
                    const query = searchInput.value.trim();
                    if (query.length < 2) {
                        e.preventDefault();
                        showToast('Por favor ingresa al menos 2 caracteres', 'warning');
                        searchInput.focus();
                    } else {
                        showLoadingState();
                    }
                });
            }
        }

        function showLoadingState() {
            const searchBtn = document.querySelector('.search-btn');
            if (searchBtn) {
                const originalContent = searchBtn.innerHTML;
                searchBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                searchBtn.disabled = true;

                // Restaurar después de 5 segundos como fallback
                setTimeout(() => {
                    searchBtn.innerHTML = originalContent;
                    searchBtn.disabled = false;
                }, 5000);
            }
        }

        // ===== ANIMACIONES AL HACER SCROLL =====
        function initAnimationsOnScroll() {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate-in');
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            });

            // Agregar observador a elementos
            document.querySelectorAll('.movie-card, .service-card').forEach(el => {
                el.classList.add('animate-on-scroll');
                observer.observe(el);
            });
        }

        // ===== FUNCIONALIDAD DE FILTROS =====
        function toggleAdvancedFilters() {
            const filters = document.getElementById('advancedFilters');
            const toggleBtn = document.getElementById('toggleFilters');

            if (filters.style.display === 'none' || !filters.style.display) {
                filters.style.display = 'block';
                toggleBtn.classList.add('active');
            } else {
                filters.style.display = 'none';
                toggleBtn.classList.remove('active');
            }
        }

        function initFilterFunctionality() {
            // Filtros de tipo
            document.querySelectorAll('.filter-btn[data-filter]').forEach(btn => {
                btn.addEventListener('click', function() {
                    // Remover active de otros botones
                    document.querySelectorAll('.filter-btn[data-filter]').forEach(b => b.classList.remove('active'));
                    // Agregar active al botón clickeado
                    this.classList.add('active');

                    const filter = this.dataset.filter;
                    applyTypeFilter(filter);
                });
            });

            // Slider de año
            const yearSlider = document.getElementById('yearSlider');
            const yearDisplay = document.getElementById('yearDisplay');

            if (yearSlider && yearDisplay) {
                yearSlider.addEventListener('input', function() {
                    yearDisplay.textContent = this.value + '+';
                    applyYearFilter(this.value);
                });
            }
        }

        function applyTypeFilter(type) {
            const cards = document.querySelectorAll('.movie-card');

            cards.forEach(card => {
                if (type === 'all') {
                    card.style.display = 'block';
                } else {
                    const cardType = card.dataset.type;
                    card.style.display = cardType === type ? 'block' : 'none';
                }
            });

            showToast(`Filtro aplicado: ${type === 'all' ? 'Todos' : type === 'movie' ? 'Películas' : 'Series'}`, 'info');
        }

        function applyYearFilter(year) {
            // Esta función se puede expandir para filtrar por año
            showToast(`Filtro de año aplicado: ${year}+`, 'info');
        }

        function clearAllFilters() {
            // Resetear filtros de tipo
            document.querySelectorAll('.filter-btn[data-filter]').forEach(btn => btn.classList.remove('active'));
            document.querySelector('.filter-btn[data-filter="all"]').classList.add('active');

            // Resetear slider de año
            const yearSlider = document.getElementById('yearSlider');
            const yearDisplay = document.getElementById('yearDisplay');
            if (yearSlider && yearDisplay) {
                yearSlider.value = 2020;
                yearDisplay.textContent = '2020+';
            }

            // Mostrar todas las tarjetas
            document.querySelectorAll('.movie-card').forEach(card => {
                card.style.display = 'block';
            });

            showToast('Filtros limpiados', 'success');
        }

        // ===== FLOATING ACTION BUTTON =====
        function toggleFabMenu() {
            const fabContainer = document.getElementById('fabContainer');
            const fab = fabContainer.querySelector('.fab');

            fabContainer.classList.toggle('open');
            fab.classList.toggle('active');
        }

        function openTutorials(event) {
            event.preventDefault();
            showToast('Sección de tutoriales próximamente disponible', 'info');
        }

        // Cerrar FAB menu al hacer click fuera
        document.addEventListener('click', function(event) {
            const fabContainer = document.getElementById('fabContainer');
            if (fabContainer && !fabContainer.contains(event.target)) {
                fabContainer.classList.remove('open');
                fabContainer.querySelector('.fab').classList.remove('active');
            }
        });

        // ===== SISTEMA DE TOAST =====
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.innerHTML = `
                <div class="toast-content">
                    <i class="fas fa-${getToastIcon(type)}"></i>
                    <span>${message}</span>
                </div>
            `;

            document.body.appendChild(toast);

            // Mostrar toast
            setTimeout(() => toast.classList.add('show'), 100);

            // Ocultar toast
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => toast.remove(), 300);
            }, 3000);
        }

        function getToastIcon(type) {
            const icons = {
                'success': 'check-circle',
                'error': 'exclamation-circle',
                'warning': 'exclamation-triangle',
                'info': 'info-circle'
            };
            return icons[type] || 'info-circle';
        }
    </script>

    <!-- Modal de Solicitud de Servicio -->
    <?php include 'service_request_modal.php'; ?>

    <!-- Estilos del Modal -->
    <link rel="stylesheet" href="service_request_styles.css">

    <!-- Scripts del Modal -->
    <script src="service_request_script.js"></script>
</body>
</html>
