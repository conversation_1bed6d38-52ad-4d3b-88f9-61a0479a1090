<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 Servicios IPTV - Opciones Disponibles</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            /* Paleta de colores profesional con verde como principal */
            --primary-color: #10b981;
            --primary-dark: #059669;
            --primary-light: #34d399;
            --secondary-color: #1f2937;
            --dark-bg: #111827;
            --darker-bg: #030712;
            --surface: #374151;
            --surface-light: #4b5563;
            --surface-elevated: #6b7280;

            /* Colores de texto profesionales */
            --text-primary: #f9fafb;
            --text-secondary: #d1d5db;
            --text-muted: #9ca3af;
            --text-accent: #34d399;

            /* Gradientes corporativos 3D con verde */
            --gradient-primary: linear-gradient(135deg, #10b981 0%, #059669 50%, #047857 100%);
            --gradient-secondary: linear-gradient(135deg, #34d399 0%, #10b981 50%, #059669 100%);
            --gradient-surface: linear-gradient(135deg, #1f2937 0%, #374151 50%, #4b5563 100%);
            --gradient-hero: linear-gradient(135deg, #030712 0%, #111827 25%, #1f2937 50%, #374151 100%);
            --gradient-card: linear-gradient(145deg, #1f2937 0%, #374151 100%);
            --gradient-elevated: linear-gradient(145deg, #374151 0%, #4b5563 100%);

            /* Sombras 3D profesionales */
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            --shadow-3d: 0 8px 16px rgba(0, 0, 0, 0.15), 0 4px 8px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);
            --shadow-elevated: 0 12px 24px rgba(0, 0, 0, 0.2), 0 6px 12px rgba(0, 0, 0, 0.15);
            --shadow-glow: 0 0 20px rgba(16, 185, 129, 0.4);

            /* Espaciado y bordes */
            --space-xs: 0.25rem;
            --space-sm: 0.5rem;
            --space-md: 1rem;
            --space-lg: 1.5rem;
            --space-xl: 2rem;
            --space-2xl: 3rem;
            --radius-sm: 6px;
            --radius-md: 8px;
            --radius-lg: 12px;
            --radius-xl: 16px;
            --radius-full: 9999px;

            /* Transiciones */
            --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);

            /* Transformaciones 3D */
            --transform-hover: translateY(-4px) scale(1.02);
            --transform-card: perspective(1000px) rotateX(0deg) rotateY(0deg);

            /* Filtros */
            --blur-backdrop: blur(12px) saturate(150%);
            --brightness-hover: brightness(1.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gradient-hero);
            background-attachment: fixed;
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
            font-feature-settings: "kern" 1, "liga" 1;
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Efectos de fondo */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 25% 25%, rgba(16, 185, 129, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(52, 211, 153, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 50% 50%, rgba(5, 150, 105, 0.06) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: var(--space-xl);
        }

        .header {
            text-align: center;
            margin-bottom: var(--space-2xl);
        }

        .page-title {
            font-size: 3rem;
            font-weight: 800;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: var(--space-md);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
        }

        .page-subtitle {
            font-size: 1.2rem;
            color: var(--text-secondary);
            max-width: 600px;
            margin: 0 auto;
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: var(--space-xl);
            margin-bottom: var(--space-2xl);
        }

        .service-card {
            background: var(--gradient-card);
            border: 1px solid rgba(255, 255, 255, 0.12);
            border-radius: var(--radius-xl);
            backdrop-filter: var(--blur-backdrop);
            overflow: hidden;
            transition: var(--transition-normal);
            box-shadow: var(--shadow-3d);
            transform: var(--transform-card);
            position: relative;
        }

        .service-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
            transform: scaleX(0);
            transition: var(--transition-normal);
        }

        .service-card:hover {
            background: var(--gradient-elevated);
            border-color: var(--primary-color);
            transform: var(--transform-hover);
            box-shadow: var(--shadow-2xl), var(--shadow-glow);
            filter: var(--brightness-hover);
        }

        .service-card:hover::before {
            transform: scaleX(1);
        }

        .service-header {
            padding: var(--space-xl);
            text-align: center;
            position: relative;
        }

        .service-icon {
            width: 80px;
            height: 80px;
            background: var(--gradient-primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto var(--space-lg);
            font-size: 2rem;
            color: white;
            box-shadow: var(--shadow-md), var(--shadow-glow);
            transition: var(--transition-normal);
        }

        .service-card:hover .service-icon {
            transform: scale(1.1) rotateY(15deg);
        }

        .service-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .service-description {
            color: var(--text-secondary);
            margin-bottom: var(--space-lg);
        }

        .service-features {
            padding: 0 var(--space-xl) var(--space-xl);
        }

        .features-list {
            list-style: none;
            margin-bottom: var(--space-lg);
        }

        .features-list li {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.5rem 0;
            color: var(--text-secondary);
        }

        .features-list li::before {
            content: '✓';
            color: var(--primary-color);
            font-weight: bold;
            font-size: 1.1rem;
        }

        .service-action {
            padding: 0 var(--space-xl) var(--space-xl);
        }

        .btn {
            width: 100%;
            padding: 1rem 1.5rem;
            border: none;
            border-radius: var(--radius-lg);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-normal);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            text-decoration: none;
            font-size: 1rem;
        }

        .btn-primary {
            background: var(--gradient-primary);
            color: white;
            box-shadow: var(--shadow-sm), var(--shadow-glow);
        }

        .btn-primary:hover {
            background: var(--gradient-primary);
            transform: var(--transform-hover);
            box-shadow: var(--shadow-md), var(--shadow-glow);
            filter: var(--brightness-hover);
        }

        .btn-secondary {
            background: var(--gradient-surface);
            color: var(--text-primary);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: var(--shadow-sm);
        }

        .btn-secondary:hover {
            background: var(--gradient-elevated);
            border-color: var(--primary-color);
            transform: var(--transform-hover);
            box-shadow: var(--shadow-md);
        }

        .back-link {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--text-secondary);
            text-decoration: none;
            margin-bottom: var(--space-xl);
            padding: 0.75rem 1rem;
            background: var(--gradient-surface);
            border-radius: var(--radius-lg);
            transition: var(--transition-normal);
        }

        .back-link:hover {
            color: var(--text-primary);
            background: var(--gradient-elevated);
            transform: translateX(-4px);
        }

        .promo-badge {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: var(--radius-full);
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            box-shadow: var(--shadow-sm);
        }

        /* Estilos modernos para modales */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(3, 7, 18, 0.85);
            backdrop-filter: blur(12px) saturate(150%);
            z-index: 10000;
            align-items: center;
            justify-content: center;
            padding: 1rem;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .modal.show {
            display: flex;
            opacity: 1;
        }

        .modal-content {
            background: var(--gradient-card);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: var(--radius-xl);
            max-width: min(95vw, 900px);
            max-height: 95vh;
            overflow: hidden;
            box-shadow:
                0 25px 50px -12px rgba(0, 0, 0, 0.4),
                0 0 0 1px rgba(255, 255, 255, 0.05),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            animation: modalSlideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
            position: relative;
        }

        .modal-content::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: var(--gradient-primary);
            opacity: 0.8;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-30px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        /* Estilos del header del modal */
        .modal-header {
            padding: 2rem 2rem 1rem 2rem;
            background: var(--gradient-surface);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
        }

        .modal-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .modal-title i {
            color: var(--primary-color);
            font-size: 1.3rem;
        }

        .modal-close {
            background: rgba(255, 255, 255, 0.1);
            border: none;
            color: var(--text-secondary);
            font-size: 1.2rem;
            cursor: pointer;
            padding: 0.75rem;
            border-radius: 50%;
            transition: var(--transition-normal);
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
        }

        .modal-close:hover {
            background: rgba(239, 68, 68, 0.2);
            color: #ef4444;
            transform: scale(1.1);
        }

        /* Contenido del modal */
        .modal-body {
            padding: 0;
            max-height: calc(95vh - 120px);
            overflow-y: auto;
        }

        /* Scrollbar personalizado */
        .modal-body::-webkit-scrollbar {
            width: 8px;
        }

        .modal-body::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.05);
        }

        .modal-body::-webkit-scrollbar-thumb {
            background: var(--gradient-primary);
            border-radius: 4px;
        }

        .modal-body::-webkit-scrollbar-thumb:hover {
            background: var(--primary-dark);
        }

        /* Estilos de formulario */
        .form-step {
            padding: 2rem;
            display: none;
        }

        .form-step.active {
            display: block;
        }

        .step-header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .step-header h4 {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .step-header p {
            color: var(--text-secondary);
            font-size: 0.95rem;
        }

        /* Grid de formulario */
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            padding: 0.875rem 1rem;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: var(--radius-lg);
            color: var(--text-primary);
            font-size: 0.95rem;
            transition: var(--transition-normal);
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            background: rgba(255, 255, 255, 0.08);
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
        }

        .form-group small {
            color: var(--text-muted);
            font-size: 0.8rem;
            margin-top: 0.25rem;
        }

        /* Botones del formulario */
        .form-actions {
            display: flex;
            gap: 1rem;
            justify-content: space-between;
            padding-top: 2rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .btn {
            padding: 0.875rem 1.5rem;
            border: none;
            border-radius: var(--radius-lg);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-normal);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            text-decoration: none;
            font-size: 0.95rem;
            min-width: 120px;
        }

        .btn-primary {
            background: var(--gradient-primary);
            color: white;
            box-shadow: var(--shadow-sm);
        }

        .btn-primary:hover {
            background: var(--gradient-primary);
            transform: translateY(-2px);
            box-shadow: var(--shadow-md), var(--shadow-glow);
        }

        .btn-secondary {
            background: var(--gradient-surface);
            color: var(--text-primary);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .btn-secondary:hover {
            background: var(--gradient-elevated);
            border-color: var(--primary-color);
            transform: translateY(-2px);
        }

        .btn-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            box-shadow: var(--shadow-sm);
        }

        .btn-success:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            transform: translateY(-2px);
            box-shadow: var(--shadow-md), var(--shadow-glow);
        }

        /* Indicador de progreso */
        .progress-indicator {
            display: flex;
            justify-content: center;
            gap: 1rem;
            padding: 1.5rem 2rem;
            background: rgba(255, 255, 255, 0.02);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .progress-step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-muted);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.9rem;
            transition: var(--transition-normal);
            border: 2px solid transparent;
        }

        .progress-step.active {
            background: var(--gradient-primary);
            color: white;
            border-color: rgba(255, 255, 255, 0.2);
            box-shadow: var(--shadow-glow);
            transform: scale(1.1);
        }

        /* Estilos adicionales para asegurar que los modales funcionen */
        .modal {
            display: none !important;
        }

        .modal.show {
            display: flex !important;
            opacity: 1;
        }

        .modal-content {
            position: relative;
            z-index: 10001;
        }

        /* Estilos para botones de cerrar */
        .modal-close {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: rgba(255, 255, 255, 0.1);
            border: none;
            color: var(--text-secondary);
            font-size: 1.2rem;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: var(--transition-normal);
        }

        .modal-close:hover {
            background: rgba(239, 68, 68, 0.2);
            color: #ef4444;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .container {
                padding: var(--space-lg);
            }

            .page-title {
                font-size: 2rem;
                flex-direction: column;
                gap: 0.5rem;
            }

            .services-grid {
                grid-template-columns: 1fr;
                gap: var(--space-lg);
            }

            .service-header {
                padding: var(--space-lg);
            }

            .service-features {
                padding: 0 var(--space-lg) var(--space-lg);
            }

            .service-action {
                padding: 0 var(--space-lg) var(--space-lg);
            }

            .modal-content {
                max-width: 95vw;
                max-height: 95vh;
                margin: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="index.php" class="back-link">
            <i class="fas fa-arrow-left"></i>
            Volver al Inicio
        </a>

        <div class="header">
            <h1 class="page-title">
                <i class="fas fa-hand-holding-heart"></i>
                Servicios IPTV Disponibles
            </h1>
            <p class="page-subtitle">
                Elige el servicio que mejor se adapte a tus necesidades. Ofrecemos opciones para nuevos usuarios, renovaciones y revendedores.
            </p>
        </div>

        <div class="services-grid">
            <!-- ACTUALIZADO: 3 tarjetas de servicios - Nuevo, Renovar, Revendedor -->
            <!-- Nuevo Servicio -->
            <div class="service-card">
                <div class="service-header">
                    <div class="service-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <h2 class="service-title">Nuevo Servicio</h2>
                    <p class="service-description">
                        Solicita tu primer servicio IPTV con prueba gratuita o activación directa
                    </p>
                </div>
                <div class="service-features">
                    <ul class="features-list">
                        <li>Prueba gratuita de 24-48 horas</li>
                        <li>Configuración guiada paso a paso</li>
                        <li>Aplicaciones recomendadas para tu dispositivo</li>
                        <li>Soporte técnico incluido</li>
                        <li>Activación inmediata</li>
                    </ul>
                </div>
                <div class="service-action">
                    <button onclick="openServiceRequestModal()" class="btn btn-primary">
                        <i class="fas fa-rocket"></i>
                        Solicitar Nuevo Servicio
                    </button>
                </div>
            </div>

            <!-- Renovar Servicio -->
            <div class="service-card">
                <div class="promo-badge">¡Promoción!</div>
                <div class="service-header">
                    <div class="service-icon">
                        <i class="fas fa-refresh"></i>
                    </div>
                    <h2 class="service-title">Renovar Servicio</h2>
                    <p class="service-description">
                        Renueva tu servicio existente con descuentos especiales
                    </p>
                </div>
                <div class="service-features">
                    <ul class="features-list">
                        <li>Descuentos por renovación anticipada</li>
                        <li>Planes de 1, 3, 6 meses y 1 año</li>
                        <li>Promociones exclusivas disponibles</li>
                        <li>Subida de comprobante de pago</li>
                        <li>Renovación sin interrupciones</li>
                    </ul>
                </div>
                <div class="service-action">
                    <button onclick="openRenewalModal()" class="btn btn-primary">
                        <i class="fas fa-sync-alt"></i>
                        Renovar Mi Servicio
                    </button>
                </div>
            </div>

            <!-- Revendedor -->
            <div class="service-card">
                <div class="service-header">
                    <div class="service-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h2 class="service-title">Ser Revendedor</h2>
                    <p class="service-description">
                        Únete a nuestro programa de revendedores y obtén beneficios exclusivos
                    </p>
                </div>
                <div class="service-features">
                    <ul class="features-list">
                        <li>Precios especiales para revendedores</li>
                        <li>Panel de gestión de clientes</li>
                        <li>Soporte técnico prioritario</li>
                        <li>Comisiones atractivas</li>
                        <li>Material promocional incluido</li>
                    </ul>
                </div>
                <div class="service-action">
                    <button onclick="openResellerModal()" class="btn btn-secondary">
                        <i class="fas fa-handshake"></i>
                        Solicitar Ser Revendedor
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Incluir modales -->
    <?php include 'service_request_modal.php'; ?>
    <?php include 'renewal_modal.php'; ?>
    <?php include 'reseller_modal.php'; ?>

    <!-- Scripts -->
    <script src="service_request_script.js"></script>
    <script src="renewal_script.js"></script>
    <script src="reseller_script.js"></script>

    <!-- Funciones directas como respaldo -->
    <script>
        // Funciones principales para abrir modales (respaldo directo)
        function openServiceRequestModal() {
            console.log('📱 [DIRECT] Abriendo modal de servicio...');
            const modal = document.getElementById('serviceRequestModal');
            if (modal) {
                modal.style.display = 'flex';
                modal.classList.add('show');
                document.body.style.overflow = 'hidden';
                console.log('✅ [DIRECT] Modal de servicio abierto');
            } else {
                console.error('❌ [DIRECT] Modal de servicio no encontrado');
            }
        }

        function openRenewalModal() {
            console.log('📱 [DIRECT] Abriendo modal de renovación...');
            const modal = document.getElementById('renewalModal');
            if (modal) {
                modal.style.display = 'flex';
                modal.classList.add('show');
                document.body.style.overflow = 'hidden';
                console.log('✅ [DIRECT] Modal de renovación abierto');
            } else {
                console.error('❌ [DIRECT] Modal de renovación no encontrado');
            }
        }

        function openResellerModal() {
            console.log('📱 [DIRECT] Abriendo modal de revendedor...');
            const modal = document.getElementById('resellerModal');
            if (modal) {
                modal.style.display = 'flex';
                modal.classList.add('show');
                document.body.style.overflow = 'hidden';
                console.log('✅ [DIRECT] Modal de revendedor abierto');
            } else {
                console.error('❌ [DIRECT] Modal de revendedor no encontrado');
            }
        }

        // Funciones para cerrar modales
        function closeServiceRequestModal() {
            closeModal('serviceRequestModal');
        }

        function closeRenewalModal() {
            closeModal('renewalModal');
        }

        function closeResellerModal() {
            closeModal('resellerModal');
        }
    </script>

    <script>
        // Funciones para abrir los modales desde service_options.php

        // Función para mostrar toast messages
        function showToast(message, type = 'info') {
            // Crear elemento toast
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 1rem 1.5rem;
                border-radius: 8px;
                color: white;
                font-weight: 600;
                z-index: 10000;
                transform: translateX(100%);
                transition: transform 0.3s ease;
                max-width: 400px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            `;

            // Colores según el tipo
            const colors = {
                success: '#10b981',
                error: '#ef4444',
                warning: '#f59e0b',
                info: '#3b82f6'
            };

            toast.style.backgroundColor = colors[type] || colors.info;
            toast.textContent = message;

            // Agregar al DOM
            document.body.appendChild(toast);

            // Mostrar con animación
            setTimeout(() => {
                toast.style.transform = 'translateX(0)';
            }, 100);

            // Ocultar después de 4 segundos
            setTimeout(() => {
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            }, 4000);
        }

        // Función de inicialización principal
        function initializeServiceOptions() {
            console.log('🚀 Inicializando service_options.php...');

            // Verificar si los modales existen en el DOM
            const modals = {
                'serviceRequestModal': 'Modal de nuevo servicio',
                'renewalModal': 'Modal de renovación',
                'resellerModal': 'Modal de revendedor'
            };

            let modalsFound = 0;
            for (const [modalId, description] of Object.entries(modals)) {
                const modal = document.getElementById(modalId);
                if (modal) {
                    console.log(`✅ ${description} encontrado`);
                    modalsFound++;
                } else {
                    console.error(`❌ ${description} NO encontrado`);
                }
            }

            console.log(`📊 Modales encontrados: ${modalsFound}/${Object.keys(modals).length}`);

            // Verificar si los scripts de modales están cargados
            const functions = {
                'openServiceRequestModal': 'service_request_script.js',
                'openRenewalModal': 'renewal_script.js',
                'openResellerModal': 'reseller_script.js'
            };

            let functionsFound = 0;
            for (const [funcName, scriptName] of Object.entries(functions)) {
                if (typeof window[funcName] === 'function') {
                    console.log(`✅ ${funcName} disponible`);
                    functionsFound++;
                } else {
                    console.warn(`⚠️ ${funcName} no disponible (${scriptName})`);
                }
            }

            console.log(`📊 Funciones encontradas: ${functionsFound}/${Object.keys(functions).length}`);

            // Configurar event listeners para cerrar modales
            setupModalEventListeners();

            // Mostrar estado de inicialización
            if (modalsFound === Object.keys(modals).length) {
                showToast('✅ Modales cargados correctamente', 'success');
            } else {
                showToast(`⚠️ Solo ${modalsFound}/${Object.keys(modals).length} modales encontrados`, 'warning');
            }
        }

        // Función de respaldo para abrir modal de servicio
        function openServiceRequestModalFallback() {
            console.log('🔍 [SERVICE] Intentando abrir modal de servicio...');

            // Verificar si el modal existe
            const modal = document.getElementById('serviceRequestModal');
            if (!modal) {
                console.error('❌ [SERVICE] Modal serviceRequestModal no encontrado en el DOM');
                console.log('🔍 [SERVICE] Modales disponibles:', document.querySelectorAll('.modal'));
                showToast('Error: Modal de servicio no encontrado', 'error');
                return;
            }

            console.log('✅ [SERVICE] Modal encontrado:', modal);

            // Intentar usar la función original
            if (typeof openServiceRequestModal === 'function') {
                console.log('✅ [SERVICE] Usando función original openServiceRequestModal');
                try {
                    openServiceRequestModal();
                    return;
                } catch (error) {
                    console.error('❌ [SERVICE] Error en función original:', error);
                }
            }

            // Función directa como respaldo
            console.log('⚠️ [SERVICE] Usando función de respaldo directa');
            console.log('🔍 [SERVICE] Estado actual del modal:', {
                display: modal.style.display,
                classes: modal.className,
                visible: modal.offsetParent !== null
            });

            modal.style.display = 'flex';
            modal.classList.add('show');
            document.body.style.overflow = 'hidden';

            console.log('✅ [SERVICE] Modal configurado. Estado final:', {
                display: modal.style.display,
                classes: modal.className,
                visible: modal.offsetParent !== null
            });

            showToast('Modal de servicio abierto', 'success');
        }

        // Función de respaldo para abrir modal de renovación
        function openRenewalModalFallback() {
            console.log('🔍 Intentando abrir modal de renovación...');

            // Verificar si el modal existe
            const modal = document.getElementById('renewalModal');
            if (!modal) {
                console.error('❌ Modal renewalModal no encontrado');
                showToast('Error: Modal de renovación no encontrado', 'error');
                return;
            }

            // Intentar usar la función original
            if (typeof openRenewalModal === 'function') {
                console.log('✅ Usando función original openRenewalModal');
                openRenewalModal();
                return;
            }

            // Función directa como respaldo
            console.log('⚠️ Usando función de respaldo directa');
            modal.style.display = 'flex';
            modal.classList.add('show');
            document.body.style.overflow = 'hidden';
            showToast('Modal de renovación abierto', 'success');
        }

        // Función de respaldo para abrir modal de revendedor
        function openResellerModalFallback() {
            console.log('🔍 Intentando abrir modal de revendedor...');

            // Verificar si el modal existe
            const modal = document.getElementById('resellerModal');
            if (!modal) {
                console.error('❌ Modal resellerModal no encontrado');
                showToast('Error: Modal de revendedor no encontrado', 'error');
                return;
            }

            // Intentar usar la función original
            if (typeof openResellerModal === 'function') {
                console.log('✅ Usando función original openResellerModal');
                openResellerModal();
                return;
            }

            // Función directa como respaldo
            console.log('⚠️ Usando función de respaldo directa');
            modal.style.display = 'flex';
            modal.classList.add('show');
            document.body.style.overflow = 'hidden';
            showToast('Modal de revendedor abierto', 'success');
        }

        // Función para cerrar modales
        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.style.display = 'none';
                modal.classList.remove('show');
                document.body.style.overflow = 'auto';
            }
        }

        // Configurar event listeners para modales
        function setupModalEventListeners() {
            console.log('🔧 Configurando event listeners para modales...');

            // Cerrar modal al hacer clic en el botón X
            document.querySelectorAll('.modal-close').forEach(button => {
                button.addEventListener('click', function() {
                    const modal = this.closest('.modal');
                    if (modal) {
                        console.log(`🔒 Cerrando modal: ${modal.id}`);
                        closeModal(modal.id);
                    }
                });
            });

            // Cerrar modal al hacer clic fuera
            document.querySelectorAll('.modal').forEach(modal => {
                modal.addEventListener('click', function(e) {
                    if (e.target === modal) {
                        console.log(`🔒 Cerrando modal por clic fuera: ${modal.id}`);
                        closeModal(modal.id);
                    }
                });
            });

            // Cerrar modal con tecla Escape
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    const openModals = document.querySelectorAll('.modal.show');
                    if (openModals.length > 0) {
                        console.log('🔒 Cerrando modales con Escape');
                        openModals.forEach(modal => {
                            closeModal(modal.id);
                        });
                    }
                }
            });

            console.log('✅ Event listeners configurados');
        }

        // Inicializar cuando el DOM esté listo
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📄 DOM Content Loaded - Iniciando service_options.php');

            // Esperar un poco para que los includes se procesen
            setTimeout(initializeServiceOptions, 100);
        });
    </script>

    <!-- Script para cargar servicios dinámicamente -->
    <script src="update_service_options_dynamic.js"></script>
</body>
</html>
