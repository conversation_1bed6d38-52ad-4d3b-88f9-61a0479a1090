<?php
session_start();
require_once 'config.php';

// Verificar si el usuario está logueado como admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

// Procesar acciones
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'update_status':
                $ticket_id = (int)$_POST['ticket_id'];
                $new_status = $_POST['status'];
                $stmt = $pdo->prepare("UPDATE support_tickets SET status = ?, updated_at = NOW() WHERE id = ?");
                $stmt->execute([$new_status, $ticket_id]);
                $success_message = "Estado del ticket actualizado correctamente";
                break;
                
            case 'assign_ticket':
                $ticket_id = (int)$_POST['ticket_id'];
                $admin_id = (int)$_POST['admin_id'];
                $stmt = $pdo->prepare("UPDATE support_tickets SET assigned_to = ?, updated_at = NOW() WHERE id = ?");
                $stmt->execute([$admin_id, $ticket_id]);
                $success_message = "Ticket asignado correctamente";
                break;
                
            case 'add_response':
                $ticket_id = (int)$_POST['ticket_id'];
                $message = $_POST['message'];
                $admin_id = $_SESSION['admin_id'] ?? 1;
                
                $stmt = $pdo->prepare("INSERT INTO ticket_responses (ticket_id, user_id, message, is_admin_response) VALUES (?, ?, ?, 1)");
                $stmt->execute([$ticket_id, $admin_id, $message]);
                
                // Actualizar timestamp del ticket
                $stmt = $pdo->prepare("UPDATE support_tickets SET updated_at = NOW() WHERE id = ?");
                $stmt->execute([$ticket_id]);
                
                $success_message = "Respuesta agregada correctamente";
                break;
        }
    }
}

// Obtener filtros
$status_filter = $_GET['status'] ?? 'all';
$priority_filter = $_GET['priority'] ?? 'all';
$category_filter = $_GET['category'] ?? 'all';

// Construir consulta
$where_conditions = [];
$params = [];

if ($status_filter !== 'all') {
    $where_conditions[] = "st.status = ?";
    $params[] = $status_filter;
}

if ($priority_filter !== 'all') {
    $where_conditions[] = "st.priority = ?";
    $params[] = $priority_filter;
}

if ($category_filter !== 'all') {
    $where_conditions[] = "st.category = ?";
    $params[] = $category_filter;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Obtener tickets agrupados por usuario
$stmt = $pdo->prepare("
    SELECT st.*, u.username, u.email,
           (SELECT COUNT(*) FROM ticket_responses tr WHERE tr.ticket_id = st.id) as response_count,
           (SELECT message FROM ticket_responses tr WHERE tr.ticket_id = st.id ORDER BY created_at DESC LIMIT 1) as last_response,
           (SELECT created_at FROM ticket_responses tr WHERE tr.ticket_id = st.id ORDER BY created_at DESC LIMIT 1) as last_response_date
    FROM support_tickets st
    LEFT JOIN users u ON st.user_id = u.id
    $where_clause
    ORDER BY u.username ASC, st.created_at DESC
");
$stmt->execute($params);
$all_tickets = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Agrupar tickets por usuario
$tickets_by_user = [];
foreach ($all_tickets as $ticket) {
    $username = $ticket['username'] ?? 'Usuario Desconocido';
    if (!isset($tickets_by_user[$username])) {
        $tickets_by_user[$username] = [
            'user_info' => [
                'username' => $username,
                'email' => $ticket['email'] ?? 'No disponible',
                'user_id' => $ticket['user_id']
            ],
            'tickets' => [],
            'stats' => [
                'total' => 0,
                'open' => 0,
                'in_progress' => 0,
                'resolved' => 0,
                'urgent' => 0
            ]
        ];
    }

    $tickets_by_user[$username]['tickets'][] = $ticket;
    $tickets_by_user[$username]['stats']['total']++;

    // Contar por estado
    if ($ticket['status'] == 'open') $tickets_by_user[$username]['stats']['open']++;
    if ($ticket['status'] == 'in_progress') $tickets_by_user[$username]['stats']['in_progress']++;
    if ($ticket['status'] == 'resolved') $tickets_by_user[$username]['stats']['resolved']++;
    if ($ticket['priority'] == 'urgent') $tickets_by_user[$username]['stats']['urgent']++;
}

// Obtener estadísticas
$stats_stmt = $pdo->query("
    SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN status = 'open' THEN 1 ELSE 0 END) as open,
        SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as in_progress,
        SUM(CASE WHEN status = 'resolved' THEN 1 ELSE 0 END) as resolved,
        SUM(CASE WHEN priority = 'urgent' THEN 1 ELSE 0 END) as urgent
    FROM support_tickets
");
$stats = $stats_stmt->fetch(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎫 Gestión de Tickets - Admin Soporte</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            /* Paleta de colores profesional con verde como principal */
            --primary-color: #10b981;
            --primary-dark: #059669;
            --primary-light: #34d399;
            --secondary-color: #1f2937;
            --dark-bg: #111827;
            --darker-bg: #030712;
            --surface: #374151;
            --surface-light: #4b5563;
            --surface-elevated: #6b7280;

            /* Colores de texto profesionales */
            --text-primary: #f9fafb;
            --text-secondary: #d1d5db;
            --text-muted: #9ca3af;
            --text-accent: #34d399;

            /* Colores de estado para tickets */
            --accent-color: #10b981;
            --support-color: #10b981;
            --warning-color: #d97706;
            --error-color: #dc2626;
            --success-color: #16a34a;
            --info-color: #2563eb;
            --urgent-color: #dc2626;

            /* Gradientes corporativos 3D con verde */
            --gradient-primary: linear-gradient(135deg, #10b981 0%, #059669 50%, #047857 100%);
            --gradient-secondary: linear-gradient(135deg, #34d399 0%, #10b981 50%, #059669 100%);
            --gradient-support: linear-gradient(135deg, #10b981 0%, #059669 50%, #047857 100%);
            --gradient-surface: linear-gradient(135deg, #1f2937 0%, #374151 50%, #4b5563 100%);
            --gradient-hero: linear-gradient(135deg, #030712 0%, #111827 25%, #1f2937 50%, #374151 100%);
            --gradient-card: linear-gradient(145deg, #1f2937 0%, #374151 100%);
            --gradient-elevated: linear-gradient(145deg, #374151 0%, #4b5563 100%);

            /* Sombras 3D profesionales con verde */
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            --shadow-3d: 0 8px 16px rgba(0, 0, 0, 0.15), 0 4px 8px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);
            --shadow-elevated: 0 12px 24px rgba(0, 0, 0, 0.2), 0 6px 12px rgba(0, 0, 0, 0.15);
            --shadow-glow: 0 0 20px rgba(16, 185, 129, 0.4);
            --shadow-support: 0 0 20px rgba(16, 185, 129, 0.3);
            --shadow-admin: 0 0 20px rgba(16, 185, 129, 0.3);

            /* Espaciado consistente */
            --space-xs: 0.25rem;
            --space-sm: 0.5rem;
            --space-md: 1rem;
            --space-lg: 1.5rem;
            --space-xl: 2rem;

            /* Border radius moderno */
            --radius-sm: 6px;
            --radius-md: 8px;
            --radius-lg: 12px;
            --radius-xl: 16px;
            --radius-full: 9999px;

            /* Transiciones y efectos 3D */
            --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-bounce: 400ms cubic-bezier(0.68, -0.55, 0.265, 1.55);

            /* Transformaciones 3D */
            --transform-hover: translateY(-4px) scale(1.02);
            --transform-active: translateY(-2px) scale(0.98);
            --transform-3d: perspective(1000px) rotateX(2deg) rotateY(-2deg);
            --transform-card: perspective(1000px) rotateX(0deg) rotateY(0deg);

            /* Filtros profesionales */
            --blur-glass: blur(16px) saturate(180%);
            --blur-backdrop: blur(12px) saturate(150%);
            --brightness-hover: brightness(1.1);
            --contrast-elevated: contrast(1.05);

            /* Compatibilidad */
            --border-color: #374151;
            --border-radius: var(--radius-lg);
            --transition: var(--transition-normal);
            --gradient-bg: var(--gradient-hero);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gradient-hero);
            background-attachment: fixed;
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
            font-feature-settings: "kern" 1, "liga" 1;
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            position: relative;
            overflow-x: hidden;
        }

        /* Efectos de fondo 3D para tickets */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 15% 25%, rgba(220, 38, 38, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 85% 75%, rgba(30, 64, 175, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 50% 50%, rgba(5, 150, 105, 0.06) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        /* Header Administrativo 3D para Tickets */
        .header {
            background: rgba(31, 41, 55, 0.95);
            backdrop-filter: var(--blur-glass);
            padding: var(--space-lg) 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.12);
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: var(--shadow-elevated);
            transform: var(--transform-card);
            transition: var(--transition-normal);
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--gradient-primary);
            opacity: 0.8;
        }

        .header::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(16, 185, 129, 0.5), transparent);
        }

        .header-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.5rem;
            font-weight: 800;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-decoration: none;
            letter-spacing: -0.025em;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            position: relative;
        }

        .logo i {
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: ticketPulse 2s ease-in-out infinite;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
        }

        @keyframes ticketPulse {
            0%, 100% { transform: scale(1) rotateZ(0deg); }
            50% { transform: scale(1.1) rotateZ(5deg); }
        }

        .nav-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .nav-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 0.75rem;
            background: var(--gradient-surface);
            border: 1px solid rgba(255, 255, 255, 0.15);
            color: var(--text-primary);
            text-decoration: none;
            border-radius: var(--radius-lg);
            backdrop-filter: var(--blur-backdrop);
            transition: var(--transition-normal);
            font-weight: 500;
            font-size: 0.9rem;
            cursor: pointer;
            box-shadow: var(--shadow-3d);
            position: relative;
            overflow: hidden;
        }

        .nav-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
            opacity: 0;
            transition: var(--transition-fast);
        }

        .nav-btn:hover {
            background: var(--gradient-elevated);
            border-color: var(--primary-color);
            transform: var(--transform-hover);
            box-shadow: var(--shadow-elevated);
            filter: var(--brightness-hover);
        }

        .nav-btn:hover::before {
            opacity: 1;
        }

        .nav-btn.back-btn {
            background: var(--gradient-primary);
            border-color: var(--primary-color);
            box-shadow: var(--shadow-3d), var(--shadow-glow);
            color: white;
        }

        .nav-btn.back-btn:hover {
            background: var(--gradient-primary);
            transform: var(--transform-hover) scale(1.05);
            box-shadow: var(--shadow-elevated), var(--shadow-glow);
            filter: var(--brightness-hover);
        }

        .main-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem 1.5rem;
        }

        .page-header {
            margin-bottom: var(--space-xl);
        }

        .page-header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: var(--space-lg);
            flex-wrap: wrap;
        }

        .page-actions {
            display: flex;
            align-items: center;
            gap: var(--space-md);
            flex-wrap: wrap;
        }

        .control-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 0.75rem;
            background: var(--gradient-surface);
            border: 1px solid rgba(255, 255, 255, 0.15);
            color: var(--text-primary);
            border-radius: var(--radius-lg);
            backdrop-filter: var(--blur-backdrop);
            transition: var(--transition-normal);
            font-weight: 500;
            font-size: 0.85rem;
            cursor: pointer;
            box-shadow: var(--shadow-sm);
        }

        .control-btn:hover {
            background: var(--gradient-elevated);
            border-color: var(--primary-color);
            transform: var(--transform-hover);
            box-shadow: var(--shadow-md);
        }

        .keyboard-hint {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 0.75rem;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: var(--radius-lg);
            color: var(--text-muted);
            font-size: 0.8rem;
        }

        @media (max-width: 768px) {
            .page-header-content {
                flex-direction: column;
                align-items: flex-start;
            }

            .page-actions {
                width: 100%;
                justify-content: flex-start;
            }
        }

        .page-title {
            font-size: 1.8rem;
            font-weight: 800;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            letter-spacing: -0.025em;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--gradient-card);
            border: 1px solid rgba(255, 255, 255, 0.12);
            border-radius: var(--radius-xl);
            backdrop-filter: var(--blur-backdrop);
            padding: var(--space-lg);
            transition: var(--transition-normal);
            position: relative;
            overflow: hidden;
            cursor: pointer;
            box-shadow: var(--shadow-3d);
            transform: var(--transform-card);
            text-align: center;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--gradient-support);
            transform: scaleX(0);
            transition: var(--transition-normal);
            border-radius: var(--radius-xl) var(--radius-xl) 0 0;
        }

        .stat-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%, rgba(255, 255, 255, 0.05) 100%);
            opacity: 0;
            transition: var(--transition-normal);
            pointer-events: none;
        }

        .stat-card:hover {
            background: var(--gradient-elevated);
            border-color: var(--support-color);
            transform: var(--transform-hover) var(--transform-3d);
            box-shadow: var(--shadow-2xl), var(--shadow-support);
            filter: var(--brightness-hover);
        }

        .stat-card:hover::before {
            transform: scaleX(1);
        }

        .stat-card:hover::after {
            opacity: 1;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 800;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            font-variant-numeric: tabular-nums;
            transition: var(--transition-normal);
        }

        .stat-card:hover .stat-number {
            color: var(--text-accent);
            text-shadow: 0 0 10px rgba(96, 165, 250, 0.5);
            transform: scale(1.05);
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.8rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            transition: var(--transition-normal);
        }

        .stat-card:hover .stat-label {
            color: var(--text-primary);
        }

        .filters {
            background: var(--gradient-card);
            border: 1px solid rgba(255, 255, 255, 0.12);
            border-radius: var(--radius-xl);
            backdrop-filter: var(--blur-backdrop);
            padding: var(--space-lg);
            margin-bottom: var(--space-xl);
            box-shadow: var(--shadow-3d);
            position: relative;
            overflow: hidden;
        }

        .filters::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--gradient-primary);
            opacity: 0.6;
        }

        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .filter-label {
            font-weight: 500;
            color: var(--text-primary);
        }

        .filter-select {
            padding: 0.75rem;
            background: var(--gradient-surface);
            color: var(--text-primary);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: var(--radius-lg);
            font-size: 0.9rem;
            backdrop-filter: var(--blur-backdrop);
            transition: var(--transition-normal);
            box-shadow: var(--shadow-sm);
        }

        .filter-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: var(--shadow-md), var(--shadow-glow);
            transform: translateY(-2px);
        }

        /* ===== ESTRUCTURA DE USUARIOS ===== */

        .empty-state {
            background: var(--gradient-card);
            border: 1px solid rgba(255, 255, 255, 0.12);
            border-radius: var(--radius-xl);
            backdrop-filter: var(--blur-backdrop);
            padding: var(--space-2xl);
            text-align: center;
            box-shadow: var(--shadow-3d);
        }

        .empty-icon {
            font-size: 4rem;
            color: var(--text-muted);
            margin-bottom: var(--space-lg);
            opacity: 0.5;
        }

        .user-section {
            background: var(--gradient-card);
            border: 1px solid rgba(255, 255, 255, 0.12);
            border-radius: var(--radius-xl);
            backdrop-filter: var(--blur-backdrop);
            margin-bottom: var(--space-xl);
            box-shadow: var(--shadow-3d);
            overflow: hidden;
            transition: var(--transition-normal);
        }

        .user-section:hover {
            border-color: var(--primary-color);
            box-shadow: var(--shadow-elevated), var(--shadow-glow);
        }

        .user-header {
            padding: var(--space-lg);
            background: var(--gradient-elevated);
            border-bottom: 1px solid rgba(255, 255, 255, 0.08);
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: var(--space-lg);
            flex-wrap: wrap;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: var(--space-md);
        }

        .user-avatar {
            width: 48px;
            height: 48px;
            background: var(--gradient-support);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            box-shadow: var(--shadow-3d);
            border: 2px solid rgba(255, 255, 255, 0.2);
        }

        .user-details h3 {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--space-xs);
        }

        .user-details p {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .user-stats {
            display: flex;
            gap: var(--space-md);
            flex-wrap: wrap;
        }

        .user-stat {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: var(--space-sm) var(--space-md);
            background: rgba(255, 255, 255, 0.05);
            border-radius: var(--radius-lg);
            border: 1px solid rgba(255, 255, 255, 0.1);
            min-width: 60px;
            transition: var(--transition-normal);
        }

        .user-stat:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        .user-stat .stat-value {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--text-primary);
            line-height: 1;
        }

        .user-stat .stat-label {
            font-size: 0.7rem;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-top: var(--space-xs);
        }

        .user-stat.urgent .stat-value { color: var(--urgent-color); }
        .user-stat.open .stat-value { color: var(--info-color); }
        .user-stat.progress .stat-value { color: var(--warning-color); }

        .toggle-user-tickets {
            display: flex;
            align-items: center;
            gap: var(--space-sm);
            padding: var(--space-sm) var(--space-md);
            background: var(--gradient-primary);
            border: none;
            border-radius: var(--radius-lg);
            color: white;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition-normal);
            box-shadow: var(--shadow-sm);
        }

        .toggle-user-tickets:hover {
            background: var(--gradient-primary);
            transform: var(--transform-hover);
            box-shadow: var(--shadow-md);
        }

        .toggle-user-tickets i {
            transition: var(--transition-normal);
        }

        .toggle-user-tickets.expanded i {
            transform: rotate(180deg);
        }

        .user-tickets {
            display: none;
            animation: slideDown 0.3s ease;
        }

        .user-tickets.expanded {
            display: block;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .ticket-item {
            padding: var(--space-lg);
            border-bottom: 1px solid rgba(255, 255, 255, 0.08);
            transition: var(--transition-normal);
            position: relative;
            background: rgba(255, 255, 255, 0.02);
        }

        .ticket-item:hover {
            background: rgba(255, 255, 255, 0.05);
            transform: translateX(4px);
        }

        .ticket-item:last-child {
            border-bottom: none;
        }

        .ticket-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background: var(--gradient-support);
            opacity: 0;
            transition: var(--transition-normal);
        }

        .ticket-item:hover::before {
            opacity: 1;
        }

        .ticket-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .ticket-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .ticket-meta {
            display: flex;
            gap: 1rem;
            color: var(--text-secondary);
            font-size: 0.9rem;
            flex-wrap: wrap;
        }

        .status-badge, .priority-badge {
            padding: 0.25rem 0.75rem;
            border-radius: var(--radius-full);
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            border: 1px solid;
            backdrop-filter: blur(8px);
            box-shadow: var(--shadow-sm);
            transition: var(--transition-normal);
        }

        .status-badge:hover, .priority-badge:hover {
            transform: scale(1.05);
            box-shadow: var(--shadow-md);
        }

        /* Estados de tickets */
        .status-open {
            background: rgba(59, 130, 246, 0.15);
            color: #60a5fa;
            border-color: rgba(59, 130, 246, 0.3);
        }
        .status-in_progress {
            background: rgba(245, 158, 11, 0.15);
            color: #fbbf24;
            border-color: rgba(245, 158, 11, 0.3);
        }
        .status-waiting_user {
            background: rgba(168, 85, 247, 0.15);
            color: #c084fc;
            border-color: rgba(168, 85, 247, 0.3);
        }
        .status-resolved {
            background: rgba(16, 185, 129, 0.15);
            color: #34d399;
            border-color: rgba(16, 185, 129, 0.3);
        }
        .status-closed {
            background: rgba(107, 114, 128, 0.15);
            color: #9ca3af;
            border-color: rgba(107, 114, 128, 0.3);
        }

        /* Prioridades */
        .priority-low {
            background: rgba(107, 114, 128, 0.15);
            color: #9ca3af;
            border-color: rgba(107, 114, 128, 0.3);
        }
        .priority-medium {
            background: rgba(59, 130, 246, 0.15);
            color: #60a5fa;
            border-color: rgba(59, 130, 246, 0.3);
        }
        .priority-high {
            background: rgba(245, 158, 11, 0.15);
            color: #fbbf24;
            border-color: rgba(245, 158, 11, 0.3);
        }
        .priority-urgent {
            background: rgba(239, 68, 68, 0.15);
            color: #f87171;
            border-color: rgba(239, 68, 68, 0.3);
            animation: urgentPulse 2s ease-in-out infinite;
        }

        @keyframes urgentPulse {
            0%, 100% { box-shadow: var(--shadow-sm); }
            50% { box-shadow: var(--shadow-md), 0 0 15px rgba(239, 68, 68, 0.4); }
        }

        .ticket-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: 1px solid;
            border-radius: var(--radius-lg);
            cursor: pointer;
            font-size: 0.85rem;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: var(--transition-normal);
            backdrop-filter: var(--blur-backdrop);
            box-shadow: var(--shadow-sm);
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
            opacity: 0;
            transition: var(--transition-fast);
        }

        .btn:hover::before {
            opacity: 1;
        }

        .btn-primary {
            background: var(--gradient-primary);
            border-color: var(--primary-color);
            color: white;
        }

        .btn-success {
            background: var(--gradient-secondary);
            border-color: var(--success-color);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
            border-color: var(--warning-color);
            color: white;
        }

        .btn:hover {
            transform: var(--transform-hover);
            box-shadow: var(--shadow-md);
            filter: var(--brightness-hover);
        }

        .btn:active {
            transform: var(--transform-active);
        }

        .success-message {
            background: rgba(16, 185, 129, 0.2);
            color: var(--success-color);
            padding: 1rem;
            border-radius: var(--border-radius);
            margin-bottom: 1rem;
            border: 1px solid rgba(16, 185, 129, 0.3);
        }

        @media (max-width: 768px) {
            .ticket-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .ticket-actions {
                width: 100%;
                justify-content: flex-start;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-container">
            <a href="tickets_admin.php" class="logo">
                <i class="fas fa-ticket-alt"></i>
                <span>Gestión de Tickets</span>
            </a>
            
            <div class="nav-buttons">
                <a href="admin2.php" class="nav-btn back-btn">
                    <i class="fas fa-arrow-left"></i>
                    <span>Volver</span>
                </a>
                <a href="admin.php" class="nav-btn">
                    <i class="fas fa-cog"></i>
                    <span>Admin</span>
                </a>
            </div>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <div class="page-header-content">
                <h1 class="page-title">
                    <i class="fas fa-ticket-alt"></i>
                    Gestión de Tickets de Soporte
                </h1>

                <div class="page-actions">
                    <button class="control-btn" onclick="toggleAllUsers(true)" title="Expandir todos los usuarios">
                        <i class="fas fa-expand-alt"></i>
                        <span>Expandir Todos</span>
                    </button>
                    <button class="control-btn" onclick="toggleAllUsers(false)" title="Contraer todos los usuarios">
                        <i class="fas fa-compress-alt"></i>
                        <span>Contraer Todos</span>
                    </button>
                    <div class="keyboard-hint">
                        <i class="fas fa-keyboard"></i>
                        <span>Ctrl+E para alternar</span>
                    </div>
                </div>
            </div>
        </div>

        <?php if (isset($success_message)): ?>
        <div class="success-message">
            <i class="fas fa-check-circle"></i>
            <?php echo htmlspecialchars($success_message); ?>
        </div>
        <?php endif; ?>

        <!-- Estadísticas -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['total']; ?></div>
                <div class="stat-label">Total Tickets</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['open']; ?></div>
                <div class="stat-label">Abiertos</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['in_progress']; ?></div>
                <div class="stat-label">En Progreso</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['resolved']; ?></div>
                <div class="stat-label">Resueltos</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['urgent']; ?></div>
                <div class="stat-label">Urgentes</div>
            </div>
        </div>

        <!-- Filtros -->
        <div class="filters">
            <form method="GET" class="filters-grid">
                <div class="filter-group">
                    <label class="filter-label">Estado</label>
                    <select name="status" class="filter-select" onchange="this.form.submit()">
                        <option value="all" <?php echo $status_filter === 'all' ? 'selected' : ''; ?>>Todos</option>
                        <option value="open" <?php echo $status_filter === 'open' ? 'selected' : ''; ?>>Abiertos</option>
                        <option value="in_progress" <?php echo $status_filter === 'in_progress' ? 'selected' : ''; ?>>En Progreso</option>
                        <option value="resolved" <?php echo $status_filter === 'resolved' ? 'selected' : ''; ?>>Resueltos</option>
                        <option value="closed" <?php echo $status_filter === 'closed' ? 'selected' : ''; ?>>Cerrados</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label class="filter-label">Prioridad</label>
                    <select name="priority" class="filter-select" onchange="this.form.submit()">
                        <option value="all" <?php echo $priority_filter === 'all' ? 'selected' : ''; ?>>Todas</option>
                        <option value="urgent" <?php echo $priority_filter === 'urgent' ? 'selected' : ''; ?>>Urgente</option>
                        <option value="high" <?php echo $priority_filter === 'high' ? 'selected' : ''; ?>>Alta</option>
                        <option value="medium" <?php echo $priority_filter === 'medium' ? 'selected' : ''; ?>>Media</option>
                        <option value="low" <?php echo $priority_filter === 'low' ? 'selected' : ''; ?>>Baja</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label class="filter-label">Categoría</label>
                    <select name="category" class="filter-select" onchange="this.form.submit()">
                        <option value="all" <?php echo $category_filter === 'all' ? 'selected' : ''; ?>>Todas</option>
                        <option value="technical" <?php echo $category_filter === 'technical' ? 'selected' : ''; ?>>Técnico</option>
                        <option value="billing" <?php echo $category_filter === 'billing' ? 'selected' : ''; ?>>Facturación</option>
                        <option value="general" <?php echo $category_filter === 'general' ? 'selected' : ''; ?>>General</option>
                        <option value="bug_report" <?php echo $category_filter === 'bug_report' ? 'selected' : ''; ?>>Reporte de Bug</option>
                    </select>
                </div>
            </form>
        </div>

        <!-- Tickets Agrupados por Usuario -->
        <?php if (empty($tickets_by_user)): ?>
        <div class="empty-state">
            <div class="empty-icon">
                <i class="fas fa-inbox"></i>
            </div>
            <h3>No hay tickets disponibles</h3>
            <p>No hay tickets que coincidan con los filtros seleccionados</p>
        </div>
        <?php else: ?>
            <?php foreach ($tickets_by_user as $username => $user_data): ?>
            <!-- Sección de Usuario -->
            <div class="user-section" id="user-<?php echo $user_data['user_info']['user_id']; ?>">
                <div class="user-header">
                    <div class="user-info">
                        <div class="user-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="user-details">
                            <h3 class="user-name"><?php echo htmlspecialchars($username); ?></h3>
                            <p class="user-email"><?php echo htmlspecialchars($user_data['user_info']['email']); ?></p>
                        </div>
                    </div>

                    <div class="user-stats">
                        <div class="user-stat">
                            <span class="stat-value"><?php echo $user_data['stats']['total']; ?></span>
                            <span class="stat-label">Total</span>
                        </div>
                        <div class="user-stat urgent">
                            <span class="stat-value"><?php echo $user_data['stats']['urgent']; ?></span>
                            <span class="stat-label">Urgentes</span>
                        </div>
                        <div class="user-stat open">
                            <span class="stat-value"><?php echo $user_data['stats']['open']; ?></span>
                            <span class="stat-label">Abiertos</span>
                        </div>
                        <div class="user-stat progress">
                            <span class="stat-value"><?php echo $user_data['stats']['in_progress']; ?></span>
                            <span class="stat-label">En Progreso</span>
                        </div>
                    </div>

                    <div class="user-actions">
                        <button class="toggle-user-tickets" onclick="toggleUserTickets('user-<?php echo $user_data['user_info']['user_id']; ?>')">
                            <i class="fas fa-chevron-down"></i>
                            <span>Ver Tickets</span>
                        </button>
                    </div>
                </div>

                <div class="user-tickets" id="tickets-user-<?php echo $user_data['user_info']['user_id']; ?>">
                    <?php foreach ($user_data['tickets'] as $ticket): ?>
                <div class="ticket-item">
                    <div class="ticket-header">
                        <div>
                            <div class="ticket-title">
                                #<?php echo $ticket['id']; ?> - <?php echo htmlspecialchars($ticket['title']); ?>
                            </div>
                            <div class="ticket-meta">
                                <span><i class="fas fa-user"></i> <?php echo htmlspecialchars($ticket['username'] ?? 'Usuario desconocido'); ?></span>
                                <span><i class="fas fa-calendar"></i> <?php echo date('d/m/Y H:i', strtotime($ticket['created_at'])); ?></span>
                                <span><i class="fas fa-comments"></i> <?php echo $ticket['response_count']; ?> respuestas</span>
                            </div>
                        </div>
                        <div class="ticket-actions">
                            <span class="status-badge status-<?php echo $ticket['status']; ?>">
                                <?php
                                $status_labels = [
                                    'open' => 'Abierto',
                                    'in_progress' => 'En Progreso',
                                    'waiting_user' => 'Esperando Usuario',
                                    'resolved' => 'Resuelto',
                                    'closed' => 'Cerrado'
                                ];
                                echo $status_labels[$ticket['status']] ?? $ticket['status'];
                                ?>
                            </span>
                            <span class="priority-badge priority-<?php echo $ticket['priority']; ?>">
                                <?php
                                $priority_labels = [
                                    'low' => 'Baja',
                                    'medium' => 'Media',
                                    'high' => 'Alta',
                                    'urgent' => 'Urgente'
                                ];
                                echo $priority_labels[$ticket['priority']] ?? $ticket['priority'];
                                ?>
                            </span>
                        </div>
                    </div>

                    <div style="margin-bottom: 1rem; color: var(--text-secondary);">
                        <strong>Categoría:</strong> <?php echo ucfirst($ticket['category']); ?>
                    </div>

                    <div style="margin-bottom: 1rem; color: var(--text-secondary);">
                        <?php echo nl2br(htmlspecialchars(substr($ticket['description'], 0, 200))); ?>
                        <?php if (strlen($ticket['description']) > 200): ?>...<?php endif; ?>
                    </div>

                    <?php if ($ticket['last_response']): ?>
                    <div style="margin-bottom: 1rem; padding: 1rem; background: rgba(255,255,255,0.05); border-radius: 8px;">
                        <strong>Última respuesta:</strong><br>
                        <?php echo nl2br(htmlspecialchars(substr($ticket['last_response'], 0, 150))); ?>
                        <?php if (strlen($ticket['last_response']) > 150): ?>...<?php endif; ?>
                    </div>
                    <?php endif; ?>

                    <div class="ticket-actions">
                        <a href="ticket_detail.php?id=<?php echo $ticket['id']; ?>" class="btn btn-primary">
                            <i class="fas fa-eye"></i>
                            Ver Detalles
                        </a>

                        <?php if ($ticket['status'] !== 'resolved' && $ticket['status'] !== 'closed'): ?>
                        <form method="POST" style="display: inline;">
                            <input type="hidden" name="action" value="update_status">
                            <input type="hidden" name="ticket_id" value="<?php echo $ticket['id']; ?>">
                            <input type="hidden" name="status" value="in_progress">
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-play"></i>
                                En Progreso
                            </button>
                        </form>

                        <form method="POST" style="display: inline;">
                            <input type="hidden" name="action" value="update_status">
                            <input type="hidden" name="ticket_id" value="<?php echo $ticket['id']; ?>">
                            <input type="hidden" name="status" value="resolved">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-check"></i>
                                Resolver
                            </button>
                        </form>
                        <?php endif; ?>
                    </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </main>

    <script>
        // ===== FUNCIONALIDADES MODERNAS =====

        // Inicializar funcionalidades
        document.addEventListener('DOMContentLoaded', function() {
            initModernFeatures();
        });

        function initModernFeatures() {
            initScrollEffects();
            initKeyboardNavigation();
            initAnimationsOnScroll();
            initAutoRefresh();
            initFormValidation();
        }

        // ===== GESTIÓN DE USUARIOS =====

        function toggleUserTickets(userId) {
            const ticketsContainer = document.getElementById('tickets-' + userId);
            const toggleButton = document.querySelector(`#${userId} .toggle-user-tickets`);

            if (ticketsContainer.classList.contains('expanded')) {
                ticketsContainer.classList.remove('expanded');
                toggleButton.classList.remove('expanded');
                toggleButton.querySelector('span').textContent = 'Ver Tickets';
            } else {
                ticketsContainer.classList.add('expanded');
                toggleButton.classList.add('expanded');
                toggleButton.querySelector('span').textContent = 'Ocultar Tickets';
            }
        }

        // Expandir/contraer todos los usuarios
        function toggleAllUsers(expand = null) {
            const userSections = document.querySelectorAll('.user-section');

            userSections.forEach(section => {
                const userId = section.id;
                const ticketsContainer = document.getElementById('tickets-' + userId);
                const toggleButton = section.querySelector('.toggle-user-tickets');

                if (expand === true || (expand === null && !ticketsContainer.classList.contains('expanded'))) {
                    ticketsContainer.classList.add('expanded');
                    toggleButton.classList.add('expanded');
                    toggleButton.querySelector('span').textContent = 'Ocultar Tickets';
                } else {
                    ticketsContainer.classList.remove('expanded');
                    toggleButton.classList.remove('expanded');
                    toggleButton.querySelector('span').textContent = 'Ver Tickets';
                }
            });
        }

        // ===== AUTO-REFRESH INTELIGENTE =====

        function initAutoRefresh() {
            setInterval(function() {
                if (document.hidden) return;

                const activeElement = document.activeElement;
                if (activeElement.tagName !== 'INPUT' && activeElement.tagName !== 'TEXTAREA') {
                    // Mostrar indicador de actualización
                    showToast('Actualizando datos...', 'info');
                    setTimeout(() => location.reload(), 1000);
                }
            }, 60000); // Aumentado a 60 segundos para mejor UX
        }

        // ===== VALIDACIÓN DE FORMULARIOS =====

        function initFormValidation() {
            document.querySelectorAll('form').forEach(form => {
                form.addEventListener('submit', function(e) {
                    const action = this.querySelector('input[name="action"]')?.value;
                    if (action === 'update_status') {
                        const status = this.querySelector('input[name="status"]')?.value;
                        if (status === 'resolved' || status === 'closed') {
                            if (!confirm('¿Estás seguro de que quieres cambiar el estado de este ticket?')) {
                                e.preventDefault();
                            }
                        }
                    }
                });
            });
        }

        // ===== EFECTOS VISUALES =====

        function initScrollEffects() {
            window.addEventListener('scroll', function() {
                // Efecto parallax sutil en el header
                const header = document.querySelector('.header');
                if (header) {
                    const scrolled = window.pageYOffset;
                    header.style.transform = `translateY(${scrolled * 0.1}px)`;
                }
            });
        }

        function initKeyboardNavigation() {
            document.addEventListener('keydown', function(e) {
                // Ctrl/Cmd + E para expandir/contraer todos
                if ((e.ctrlKey || e.metaKey) && e.key === 'e') {
                    e.preventDefault();
                    toggleAllUsers();
                }

                // Esc para contraer todos
                if (e.key === 'Escape') {
                    toggleAllUsers(false);
                }

                // F1 para ayuda
                if (e.key === 'F1') {
                    e.preventDefault();
                    showKeyboardHelp();
                }
            });
        }

        function initAnimationsOnScroll() {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            });

            // Observar secciones de usuario
            document.querySelectorAll('.user-section').forEach(el => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(30px)';
                el.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
                observer.observe(el);
            });
        }

        // ===== SISTEMA DE TOAST =====

        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.innerHTML = `
                <div class="toast-content">
                    <i class="fas fa-${getToastIcon(type)}"></i>
                    <span>${message}</span>
                </div>
            `;

            document.body.appendChild(toast);

            setTimeout(() => toast.classList.add('show'), 100);

            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => toast.remove(), 300);
            }, 3000);
        }

        function getToastIcon(type) {
            const icons = {
                'success': 'check-circle',
                'error': 'exclamation-circle',
                'warning': 'exclamation-triangle',
                'info': 'info-circle'
            };
            return icons[type] || 'info-circle';
        }

        function showKeyboardHelp() {
            showToast('Atajos: Ctrl+E (Expandir/Contraer), Esc (Contraer todos), F1 (Ayuda)', 'info');
        }
    </script>

    <!-- Estilos para Toast -->
    <style>
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--gradient-card);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: var(--radius-lg);
            padding: 1rem;
            box-shadow: var(--shadow-xl);
            z-index: 10000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            backdrop-filter: var(--blur-glass);
            max-width: 400px;
        }

        .toast.show {
            transform: translateX(0);
        }

        .toast-content {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--text-primary);
        }

        .toast-success { border-left: 3px solid var(--success-color); }
        .toast-error { border-left: 3px solid var(--error-color); }
        .toast-warning { border-left: 3px solid var(--warning-color); }
        .toast-info { border-left: 3px solid var(--info-color); }
    </style>
</body>
</html>
