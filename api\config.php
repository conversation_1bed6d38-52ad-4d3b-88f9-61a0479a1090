<?php
/**
 * Configuración de la API RGS TOOL
 * Configuración centralizada para la API REST
 */

// Configuración de la base de datos
define('DB_HOST', 'localhost');
define('DB_NAME', 'u170528143_php');
define('DB_USER', 'u170528143_php');
define('DB_PASS', '&T4v!$=i');

// Configuración de la API
define('API_VERSION', '1.0');
define('API_NAME', 'RGS TOOL API');

// Configuración de seguridad
define('API_KEY_REQUIRED', false); // Cambiar a true en producción
define('RATE_LIMIT_ENABLED', false); // Cambiar a true en producción
define('RATE_LIMIT_REQUESTS', 100); // Requests por minuto
define('RATE_LIMIT_WINDOW', 60); // Ventana en segundos

// Configuración CORS
define('CORS_ALLOWED_ORIGINS', '*'); // En producción especificar dominios
define('CORS_ALLOWED_METHODS', 'GET, POST, PUT, DELETE, OPTIONS');
define('CORS_ALLOWED_HEADERS', 'Content-Type, Authorization, X-Requested-With, X-API-Key');

// Configuración de respuestas
define('DEFAULT_TIMEZONE', 'America/Panama');
define('DATE_FORMAT', 'Y-m-d H:i:s');

// Configuración de logs
define('LOG_ENABLED', true);
define('LOG_LEVEL', 'INFO'); // DEBUG, INFO, WARNING, ERROR
define('LOG_FILE', __DIR__ . '/logs/api.log');

// Configuración de cache
define('CACHE_ENABLED', false);
define('CACHE_TTL', 300); // 5 minutos

// Configuración de paginación
define('DEFAULT_PAGE_SIZE', 20);
define('MAX_PAGE_SIZE', 100);

// Configuración de servicios externos
define('TMDB_CACHE_TTL', 3600); // 1 hora
define('GEOLOCATION_CACHE_TTL', 86400); // 24 horas

// Estados de pedidos válidos
define('VALID_ORDER_STATUSES', [
    'Recibido',
    'Pendiente',
    'En Cola',
    'Procesando',
    'Ya en existencia en la plataforma',
    'Listo',
    'No disponible'
]);

// Tipos de media válidos
define('VALID_MEDIA_TYPES', [
    'movie',
    'tv',
    'person'
]);

// Configuración de errores
define('ERROR_CODES', [
    'INVALID_ENDPOINT' => 1001,
    'METHOD_NOT_ALLOWED' => 1002,
    'MISSING_PARAMETER' => 1003,
    'INVALID_PARAMETER' => 1004,
    'AUTHENTICATION_FAILED' => 1005,
    'AUTHORIZATION_FAILED' => 1006,
    'RESOURCE_NOT_FOUND' => 1007,
    'DATABASE_ERROR' => 1008,
    'EXTERNAL_API_ERROR' => 1009,
    'RATE_LIMIT_EXCEEDED' => 1010,
    'VALIDATION_ERROR' => 1011,
    'INTERNAL_ERROR' => 1012
]);

// Configuración de timezone
date_default_timezone_set(DEFAULT_TIMEZONE);

// Función para obtener configuración de base de datos
function getDbConfig() {
    return [
        'host' => DB_HOST,
        'name' => DB_NAME,
        'user' => DB_USER,
        'pass' => DB_PASS
    ];
}

// Función para obtener información de la API
function getApiInfo() {
    return [
        'name' => API_NAME,
        'version' => API_VERSION,
        'timezone' => DEFAULT_TIMEZONE,
        'timestamp' => date(DATE_FORMAT),
        'endpoints' => [
            'auth' => [
                'POST /api/auth/login' => 'Autenticación de usuario'
            ],
            'content' => [
                'GET /api/content/trending' => 'Contenido trending de TMDB',
                'GET /api/content/search?q={query}' => 'Búsqueda de contenido'
            ],
            'orders' => [
                'POST /api/orders/create' => 'Crear nuevo pedido',
                'GET /api/orders/user/{id}' => 'Obtener pedidos de usuario'
            ],
            'stats' => [
                'GET /api/stats' => 'Estadísticas generales del sistema'
            ],
            'user' => [
                'GET /api/user/profile/{id}' => 'Perfil de usuario'
            ]
        ]
    ];
}

// Función para logging
function apiLog($level, $message, $context = []) {
    if (!LOG_ENABLED) return;
    
    $levels = ['DEBUG' => 0, 'INFO' => 1, 'WARNING' => 2, 'ERROR' => 3];
    $current_level = $levels[LOG_LEVEL] ?? 1;
    $message_level = $levels[$level] ?? 1;
    
    if ($message_level < $current_level) return;
    
    $timestamp = date(DATE_FORMAT);
    $context_str = !empty($context) ? ' ' . json_encode($context) : '';
    $log_entry = "[$timestamp] [$level] $message$context_str" . PHP_EOL;
    
    // Crear directorio de logs si no existe
    $log_dir = dirname(LOG_FILE);
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    
    file_put_contents(LOG_FILE, $log_entry, FILE_APPEND | LOCK_EX);
}

// Función para validar parámetros
function validateParameter($value, $type, $required = true, $options = []) {
    if ($required && (is_null($value) || $value === '')) {
        return ['valid' => false, 'error' => 'Parameter is required'];
    }
    
    if (!$required && (is_null($value) || $value === '')) {
        return ['valid' => true, 'value' => null];
    }
    
    switch ($type) {
        case 'int':
            if (!is_numeric($value) || (int)$value != $value) {
                return ['valid' => false, 'error' => 'Parameter must be an integer'];
            }
            $value = (int)$value;
            if (isset($options['min']) && $value < $options['min']) {
                return ['valid' => false, 'error' => "Parameter must be >= {$options['min']}"];
            }
            if (isset($options['max']) && $value > $options['max']) {
                return ['valid' => false, 'error' => "Parameter must be <= {$options['max']}"];
            }
            break;
            
        case 'string':
            if (!is_string($value)) {
                return ['valid' => false, 'error' => 'Parameter must be a string'];
            }
            if (isset($options['min_length']) && strlen($value) < $options['min_length']) {
                return ['valid' => false, 'error' => "Parameter must be at least {$options['min_length']} characters"];
            }
            if (isset($options['max_length']) && strlen($value) > $options['max_length']) {
                return ['valid' => false, 'error' => "Parameter must be at most {$options['max_length']} characters"];
            }
            break;
            
        case 'email':
            if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
                return ['valid' => false, 'error' => 'Parameter must be a valid email'];
            }
            break;
            
        case 'enum':
            if (!in_array($value, $options['values'] ?? [])) {
                $valid_values = implode(', ', $options['values'] ?? []);
                return ['valid' => false, 'error' => "Parameter must be one of: $valid_values"];
            }
            break;
    }
    
    return ['valid' => true, 'value' => $value];
}

// Función para sanitizar entrada
function sanitizeInput($input) {
    if (is_array($input)) {
        return array_map('sanitizeInput', $input);
    }
    
    if (is_string($input)) {
        return trim(htmlspecialchars($input, ENT_QUOTES, 'UTF-8'));
    }
    
    return $input;
}

// Función para generar respuesta de error estándar
function generateErrorResponse($code, $message, $details = null) {
    $error_code = ERROR_CODES[$code] ?? 9999;
    
    $response = [
        'success' => false,
        'error' => [
            'code' => $error_code,
            'type' => $code,
            'message' => $message
        ],
        'timestamp' => date(DATE_FORMAT)
    ];
    
    if ($details !== null) {
        $response['error']['details'] = $details;
    }
    
    return $response;
}

// Función para generar respuesta de éxito estándar
function generateSuccessResponse($data, $message = null) {
    $response = [
        'success' => true,
        'data' => $data,
        'timestamp' => date(DATE_FORMAT)
    ];
    
    if ($message !== null) {
        $response['message'] = $message;
    }
    
    return $response;
}
?>
