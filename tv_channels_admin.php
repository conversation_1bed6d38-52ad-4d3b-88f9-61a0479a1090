<?php
session_start();

// Verificar si el usuario está logueado como admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

// Configuración MySQL
$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Error de conexión a la base de datos: " . $e->getMessage());
}

// Crear tabla para canales de TV si no existe
$pdo->exec("CREATE TABLE IF NOT EXISTS tv_channels (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    url TEXT NOT NULL,
    logo VARCHAR(500),
    category VARCHAR(100),
    country VARCHAR(50),
    language VARCHAR(50),
    status ENUM('unknown', 'online', 'offline') DEFAULT 'unknown',
    last_check DATETIME,
    response_time INT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_category (category),
    INDEX idx_status (status),
    INDEX idx_country (country)
)");

// Procesar acciones
$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'upload_m3u':
                if (isset($_FILES['m3u_file']) && $_FILES['m3u_file']['error'] === UPLOAD_ERR_OK) {
                    $content = file_get_contents($_FILES['m3u_file']['tmp_name']);
                    $channels = parseM3U($content);
                    
                    $inserted = 0;
                    foreach ($channels as $channel) {
                        try {
                            $stmt = $pdo->prepare("INSERT INTO tv_channels (name, url, logo, category, country, language) VALUES (?, ?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE url = VALUES(url), logo = VALUES(logo), category = VALUES(category), updated_at = NOW()");
                            $stmt->execute([
                                $channel['name'],
                                $channel['url'],
                                $channel['logo'] ?? '',
                                $channel['category'] ?? 'General',
                                $channel['country'] ?? '',
                                $channel['language'] ?? ''
                            ]);
                            $inserted++;
                        } catch (Exception $e) {
                            // Continuar con el siguiente canal si hay error
                        }
                    }
                    $message = "Lista M3U procesada correctamente. $inserted canales importados.";
                    $message_type = 'success';
                } else {
                    $message = "Error al subir el archivo M3U.";
                    $message_type = 'error';
                }
                break;
                
            case 'clear_channels':
                $stmt = $pdo->prepare("DELETE FROM tv_channels");
                $stmt->execute();
                $message = "Todos los canales han sido eliminados.";
                $message_type = 'success';
                break;
        }
    }
}

// Función para parsear M3U
function parseM3U($content) {
    $channels = [];
    $lines = explode("\n", $content);
    $current_channel = null;
    
    foreach ($lines as $line) {
        $line = trim($line);
        
        if (strpos($line, '#EXTINF:') === 0) {
            $current_channel = [];
            
            // Extraer nombre del canal
            if (preg_match('/,(.+)$/', $line, $matches)) {
                $current_channel['name'] = trim($matches[1]);
            }
            
            // Extraer logo
            if (preg_match('/tvg-logo="([^"]+)"/', $line, $matches)) {
                $current_channel['logo'] = $matches[1];
            }
            
            // Extraer categoría
            if (preg_match('/group-title="([^"]+)"/', $line, $matches)) {
                $current_channel['category'] = $matches[1];
            }
            
            // Extraer país
            if (preg_match('/tvg-country="([^"]+)"/', $line, $matches)) {
                $current_channel['country'] = $matches[1];
            }
            
            // Extraer idioma
            if (preg_match('/tvg-language="([^"]+)"/', $line, $matches)) {
                $current_channel['language'] = $matches[1];
            }
            
        } elseif (!empty($line) && !str_starts_with($line, '#') && $current_channel !== null) {
            $current_channel['url'] = $line;
            $channels[] = $current_channel;
            $current_channel = null;
        }
    }
    
    return $channels;
}

// Obtener filtros
$category_filter = $_GET['category'] ?? 'all';
$status_filter = $_GET['status'] ?? 'all';
$country_filter = $_GET['country'] ?? 'all';

// Construir consulta con filtros
$where_conditions = [];
$params = [];

if ($category_filter !== 'all') {
    $where_conditions[] = "category = ?";
    $params[] = $category_filter;
}

if ($status_filter !== 'all') {
    $where_conditions[] = "status = ?";
    $params[] = $status_filter;
}

if ($country_filter !== 'all') {
    $where_conditions[] = "country = ?";
    $params[] = $country_filter;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Obtener canales
$stmt = $pdo->prepare("SELECT * FROM tv_channels $where_clause ORDER BY category ASC, name ASC");
$stmt->execute($params);
$channels = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Agrupar canales por categoría
$channels_by_category = [];
foreach ($channels as $channel) {
    $category = $channel['category'] ?: 'Sin Categoría';
    if (!isset($channels_by_category[$category])) {
        $channels_by_category[$category] = [];
    }
    $channels_by_category[$category][] = $channel;
}

// Obtener estadísticas
$stats_stmt = $pdo->query("
    SELECT 
        COUNT(*) as total,
        COUNT(DISTINCT category) as categories,
        SUM(CASE WHEN status = 'online' THEN 1 ELSE 0 END) as online,
        SUM(CASE WHEN status = 'offline' THEN 1 ELSE 0 END) as offline,
        SUM(CASE WHEN status = 'unknown' THEN 1 ELSE 0 END) as unknown
    FROM tv_channels
");
$stats = $stats_stmt->fetch(PDO::FETCH_ASSOC);

// Obtener categorías para filtro
$categories_stmt = $pdo->query("SELECT DISTINCT category FROM tv_channels WHERE category IS NOT NULL AND category != '' ORDER BY category");
$categories = $categories_stmt->fetchAll(PDO::FETCH_COLUMN);

// Obtener países para filtro
$countries_stmt = $pdo->query("SELECT DISTINCT country FROM tv_channels WHERE country IS NOT NULL AND country != '' ORDER BY country");
$countries = $countries_stmt->fetchAll(PDO::FETCH_COLUMN);
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📺 Gestión de Canales TV - Admin</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            /* Paleta de colores profesional con verde como principal */
            --primary-color: #10b981;
            --primary-dark: #059669;
            --primary-light: #34d399;
            --secondary-color: #1f2937;
            --dark-bg: #111827;
            --darker-bg: #030712;
            --surface: #374151;
            --surface-light: #4b5563;
            --surface-elevated: #6b7280;

            /* Colores de texto profesionales */
            --text-primary: #f9fafb;
            --text-secondary: #d1d5db;
            --text-muted: #9ca3af;
            --text-accent: #34d399;

            /* Colores de estado para canales */
            --accent-color: #10b981;
            --warning-color: #d97706;
            --error-color: #dc2626;
            --success-color: #16a34a;
            --info-color: #2563eb;
            --online-color: #16a34a;
            --offline-color: #dc2626;
            --unknown-color: #6b7280;

            /* Gradientes corporativos 3D con verde */
            --gradient-primary: linear-gradient(135deg, #10b981 0%, #059669 50%, #047857 100%);
            --gradient-secondary: linear-gradient(135deg, #34d399 0%, #10b981 50%, #059669 100%);
            --gradient-surface: linear-gradient(135deg, #1f2937 0%, #374151 50%, #4b5563 100%);
            --gradient-hero: linear-gradient(135deg, #030712 0%, #111827 25%, #1f2937 50%, #374151 100%);
            --gradient-card: linear-gradient(145deg, #1f2937 0%, #374151 100%);
            --gradient-elevated: linear-gradient(145deg, #374151 0%, #4b5563 100%);

            /* Sombras 3D profesionales */
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            --shadow-3d: 0 8px 16px rgba(0, 0, 0, 0.15), 0 4px 8px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);
            --shadow-elevated: 0 12px 24px rgba(0, 0, 0, 0.2), 0 6px 12px rgba(0, 0, 0, 0.15);
            --shadow-glow: 0 0 20px rgba(16, 185, 129, 0.4);
            --shadow-admin: 0 0 20px rgba(16, 185, 129, 0.3);

            /* Espaciado consistente */
            --space-xs: 0.25rem;
            --space-sm: 0.5rem;
            --space-md: 1rem;
            --space-lg: 1.5rem;
            --space-xl: 2rem;
            --space-2xl: 3rem;

            /* Border radius moderno */
            --radius-sm: 6px;
            --radius-md: 8px;
            --radius-lg: 12px;
            --radius-xl: 16px;
            --radius-full: 9999px;

            /* Transiciones y efectos 3D */
            --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-bounce: 400ms cubic-bezier(0.68, -0.55, 0.265, 1.55);

            /* Transformaciones 3D */
            --transform-hover: translateY(-4px) scale(1.02);
            --transform-active: translateY(-2px) scale(0.98);
            --transform-3d: perspective(1000px) rotateX(2deg) rotateY(-2deg);
            --transform-card: perspective(1000px) rotateX(0deg) rotateY(0deg);

            /* Filtros profesionales */
            --blur-glass: blur(16px) saturate(180%);
            --blur-backdrop: blur(12px) saturate(150%);
            --brightness-hover: brightness(1.1);
            --contrast-elevated: contrast(1.05);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gradient-hero);
            background-attachment: fixed;
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
            font-feature-settings: "kern" 1, "liga" 1;
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            position: relative;
            overflow-x: hidden;
        }

        /* Efectos de fondo 3D para canales TV */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 25% 25%, rgba(16, 185, 129, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(52, 211, 153, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 50% 50%, rgba(5, 150, 105, 0.06) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        /* Header Administrativo 3D para Canales TV */
        .header {
            background: rgba(31, 41, 55, 0.95);
            backdrop-filter: var(--blur-glass);
            padding: var(--space-lg) 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.12);
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: var(--shadow-elevated);
            transform: var(--transform-card);
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--gradient-primary);
            opacity: 0.8;
        }

        .header-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.5rem;
            font-weight: 800;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-decoration: none;
            letter-spacing: -0.025em;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .logo i {
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: tvPulse 3s ease-in-out infinite;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
        }

        @keyframes tvPulse {
            0%, 100% { transform: scale(1) rotateY(0deg); }
            50% { transform: scale(1.1) rotateY(15deg); }
        }

        .nav-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .nav-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.6rem 1rem;
            background: var(--gradient-card);
            border: 1px solid rgba(255, 255, 255, 0.12);
            color: var(--text-primary);
            text-decoration: none;
            border-radius: var(--radius-xl);
            backdrop-filter: var(--blur-backdrop);
            transition: var(--transition-normal);
            font-weight: 500;
            font-size: 0.9rem;
            cursor: pointer;
            box-shadow: var(--shadow-3d);
            position: relative;
            overflow: hidden;
            white-space: nowrap;
        }

        .nav-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
            opacity: 0;
            transition: var(--transition-fast);
        }

        .nav-btn:hover {
            background: var(--gradient-elevated);
            border-color: var(--primary-color);
            transform: var(--transform-hover);
            box-shadow: var(--shadow-elevated), var(--shadow-glow);
            filter: var(--brightness-hover);
        }

        .nav-btn:hover::before {
            opacity: 1;
        }

        .nav-btn.back-btn {
            background: var(--gradient-primary);
            border-color: var(--primary-color);
            color: white;
            box-shadow: var(--shadow-3d), var(--shadow-glow);
        }

        .nav-btn.back-btn:hover {
            background: var(--gradient-primary);
            transform: var(--transform-hover) scale(1.05);
            box-shadow: var(--shadow-elevated), var(--shadow-glow);
        }

        /* Contenido Principal */
        .main-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: var(--space-xl) 1.5rem;
        }

        .page-header {
            margin-bottom: var(--space-xl);
        }

        .page-title {
            font-size: 2rem;
            font-weight: 800;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            letter-spacing: -0.025em;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .page-subtitle {
            color: var(--text-secondary);
            font-size: 1.1rem;
            margin-bottom: var(--space-lg);
        }

        /* Estadísticas */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--space-lg);
            margin-bottom: var(--space-xl);
        }

        .stat-card {
            background: var(--gradient-card);
            border: 1px solid rgba(255, 255, 255, 0.12);
            border-radius: var(--radius-xl);
            backdrop-filter: var(--blur-backdrop);
            padding: var(--space-lg);
            transition: var(--transition-normal);
            box-shadow: var(--shadow-3d);
            transform: var(--transform-card);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--gradient-primary);
            transform: scaleX(0);
            transition: var(--transition-normal);
            border-radius: var(--radius-xl) var(--radius-xl) 0 0;
        }

        .stat-card:hover {
            background: var(--gradient-elevated);
            border-color: var(--primary-color);
            transform: var(--transform-hover) var(--transform-3d);
            box-shadow: var(--shadow-2xl), var(--shadow-glow);
            filter: var(--brightness-hover);
        }

        .stat-card:hover::before {
            transform: scaleX(1);
        }

        .stat-icon {
            width: 56px;
            height: 56px;
            background: var(--gradient-primary);
            border-radius: var(--radius-xl);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: var(--space-md);
            color: white;
            font-size: 1.5rem;
            box-shadow: var(--shadow-3d), var(--shadow-glow);
            transition: var(--transition-normal);
        }

        .stat-card:hover .stat-icon {
            transform: scale(1.1) rotateY(15deg);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 800;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
            transition: var(--transition-normal);
        }

        .stat-card:hover .stat-number {
            transform: scale(1.05);
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .stat-card:hover .stat-label {
            color: var(--text-primary);
        }

        /* Controles y Formularios */
        .controls-section {
            background: var(--gradient-card);
            border: 1px solid rgba(255, 255, 255, 0.12);
            border-radius: var(--radius-xl);
            backdrop-filter: var(--blur-backdrop);
            padding: var(--space-xl);
            margin-bottom: var(--space-xl);
            box-shadow: var(--shadow-3d);
        }

        .controls-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--gradient-primary);
            opacity: 0.8;
        }

        .controls-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--space-xl);
        }

        .control-group {
            display: flex;
            flex-direction: column;
            gap: var(--space-md);
        }

        .control-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--space-sm);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .form-input {
            padding: 0.75rem;
            background: var(--gradient-surface);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: var(--radius-lg);
            color: var(--text-primary);
            font-size: 0.9rem;
            transition: var(--transition-normal);
            backdrop-filter: var(--blur-backdrop);
            box-shadow: var(--shadow-sm);
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: var(--shadow-md), var(--shadow-glow);
            transform: translateY(-2px);
            background: var(--gradient-elevated);
        }

        .form-input:hover {
            border-color: rgba(255, 255, 255, 0.2);
            transform: translateY(-1px);
        }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: var(--radius-lg);
            font-weight: 500;
            font-size: 0.9rem;
            cursor: pointer;
            transition: var(--transition-normal);
            backdrop-filter: var(--blur-backdrop);
            box-shadow: var(--shadow-sm);
            position: relative;
            overflow: hidden;
            white-space: nowrap;
            text-decoration: none;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
            opacity: 0;
            transition: var(--transition-fast);
        }

        .btn:hover::before {
            opacity: 1;
        }

        .btn-primary {
            background: var(--gradient-primary);
            border-color: var(--primary-color);
            color: white;
            box-shadow: var(--shadow-sm), var(--shadow-glow);
        }

        .btn-primary:hover {
            background: var(--gradient-primary);
            transform: var(--transform-hover);
            box-shadow: var(--shadow-md), var(--shadow-glow);
            filter: var(--brightness-hover);
        }

        .btn-secondary {
            background: var(--gradient-surface);
            border-color: rgba(255, 255, 255, 0.2);
            color: var(--text-primary);
        }

        .btn-secondary:hover {
            background: var(--gradient-elevated);
            border-color: var(--primary-color);
            transform: var(--transform-hover);
            box-shadow: var(--shadow-md);
        }

        .btn-danger {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            border-color: #dc2626;
            color: white;
            box-shadow: var(--shadow-sm), 0 0 15px rgba(220, 38, 38, 0.3);
        }

        .btn-danger:hover {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            transform: var(--transform-hover);
            box-shadow: var(--shadow-md), 0 0 25px rgba(220, 38, 38, 0.5);
        }

        /* Filtros */
        .filters-section {
            background: var(--gradient-card);
            border: 1px solid rgba(255, 255, 255, 0.12);
            border-radius: var(--radius-xl);
            backdrop-filter: var(--blur-backdrop);
            padding: var(--space-lg);
            margin-bottom: var(--space-xl);
            box-shadow: var(--shadow-sm);
        }

        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--space-md);
            align-items: end;
        }

        /* Canales por Categoría */
        .category-section {
            margin-bottom: var(--space-2xl);
        }

        .category-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: var(--space-lg);
            padding: var(--space-md) 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .category-title {
            font-size: 1.5rem;
            font-weight: 700;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .category-count {
            background: var(--gradient-primary);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: var(--radius-full);
            font-size: 0.8rem;
            font-weight: 600;
            box-shadow: var(--shadow-sm), var(--shadow-glow);
        }

        /* Grid de Canales */
        .channels-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: var(--space-lg);
        }

        .channel-card {
            background: var(--gradient-card);
            border: 1px solid rgba(255, 255, 255, 0.12);
            border-radius: var(--radius-xl);
            backdrop-filter: var(--blur-backdrop);
            overflow: hidden;
            transition: var(--transition-normal);
            box-shadow: var(--shadow-3d);
            transform: var(--transform-card);
            position: relative;
        }

        .channel-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--gradient-primary);
            transform: scaleX(0);
            transition: var(--transition-normal);
            border-radius: var(--radius-xl) var(--radius-xl) 0 0;
        }

        .channel-card:hover {
            background: var(--gradient-elevated);
            border-color: var(--primary-color);
            transform: var(--transform-hover) var(--transform-3d);
            box-shadow: var(--shadow-2xl), var(--shadow-glow);
            filter: var(--brightness-hover);
        }

        .channel-card:hover::before {
            transform: scaleX(1);
        }

        .channel-header {
            padding: var(--space-lg);
            display: flex;
            align-items: center;
            gap: var(--space-md);
        }

        .channel-logo {
            width: 64px;
            height: 64px;
            border-radius: var(--radius-lg);
            object-fit: cover;
            background: var(--gradient-surface);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-muted);
            font-size: 1.5rem;
            box-shadow: var(--shadow-sm);
            transition: var(--transition-normal);
        }

        .channel-card:hover .channel-logo {
            transform: scale(1.05);
            box-shadow: var(--shadow-md);
        }

        .channel-info {
            flex: 1;
            min-width: 0;
        }

        .channel-name {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.25rem;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .channel-meta {
            display: flex;
            align-items: center;
            gap: var(--space-sm);
            color: var(--text-secondary);
            font-size: 0.85rem;
            flex-wrap: wrap;
        }

        .channel-status {
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            padding: 0.25rem 0.5rem;
            border-radius: var(--radius-full);
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .status-online {
            background: rgba(22, 163, 74, 0.2);
            color: var(--online-color);
            border: 1px solid rgba(22, 163, 74, 0.3);
        }

        .status-offline {
            background: rgba(220, 38, 38, 0.2);
            color: var(--offline-color);
            border: 1px solid rgba(220, 38, 38, 0.3);
        }

        .status-unknown {
            background: rgba(107, 114, 128, 0.2);
            color: var(--unknown-color);
            border: 1px solid rgba(107, 114, 128, 0.3);
        }

        .channel-actions {
            padding: var(--space-md) var(--space-lg);
            border-top: 1px solid rgba(255, 255, 255, 0.08);
            display: flex;
            gap: var(--space-sm);
        }

        .action-btn {
            flex: 1;
            padding: 0.5rem;
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: var(--radius-lg);
            cursor: pointer;
            transition: var(--transition-normal);
            font-weight: 500;
            font-size: 0.8rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.25rem;
            backdrop-filter: var(--blur-backdrop);
            box-shadow: var(--shadow-sm);
            position: relative;
            overflow: hidden;
            white-space: nowrap;
        }

        .action-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
            opacity: 0;
            transition: var(--transition-fast);
        }

        .action-btn:hover::before {
            opacity: 1;
        }

        .btn-copy {
            background: var(--gradient-primary);
            border-color: var(--primary-color);
            color: white;
            box-shadow: var(--shadow-sm), var(--shadow-glow);
        }

        .btn-copy:hover {
            background: var(--gradient-primary);
            transform: var(--transform-hover);
            box-shadow: var(--shadow-md), var(--shadow-glow);
            filter: var(--brightness-hover);
        }

        .btn-test {
            background: var(--gradient-surface);
            border-color: rgba(255, 255, 255, 0.2);
            color: var(--text-primary);
        }

        .btn-test:hover {
            background: var(--gradient-elevated);
            border-color: var(--primary-color);
            transform: var(--transform-hover);
            box-shadow: var(--shadow-md);
        }

        .btn-test.testing {
            background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
            border-color: #d97706;
            color: white;
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        /* Mensajes */
        .message {
            padding: var(--space-md) var(--space-lg);
            border-radius: var(--radius-lg);
            margin-bottom: var(--space-lg);
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 500;
            backdrop-filter: var(--blur-backdrop);
            box-shadow: var(--shadow-sm);
        }

        .message.success {
            background: rgba(22, 163, 74, 0.15);
            border: 1px solid rgba(22, 163, 74, 0.3);
            color: var(--success-color);
        }

        .message.error {
            background: rgba(220, 38, 38, 0.15);
            border: 1px solid rgba(220, 38, 38, 0.3);
            color: var(--error-color);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .header-container {
                flex-direction: column;
                gap: var(--space-md);
                text-align: center;
            }

            .nav-buttons {
                flex-wrap: wrap;
                justify-content: center;
            }

            .nav-btn span {
                display: none;
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }

            .controls-grid {
                grid-template-columns: 1fr;
            }

            .filters-grid {
                grid-template-columns: 1fr;
            }

            .channels-grid {
                grid-template-columns: 1fr;
            }

            .channel-header {
                padding: var(--space-md);
            }

            .channel-actions {
                flex-direction: column;
                gap: var(--space-xs);
            }

            .action-btn {
                font-size: 0.75rem;
                padding: 0.4rem;
            }
        }

        @media (max-width: 480px) {
            .main-content {
                padding: var(--space-lg) 1rem;
            }

            .page-title {
                font-size: 1.5rem;
            }

            .category-title {
                font-size: 1.2rem;
            }

            .channel-logo {
                width: 48px;
                height: 48px;
                font-size: 1.2rem;
            }

            .channel-name {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-container">
            <a href="#" class="logo">
                <i class="fas fa-tv"></i>
                <span>Gestión de Canales TV</span>
            </a>

            <div class="nav-buttons">
                <a href="admin.php" class="nav-btn back-btn">
                    <i class="fas fa-arrow-left"></i>
                    <span>Volver al Admin</span>
                </a>
                <a href="tickets_admin.php" class="nav-btn">
                    <i class="fas fa-ticket-alt"></i>
                    <span>Tickets</span>
                </a>
                <a href="logout.php" class="nav-btn">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Salir</span>
                </a>
            </div>
        </div>
    </header>

    <!-- Contenido Principal -->
    <main class="main-content">
        <!-- Header de Página -->
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-tv"></i>
                Gestión de Canales de TV
            </h1>
            <p class="page-subtitle">
                Administra y monitorea canales de televisión desde listas M3U con funciones avanzadas de testing y gestión.
            </p>
        </div>

        <!-- Mensajes -->
        <?php if (!empty($message)): ?>
        <div class="message <?php echo $message_type; ?>">
            <i class="fas fa-<?php echo $message_type === 'success' ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
            <?php echo htmlspecialchars($message); ?>
        </div>
        <?php endif; ?>

        <!-- Estadísticas -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-list"></i>
                </div>
                <div class="stat-number"><?php echo $stats['total'] ?? 0; ?></div>
                <div class="stat-label">Total Canales</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-folder"></i>
                </div>
                <div class="stat-number"><?php echo $stats['categories'] ?? 0; ?></div>
                <div class="stat-label">Categorías</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-number"><?php echo $stats['online'] ?? 0; ?></div>
                <div class="stat-label">En Línea</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-times-circle"></i>
                </div>
                <div class="stat-number"><?php echo $stats['offline'] ?? 0; ?></div>
                <div class="stat-label">Fuera de Línea</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-question-circle"></i>
                </div>
                <div class="stat-number"><?php echo $stats['unknown'] ?? 0; ?></div>
                <div class="stat-label">Sin Verificar</div>
            </div>
        </div>

        <!-- Controles -->
        <div class="controls-section">
            <div class="controls-grid">
                <!-- Subir Lista M3U -->
                <div class="control-group">
                    <h3 class="control-title">
                        <i class="fas fa-upload"></i>
                        Cargar Lista M3U
                    </h3>
                    <form method="POST" enctype="multipart/form-data">
                        <input type="hidden" name="action" value="upload_m3u">
                        <input type="file" name="m3u_file" accept=".m3u,.m3u8" class="form-input" required>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-upload"></i>
                            Procesar Lista M3U
                        </button>
                    </form>
                </div>

                <!-- Acciones Rápidas -->
                <div class="control-group">
                    <h3 class="control-title">
                        <i class="fas fa-tools"></i>
                        Acciones Rápidas
                    </h3>
                    <button onclick="testAllChannels()" class="btn btn-secondary">
                        <i class="fas fa-play-circle"></i>
                        Probar Todos los Canales
                    </button>
                    <form method="POST" style="display: inline;" onsubmit="return confirm('¿Estás seguro de eliminar todos los canales?')">
                        <input type="hidden" name="action" value="clear_channels">
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash"></i>
                            Limpiar Todos los Canales
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Filtros -->
        <div class="filters-section">
            <form method="GET" class="filters-grid">
                <div>
                    <label for="category" style="color: var(--text-secondary); font-size: 0.9rem; margin-bottom: 0.5rem; display: block;">Categoría</label>
                    <select name="category" id="category" class="form-input">
                        <option value="all">Todas las categorías</option>
                        <?php foreach ($categories as $category): ?>
                        <option value="<?php echo htmlspecialchars($category); ?>" <?php echo $category_filter === $category ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($category); ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div>
                    <label for="status" style="color: var(--text-secondary); font-size: 0.9rem; margin-bottom: 0.5rem; display: block;">Estado</label>
                    <select name="status" id="status" class="form-input">
                        <option value="all">Todos los estados</option>
                        <option value="online" <?php echo $status_filter === 'online' ? 'selected' : ''; ?>>En línea</option>
                        <option value="offline" <?php echo $status_filter === 'offline' ? 'selected' : ''; ?>>Fuera de línea</option>
                        <option value="unknown" <?php echo $status_filter === 'unknown' ? 'selected' : ''; ?>>Sin verificar</option>
                    </select>
                </div>

                <div>
                    <label for="country" style="color: var(--text-secondary); font-size: 0.9rem; margin-bottom: 0.5rem; display: block;">País</label>
                    <select name="country" id="country" class="form-input">
                        <option value="all">Todos los países</option>
                        <?php foreach ($countries as $country): ?>
                        <option value="<?php echo htmlspecialchars($country); ?>" <?php echo $country_filter === $country ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($country); ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div style="display: flex; gap: 0.5rem; align-items: end;">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter"></i>
                        Filtrar
                    </button>
                    <a href="?" class="btn btn-secondary">
                        <i class="fas fa-times"></i>
                        Limpiar
                    </a>
                </div>
            </form>
        </div>

        <!-- Canales por Categoría -->
        <?php if (empty($channels_by_category)): ?>
        <div style="text-align: center; padding: 3rem; color: var(--text-secondary);">
            <i class="fas fa-tv" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
            <h3 style="font-size: 1.2rem; margin-bottom: 0.5rem; color: var(--text-primary);">No hay canales disponibles</h3>
            <p>Sube una lista M3U para comenzar a gestionar canales de TV.</p>
        </div>
        <?php else: ?>
            <?php foreach ($channels_by_category as $category => $category_channels): ?>
            <div class="category-section">
                <div class="category-header">
                    <h2 class="category-title">
                        <i class="fas fa-folder"></i>
                        <?php echo htmlspecialchars($category); ?>
                    </h2>
                    <span class="category-count"><?php echo count($category_channels); ?> canales</span>
                </div>

                <div class="channels-grid">
                    <?php foreach ($category_channels as $channel): ?>
                    <div class="channel-card" data-channel-id="<?php echo $channel['id']; ?>">
                        <div class="channel-header">
                            <?php if (!empty($channel['logo'])): ?>
                            <img src="<?php echo htmlspecialchars($channel['logo']); ?>"
                                 alt="<?php echo htmlspecialchars($channel['name']); ?>"
                                 class="channel-logo"
                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                            <div class="channel-logo" style="display: none;">
                                <i class="fas fa-tv"></i>
                            </div>
                            <?php else: ?>
                            <div class="channel-logo">
                                <i class="fas fa-tv"></i>
                            </div>
                            <?php endif; ?>

                            <div class="channel-info">
                                <h3 class="channel-name" title="<?php echo htmlspecialchars($channel['name']); ?>">
                                    <?php echo htmlspecialchars($channel['name']); ?>
                                </h3>
                                <div class="channel-meta">
                                    <span class="channel-status status-<?php echo $channel['status']; ?>">
                                        <i class="fas fa-<?php echo $channel['status'] === 'online' ? 'check-circle' : ($channel['status'] === 'offline' ? 'times-circle' : 'question-circle'); ?>"></i>
                                        <?php echo ucfirst($channel['status']); ?>
                                    </span>
                                    <?php if (!empty($channel['country'])): ?>
                                    <span><i class="fas fa-flag"></i> <?php echo htmlspecialchars($channel['country']); ?></span>
                                    <?php endif; ?>
                                    <?php if (!empty($channel['language'])): ?>
                                    <span><i class="fas fa-language"></i> <?php echo htmlspecialchars($channel['language']); ?></span>
                                    <?php endif; ?>
                                    <?php if ($channel['last_check']): ?>
                                    <span><i class="fas fa-clock"></i> <?php echo date('d/m/Y H:i', strtotime($channel['last_check'])); ?></span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <div class="channel-actions">
                            <button onclick="copyChannelUrl(<?php echo $channel['id']; ?>, '<?php echo addslashes($channel['url']); ?>')"
                                    class="action-btn btn-copy">
                                <i class="fas fa-copy"></i>
                                Copiar URL
                            </button>
                            <button onclick="testChannel(<?php echo $channel['id']; ?>, '<?php echo addslashes($channel['url']); ?>')"
                                    class="action-btn btn-test"
                                    id="test-btn-<?php echo $channel['id']; ?>">
                                <i class="fas fa-play-circle"></i>
                                Test 200 OK
                            </button>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </main>

    <!-- JavaScript para funcionalidades avanzadas -->
    <script>
        // Función para copiar URL del canal
        function copyChannelUrl(channelId, url) {
            navigator.clipboard.writeText(url).then(function() {
                showToast('URL copiada al portapapeles', 'success');
            }).catch(function(err) {
                console.error('Error al copiar: ', err);
                showToast('Error al copiar la URL', 'error');
            });
        }

        // Función para probar canal individual (200 OK test)
        async function testChannel(channelId, url) {
            const button = document.getElementById(`test-btn-${channelId}`);
            const originalContent = button.innerHTML;

            // Cambiar estado del botón
            button.classList.add('testing');
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Probando...';
            button.disabled = true;

            try {
                const response = await fetch('test_channel.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        channel_id: channelId,
                        url: url
                    })
                });

                const result = await response.json();

                // Actualizar estado del canal en la interfaz
                updateChannelStatus(channelId, result.status, result.response_time);

                if (result.status === 'online') {
                    showToast(`Canal en línea (${result.response_time}ms)`, 'success');
                } else {
                    showToast('Canal fuera de línea', 'error');
                }

            } catch (error) {
                console.error('Error al probar canal:', error);
                showToast('Error al probar el canal', 'error');
            } finally {
                // Restaurar botón
                button.classList.remove('testing');
                button.innerHTML = originalContent;
                button.disabled = false;
            }
        }

        // Función para probar todos los canales
        async function testAllChannels() {
            if (!confirm('¿Estás seguro de probar todos los canales? Esto puede tomar varios minutos.')) {
                return;
            }

            const channels = document.querySelectorAll('[data-channel-id]');
            let completed = 0;
            const total = channels.length;

            showToast(`Iniciando prueba de ${total} canales...`, 'info');

            // Probar canales en lotes para evitar sobrecarga
            const batchSize = 5;
            for (let i = 0; i < channels.length; i += batchSize) {
                const batch = Array.from(channels).slice(i, i + batchSize);

                await Promise.all(batch.map(async (channelCard) => {
                    const channelId = channelCard.dataset.channelId;
                    const testButton = channelCard.querySelector('.btn-test');
                    const url = testButton.onclick.toString().match(/'([^']+)'/)[1];

                    try {
                        await testChannel(channelId, url);
                    } catch (error) {
                        console.error(`Error testing channel ${channelId}:`, error);
                    }

                    completed++;
                    updateProgress(completed, total);
                }));

                // Pausa entre lotes
                await new Promise(resolve => setTimeout(resolve, 1000));
            }

            showToast('Prueba de canales completada', 'success');
        }

        // Función para actualizar el estado del canal en la interfaz
        function updateChannelStatus(channelId, status, responseTime) {
            const channelCard = document.querySelector(`[data-channel-id="${channelId}"]`);
            if (!channelCard) return;

            const statusElement = channelCard.querySelector('.channel-status');
            const metaElement = channelCard.querySelector('.channel-meta');

            // Actualizar clase de estado
            statusElement.className = `channel-status status-${status}`;

            // Actualizar icono y texto
            const iconClass = status === 'online' ? 'check-circle' :
                             status === 'offline' ? 'times-circle' : 'question-circle';
            statusElement.innerHTML = `<i class="fas fa-${iconClass}"></i> ${status.charAt(0).toUpperCase() + status.slice(1)}`;

            // Agregar tiempo de respuesta si está disponible
            if (responseTime && status === 'online') {
                const existingTime = metaElement.querySelector('.response-time');
                if (existingTime) {
                    existingTime.remove();
                }

                const timeSpan = document.createElement('span');
                timeSpan.className = 'response-time';
                timeSpan.innerHTML = `<i class="fas fa-tachometer-alt"></i> ${responseTime}ms`;
                metaElement.appendChild(timeSpan);
            }
        }

        // Función para mostrar progreso
        function updateProgress(completed, total) {
            const percentage = Math.round((completed / total) * 100);
            console.log(`Progreso: ${completed}/${total} (${percentage}%)`);
        }

        // Función para mostrar notificaciones toast
        function showToast(message, type = 'info') {
            // Crear elemento toast
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: var(--gradient-card);
                border: 1px solid rgba(255, 255, 255, 0.12);
                border-radius: var(--radius-lg);
                padding: 1rem 1.5rem;
                color: var(--text-primary);
                backdrop-filter: var(--blur-backdrop);
                box-shadow: var(--shadow-lg);
                z-index: 10000;
                transform: translateX(100%);
                transition: var(--transition-normal);
                max-width: 300px;
                word-wrap: break-word;
            `;

            // Agregar icono según el tipo
            const icons = {
                success: 'check-circle',
                error: 'exclamation-triangle',
                info: 'info-circle',
                warning: 'exclamation-circle'
            };

            toast.innerHTML = `
                <div style="display: flex; align-items: center; gap: 0.5rem;">
                    <i class="fas fa-${icons[type] || 'info-circle'}"></i>
                    <span>${message}</span>
                </div>
            `;

            // Agregar al DOM
            document.body.appendChild(toast);

            // Animar entrada
            setTimeout(() => {
                toast.style.transform = 'translateX(0)';
            }, 100);

            // Remover después de 3 segundos
            setTimeout(() => {
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }

        // Auto-refresh de estadísticas cada 30 segundos
        setInterval(() => {
            fetch('get_channel_stats.php')
                .then(response => response.json())
                .then(stats => {
                    document.querySelector('.stat-card:nth-child(1) .stat-number').textContent = stats.total || 0;
                    document.querySelector('.stat-card:nth-child(3) .stat-number').textContent = stats.online || 0;
                    document.querySelector('.stat-card:nth-child(4) .stat-number').textContent = stats.offline || 0;
                    document.querySelector('.stat-card:nth-child(5) .stat-number').textContent = stats.unknown || 0;
                })
                .catch(error => console.error('Error updating stats:', error));
        }, 30000);

        // Inicialización
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Gestión de Canales TV cargada correctamente');

            // Agregar tooltips a los botones
            const buttons = document.querySelectorAll('.action-btn');
            buttons.forEach(button => {
                button.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px) scale(1.05)';
                });

                button.addEventListener('mouseleave', function() {
                    this.style.transform = '';
                });
            });
        });
    </script>
</body>
</html>
