<?php
// Configuración MySQL
$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Error de conexión a la base de datos: " . $e->getMessage());
}

// Obtener ID de la solicitud
$request_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$request_id) {
    header('Location: index.php');
    exit;
}

// Obtener detalles de la solicitud
try {
    $stmt = $pdo->prepare("SELECT * FROM service_requests WHERE id = ?");
    $stmt->execute([$request_id]);
    $request = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$request) {
        header('Location: index.php');
        exit;
    }
} catch (Exception $e) {
    header('Location: index.php');
    exit;
}

// Función para obtener el nombre del dispositivo
function getDeviceName($device_type) {
    $devices = [
        'android' => 'Android',
        'ios' => 'iPhone/iPad',
        'smart_tv_samsung' => 'Smart TV Samsung',
        'smart_tv_lg' => 'Smart TV LG',
        'smart_tv_other' => 'Otra Smart TV',
        'pc_windows' => 'PC Windows',
        'pc_mac' => 'Mac',
        'other' => 'Otro dispositivo'
    ];
    
    return $devices[$device_type] ?? 'Dispositivo desconocido';
}

// Función para obtener el tipo de servicio
function getServiceTypeName($service_type) {
    return $service_type === 'trial' ? 'Prueba Gratuita' : 'Compra Directa';
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>✅ Solicitud Enviada - Confirmación</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            /* Paleta de colores profesional con verde como principal */
            --primary-color: #10b981;
            --primary-dark: #059669;
            --primary-light: #34d399;
            --secondary-color: #1f2937;
            --dark-bg: #111827;
            --darker-bg: #030712;
            --surface: #374151;
            --surface-light: #4b5563;
            --surface-elevated: #6b7280;

            /* Colores de texto profesionales */
            --text-primary: #f9fafb;
            --text-secondary: #d1d5db;
            --text-muted: #9ca3af;
            --text-accent: #34d399;

            /* Colores de estado */
            --success-color: #16a34a;
            --warning-color: #d97706;
            --info-color: #2563eb;

            /* Gradientes corporativos 3D con verde */
            --gradient-primary: linear-gradient(135deg, #10b981 0%, #059669 50%, #047857 100%);
            --gradient-secondary: linear-gradient(135deg, #34d399 0%, #10b981 50%, #059669 100%);
            --gradient-surface: linear-gradient(135deg, #1f2937 0%, #374151 50%, #4b5563 100%);
            --gradient-hero: linear-gradient(135deg, #030712 0%, #111827 25%, #1f2937 50%, #374151 100%);
            --gradient-card: linear-gradient(145deg, #1f2937 0%, #374151 100%);
            --gradient-elevated: linear-gradient(145deg, #374151 0%, #4b5563 100%);

            /* Sombras 3D profesionales */
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            --shadow-3d: 0 8px 16px rgba(0, 0, 0, 0.15), 0 4px 8px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);
            --shadow-elevated: 0 12px 24px rgba(0, 0, 0, 0.2), 0 6px 12px rgba(0, 0, 0, 0.15);
            --shadow-glow: 0 0 20px rgba(16, 185, 129, 0.4);
            --shadow-success: 0 0 20px rgba(22, 163, 74, 0.3);

            /* Espaciado y bordes */
            --space-xs: 0.25rem;
            --space-sm: 0.5rem;
            --space-md: 1rem;
            --space-lg: 1.5rem;
            --space-xl: 2rem;
            --space-2xl: 3rem;
            --radius-sm: 6px;
            --radius-md: 8px;
            --radius-lg: 12px;
            --radius-xl: 16px;
            --radius-full: 9999px;

            /* Transiciones */
            --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);

            /* Transformaciones 3D */
            --transform-hover: translateY(-4px) scale(1.02);
            --transform-card: perspective(1000px) rotateX(0deg) rotateY(0deg);

            /* Filtros */
            --blur-backdrop: blur(12px) saturate(150%);
            --brightness-hover: brightness(1.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gradient-hero);
            background-attachment: fixed;
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
            font-feature-settings: "kern" 1, "liga" 1;
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: var(--space-lg);
        }

        /* Efectos de fondo */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 25% 25%, rgba(16, 185, 129, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(52, 211, 153, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 50% 50%, rgba(5, 150, 105, 0.06) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        .confirmation-container {
            max-width: 600px;
            width: 100%;
            background: var(--gradient-card);
            border: 1px solid rgba(255, 255, 255, 0.12);
            border-radius: var(--radius-xl);
            backdrop-filter: var(--blur-backdrop);
            box-shadow: var(--shadow-2xl), var(--shadow-glow);
            overflow: hidden;
            animation: slideInUp 0.6s ease-out;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .confirmation-header {
            background: var(--gradient-primary);
            color: white;
            padding: var(--space-xl);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .confirmation-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
            pointer-events: none;
        }

        .success-icon {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto var(--space-lg);
            font-size: 2.5rem;
            animation: bounceIn 0.8s ease-out 0.3s both;
            box-shadow: var(--shadow-lg);
        }

        @keyframes bounceIn {
            0% { transform: scale(0); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .confirmation-title {
            font-size: 2rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
            position: relative;
        }

        .confirmation-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            position: relative;
        }

        .confirmation-content {
            padding: var(--space-xl);
        }

        .request-details {
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid rgba(255, 255, 255, 0.08);
            border-radius: var(--radius-lg);
            padding: var(--space-lg);
            margin-bottom: var(--space-xl);
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        }

        .detail-row:last-child {
            border-bottom: none;
        }

        .detail-label {
            color: var(--text-secondary);
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .detail-value {
            color: var(--text-primary);
            font-weight: 600;
            text-align: right;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: var(--radius-full);
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            background: rgba(217, 119, 6, 0.2);
            color: var(--warning-color);
            border: 1px solid rgba(217, 119, 6, 0.3);
        }

        .next-steps {
            background: rgba(37, 99, 235, 0.1);
            border: 1px solid rgba(37, 99, 235, 0.2);
            border-radius: var(--radius-lg);
            padding: var(--space-lg);
            margin-bottom: var(--space-xl);
        }

        .next-steps h3 {
            color: var(--info-color);
            margin-bottom: var(--space-md);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .next-steps ul {
            list-style: none;
            padding: 0;
        }

        .next-steps li {
            padding: 0.5rem 0;
            color: var(--text-secondary);
            display: flex;
            align-items: flex-start;
            gap: 0.75rem;
        }

        .next-steps li::before {
            content: '✓';
            color: var(--success-color);
            font-weight: bold;
            flex-shrink: 0;
            margin-top: 0.1rem;
        }

        .action-buttons {
            display: flex;
            gap: var(--space-md);
            flex-wrap: wrap;
        }

        .btn {
            flex: 1;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: var(--radius-lg);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-normal);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            text-decoration: none;
            min-width: 140px;
        }

        .btn-primary {
            background: var(--gradient-primary);
            color: white;
            box-shadow: var(--shadow-sm), var(--shadow-glow);
        }

        .btn-primary:hover {
            background: var(--gradient-primary);
            transform: var(--transform-hover);
            box-shadow: var(--shadow-md), var(--shadow-glow);
            filter: var(--brightness-hover);
        }

        .btn-secondary {
            background: var(--gradient-surface);
            color: var(--text-primary);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: var(--shadow-sm);
        }

        .btn-secondary:hover {
            background: var(--gradient-elevated);
            border-color: var(--primary-color);
            transform: var(--transform-hover);
            box-shadow: var(--shadow-md);
        }

        /* Responsive */
        @media (max-width: 768px) {
            body {
                padding: var(--space-md);
            }

            .confirmation-header {
                padding: var(--space-lg);
            }

            .confirmation-title {
                font-size: 1.5rem;
            }

            .confirmation-subtitle {
                font-size: 1rem;
            }

            .success-icon {
                width: 60px;
                height: 60px;
                font-size: 2rem;
            }

            .detail-row {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.25rem;
            }

            .detail-value {
                text-align: left;
            }

            .action-buttons {
                flex-direction: column;
            }

            .btn {
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <div class="confirmation-container">
        <div class="confirmation-header">
            <div class="success-icon">
                <i class="fas fa-check"></i>
            </div>
            <h1 class="confirmation-title">¡Solicitud Enviada!</h1>
            <p class="confirmation-subtitle">Tu solicitud ha sido recibida correctamente</p>
        </div>
        
        <div class="confirmation-content">
            <div class="request-details">
                <div class="detail-row">
                    <span class="detail-label">
                        <i class="fas fa-hashtag"></i>
                        Número de Solicitud
                    </span>
                    <span class="detail-value">#<?php echo $request['id']; ?></span>
                </div>
                
                <div class="detail-row">
                    <span class="detail-label">
                        <i class="fas fa-user"></i>
                        Cliente
                    </span>
                    <span class="detail-value"><?php echo htmlspecialchars($request['first_name'] . ' ' . $request['last_name']); ?></span>
                </div>
                
                <div class="detail-row">
                    <span class="detail-label">
                        <i class="fas fa-envelope"></i>
                        Email
                    </span>
                    <span class="detail-value"><?php echo htmlspecialchars($request['email']); ?></span>
                </div>
                
                <div class="detail-row">
                    <span class="detail-label">
                        <i class="fas fa-shopping-cart"></i>
                        Tipo de Servicio
                    </span>
                    <span class="detail-value"><?php echo getServiceTypeName($request['service_type']); ?></span>
                </div>
                
                <div class="detail-row">
                    <span class="detail-label">
                        <i class="fas fa-tv"></i>
                        Dispositivo
                    </span>
                    <span class="detail-value"><?php echo getDeviceName($request['device_type']); ?></span>
                </div>
                
                <div class="detail-row">
                    <span class="detail-label">
                        <i class="fas fa-clock"></i>
                        Estado
                    </span>
                    <span class="detail-value">
                        <span class="status-badge">Pendiente</span>
                    </span>
                </div>
                
                <div class="detail-row">
                    <span class="detail-label">
                        <i class="fas fa-calendar"></i>
                        Fecha de Solicitud
                    </span>
                    <span class="detail-value"><?php echo date('d/m/Y H:i', strtotime($request['created_at'])); ?></span>
                </div>
            </div>
            
            <div class="next-steps">
                <h3>
                    <i class="fas fa-list-check"></i>
                    Próximos Pasos
                </h3>
                <ul>
                    <li>Nuestro equipo revisará tu solicitud en las próximas horas</li>
                    <li>Te contactaremos al teléfono <?php echo htmlspecialchars($request['phone']); ?> para confirmar detalles</li>
                    <li>Recibirás un email con las credenciales de acceso una vez aprobada</li>
                    <li>Podrás comenzar a disfrutar del servicio inmediatamente</li>
                    <?php if ($request['service_type'] === 'trial'): ?>
                    <li>Tu prueba gratuita tendrá una duración de 24-48 horas</li>
                    <?php endif; ?>
                </ul>
            </div>
            
            <div class="action-buttons">
                <a href="index.php" class="btn btn-primary">
                    <i class="fas fa-home"></i>
                    Volver al Inicio
                </a>
                <a href="mailto:<?php echo htmlspecialchars($request['email']); ?>?subject=Solicitud%20IPTV%20%23<?php echo $request['id']; ?>" class="btn btn-secondary">
                    <i class="fas fa-envelope"></i>
                    Contactar Soporte
                </a>
            </div>
        </div>
    </div>
</body>
</html>
