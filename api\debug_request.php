<?php
/**
 * Debug: Capturar exactamente qué envía la app Android
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// Manejar preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Capturar toda la información de la petición
$request_info = [
    'method' => $_SERVER['REQUEST_METHOD'] ?? 'Unknown',
    'uri' => $_SERVER['REQUEST_URI'] ?? 'Unknown',
    'query_string' => $_SERVER['QUERY_STRING'] ?? '',
    'content_type' => $_SERVER['CONTENT_TYPE'] ?? 'Unknown',
    'content_length' => $_SERVER['CONTENT_LENGTH'] ?? 0,
    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown',
    'host' => $_SERVER['HTTP_HOST'] ?? 'Unknown',
    'timestamp' => date('Y-m-d H:i:s')
];

// Capturar headers
$headers = [];
foreach ($_SERVER as $key => $value) {
    if (strpos($key, 'HTTP_') === 0) {
        $header_name = str_replace('HTTP_', '', $key);
        $header_name = str_replace('_', '-', $header_name);
        $headers[$header_name] = $value;
    }
}

// Capturar body
$raw_body = file_get_contents('php://input');
$json_body = null;
if (!empty($raw_body)) {
    $json_body = json_decode($raw_body, true);
}

// Capturar GET y POST data
$get_data = $_GET;
$post_data = $_POST;

// Log para archivo (opcional)
$log_entry = [
    'timestamp' => date('Y-m-d H:i:s'),
    'method' => $request_info['method'],
    'uri' => $request_info['uri'],
    'user_agent' => $request_info['user_agent'],
    'body_length' => strlen($raw_body),
    'has_json' => $json_body !== null
];

// Guardar en log file
file_put_contents(__DIR__ . '/debug_requests.log', json_encode($log_entry) . "\n", FILE_APPEND);

// Respuesta completa
echo json_encode([
    'success' => true,
    'message' => 'Request captured successfully',
    'debug_info' => [
        'request' => $request_info,
        'headers' => $headers,
        'get_data' => $get_data,
        'post_data' => $post_data,
        'raw_body' => $raw_body,
        'json_body' => $json_body,
        'body_length' => strlen($raw_body)
    ]
], JSON_PRETTY_PRINT);
?>
