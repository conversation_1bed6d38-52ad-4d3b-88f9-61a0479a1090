<?php
session_start();
require_once 'config.php';
require_once 'ticket_notifications.php';

// Verificar si el usuario está logueado como admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

$ticket_id = (int)($_GET['id'] ?? 0);
if (!$ticket_id) {
    header('Location: tickets_admin.php');
    exit;
}

// Procesar respuesta
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_response'])) {
    try {
        $message = clean_input($_POST['message']);
        $admin_id = $_SESSION['admin_id'] ?? 2; // ID del admin por defecto

        if (!empty($message)) {
            // Detectar qué columna usar para admin
            $admin_column = 'is_admin_response'; // Por defecto
            try {
                $stmt = $pdo->query("SHOW COLUMNS FROM ticket_responses");
                $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);

                if (in_array('is_admin', $columns)) {
                    $admin_column = 'is_admin';
                } elseif (in_array('is_admin_response', $columns)) {
                    $admin_column = 'is_admin_response';
                }
            } catch (Exception $e) {
                // Si hay error, usar el valor por defecto
            }

            $stmt = $pdo->prepare("INSERT INTO ticket_responses (ticket_id, user_id, message, $admin_column) VALUES (?, ?, ?, 1)");
            $stmt->execute([$ticket_id, $admin_id, $message]);

            // Actualizar timestamp del ticket
            $stmt = $pdo->prepare("UPDATE support_tickets SET updated_at = NOW() WHERE id = ?");
            $stmt->execute([$ticket_id]);

            // Obtener información del ticket para la notificación
            $stmt = $pdo->prepare("SELECT user_id FROM support_tickets WHERE id = ?");
            $stmt->execute([$ticket_id]);
            $ticket_info = $stmt->fetch(PDO::FETCH_ASSOC);

            // Crear notificación para el usuario del ticket
            if ($ticket_info && $ticket_info['user_id']) {
                $admin_username = $_SESSION['admin_username'] ?? 'Soporte';
                createTicketResponseNotification($pdo, $ticket_id, $ticket_info['user_id'], $admin_username);
            }

            // Crear notificación para el admin (opcional, solo si la tabla existe)
            try {
                $stmt = $pdo->query("SHOW TABLES LIKE 'admin_notifications'");
                if ($stmt->rowCount() > 0) {
                    // Verificar si tiene las columnas necesarias
                    $stmt = $pdo->query("SHOW COLUMNS FROM admin_notifications");
                    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);

                    if (in_array('reference_id', $columns) && in_array('reference_type', $columns)) {
                        $stmt = $pdo->prepare("INSERT INTO admin_notifications (type, title, message, reference_id, reference_type) VALUES (?, ?, ?, ?, ?)");
                        $stmt->execute(['ticket', 'Respuesta agregada', 'Se agregó una respuesta al ticket #' . $ticket_id, $ticket_id, 'support_tickets']);
                    } else {
                        // Tabla existe pero sin las columnas necesarias, usar versión simple
                        $stmt = $pdo->prepare("INSERT INTO admin_notifications (type, title, message) VALUES (?, ?, ?)");
                        $stmt->execute(['ticket', 'Respuesta agregada', 'Se agregó una respuesta al ticket #' . $ticket_id]);
                    }
                }
            } catch (Exception $e) {
                // Si hay error con notificaciones, continuar sin ellas
                error_log("Error creando notificación admin: " . $e->getMessage());
            }

            $success_message = "Respuesta agregada correctamente";
        } else {
            $error_message = "El mensaje no puede estar vacío";
        }
    } catch (Exception $e) {
        $error_message = "Error al agregar la respuesta: " . $e->getMessage();
    }
}

// Obtener ticket
try {
    $stmt = $pdo->prepare("
        SELECT st.*, u.username, u.email
        FROM support_tickets st
        LEFT JOIN users u ON st.user_id = u.id
        WHERE st.id = ?
    ");
    $stmt->execute([$ticket_id]);
    $ticket = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$ticket) {
        header('Location: tickets_admin.php');
        exit;
    }

    // Detectar qué columna de admin existe en ticket_responses
    $admin_column = 'is_admin_response'; // Por defecto
    try {
        $stmt = $pdo->query("SHOW COLUMNS FROM ticket_responses");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);

        if (in_array('is_admin', $columns)) {
            $admin_column = 'is_admin';
        } elseif (in_array('is_admin_response', $columns)) {
            $admin_column = 'is_admin_response';
        }
    } catch (Exception $e) {
        // Si hay error, usar el valor por defecto
    }

    // Obtener respuestas
    $stmt = $pdo->prepare("
        SELECT tr.*, u.username, tr.$admin_column as is_admin_response
        FROM ticket_responses tr
        LEFT JOIN users u ON tr.user_id = u.id
        WHERE tr.ticket_id = ?
        ORDER BY tr.created_at ASC
    ");
    $stmt->execute([$ticket_id]);
    $responses = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    die("Error al cargar el ticket: " . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎫 Ticket #<?php echo $ticket['id']; ?> - Admin Soporte</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e293b;
            --dark-bg: #0f172a;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --accent-color: #10b981;
            --support-color: #e91e63;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --success-color: #10b981;
            --border-color: #334155;
            --gradient-bg: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            --border-radius: 12px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gradient-bg);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
        }

        .header {
            background: var(--secondary-color);
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--support-color);
            text-decoration: none;
        }

        .nav-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .nav-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            background: transparent;
            color: var(--text-secondary);
            text-decoration: none;
            border-radius: var(--border-radius);
            transition: var(--transition);
            font-weight: 500;
        }

        .nav-btn:hover {
            background: rgba(255,255,255,0.1);
            color: var(--text-primary);
        }

        .nav-btn.back-btn {
            background: var(--primary-color);
            color: white;
        }

        .main-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem 1.5rem;
        }

        .ticket-header {
            background: var(--secondary-color);
            padding: 2rem;
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            margin-bottom: 2rem;
        }

        .ticket-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 1rem;
        }

        .ticket-meta {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--text-secondary);
        }

        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .status-open { background: rgba(59, 130, 246, 0.2); color: #3b82f6; }
        .status-in_progress { background: rgba(245, 158, 11, 0.2); color: #f59e0b; }
        .status-resolved { background: rgba(16, 185, 129, 0.2); color: #10b981; }

        .priority-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .priority-urgent { background: rgba(239, 68, 68, 0.2); color: #ef4444; }
        .priority-high { background: rgba(245, 158, 11, 0.2); color: #f59e0b; }
        .priority-medium { background: rgba(59, 130, 246, 0.2); color: #3b82f6; }
        .priority-low { background: rgba(107, 114, 128, 0.2); color: #6b7280; }

        .ticket-description {
            background: var(--dark-bg);
            padding: 1.5rem;
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            margin-bottom: 2rem;
        }

        .responses-section {
            margin-bottom: 2rem;
        }

        .section-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .response-item {
            background: var(--secondary-color);
            padding: 1.5rem;
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            margin-bottom: 1rem;
        }

        .response-admin {
            background: rgba(16, 185, 129, 0.1);
            border-color: rgba(16, 185, 129, 0.3);
        }

        .response-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .response-author {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 500;
        }

        .admin-badge {
            background: var(--accent-color);
            color: white;
            padding: 0.2rem 0.5rem;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 500;
        }

        .response-date {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .response-form {
            background: var(--secondary-color);
            padding: 2rem;
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .form-textarea {
            width: 100%;
            min-height: 120px;
            padding: 1rem;
            background: var(--dark-bg);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            font-family: inherit;
            font-size: 0.9rem;
            resize: vertical;
        }

        .form-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.15);
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: var(--transition);
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            opacity: 0.9;
        }

        .success-message {
            background: rgba(16, 185, 129, 0.2);
            color: var(--success-color);
            padding: 1rem;
            border-radius: var(--border-radius);
            margin-bottom: 1rem;
            border: 1px solid rgba(16, 185, 129, 0.3);
        }

        .error-message {
            background: rgba(239, 68, 68, 0.2);
            color: var(--error-color);
            padding: 1rem;
            border-radius: var(--border-radius);
            margin-bottom: 1rem;
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        .actions-bar {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        @media (max-width: 768px) {
            .ticket-meta {
                grid-template-columns: 1fr;
            }
            
            .response-header {
                flex-direction: column;
                align-items: flex-start;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-container">
            <a href="ticket_detail.php?id=<?php echo $ticket['id']; ?>" class="logo">
                <i class="fas fa-ticket-alt"></i>
                <span>Ticket #<?php echo $ticket['id']; ?></span>
            </a>
            
            <div class="nav-buttons">
                <a href="tickets_admin.php" class="nav-btn back-btn">
                    <i class="fas fa-arrow-left"></i>
                    <span>Volver a Tickets</span>
                </a>
                <a href="admin2.php" class="nav-btn">
                    <i class="fas fa-headset"></i>
                    <span>Admin Soporte</span>
                </a>
            </div>
        </div>
    </header>

    <main class="main-content">
        <?php if (isset($success_message)): ?>
        <div class="success-message">
            <i class="fas fa-check-circle"></i>
            <?php echo htmlspecialchars($success_message); ?>
        </div>
        <?php endif; ?>

        <?php if (isset($error_message)): ?>
        <div class="error-message">
            <i class="fas fa-exclamation-circle"></i>
            <?php echo htmlspecialchars($error_message); ?>
        </div>
        <?php endif; ?>

        <!-- Header del Ticket -->
        <div class="ticket-header">
            <h1 class="ticket-title">
                <?php echo htmlspecialchars($ticket['title']); ?>
            </h1>
            
            <div class="ticket-meta">
                <div class="meta-item">
                    <i class="fas fa-user"></i>
                    <span><?php echo htmlspecialchars($ticket['username'] ?? 'Usuario desconocido'); ?></span>
                </div>
                <div class="meta-item">
                    <i class="fas fa-envelope"></i>
                    <span><?php echo htmlspecialchars($ticket['email'] ?? 'Sin email'); ?></span>
                </div>
                <div class="meta-item">
                    <i class="fas fa-calendar"></i>
                    <span><?php echo date('d/m/Y H:i', strtotime($ticket['created_at'])); ?></span>
                </div>
                <div class="meta-item">
                    <i class="fas fa-tag"></i>
                    <span><?php echo ucfirst($ticket['category']); ?></span>
                </div>
            </div>
            
            <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
                <span class="status-badge status-<?php echo $ticket['status']; ?>">
                    <?php 
                    $status_labels = [
                        'open' => 'Abierto',
                        'in_progress' => 'En Progreso',
                        'waiting_user' => 'Esperando Usuario',
                        'resolved' => 'Resuelto',
                        'closed' => 'Cerrado'
                    ];
                    echo $status_labels[$ticket['status']] ?? $ticket['status'];
                    ?>
                </span>
                <span class="priority-badge priority-<?php echo $ticket['priority']; ?>">
                    Prioridad: <?php 
                    $priority_labels = [
                        'low' => 'Baja',
                        'medium' => 'Media',
                        'high' => 'Alta',
                        'urgent' => 'Urgente'
                    ];
                    echo $priority_labels[$ticket['priority']] ?? $ticket['priority'];
                    ?>
                </span>
            </div>
        </div>

        <!-- Acciones Rápidas -->
        <div class="actions-bar">
            <?php if ($ticket['status'] !== 'resolved' && $ticket['status'] !== 'closed'): ?>
            <form method="POST" action="tickets_admin.php" style="display: inline;">
                <input type="hidden" name="action" value="update_status">
                <input type="hidden" name="ticket_id" value="<?php echo $ticket['id']; ?>">
                <input type="hidden" name="status" value="in_progress">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-play"></i>
                    Marcar En Progreso
                </button>
            </form>
            
            <form method="POST" action="tickets_admin.php" style="display: inline;">
                <input type="hidden" name="action" value="update_status">
                <input type="hidden" name="ticket_id" value="<?php echo $ticket['id']; ?>">
                <input type="hidden" name="status" value="resolved">
                <button type="submit" class="btn btn-success">
                    <i class="fas fa-check"></i>
                    Marcar Resuelto
                </button>
            </form>
            <?php endif; ?>
        </div>

        <!-- Descripción del Ticket -->
        <div class="ticket-description">
            <h2 class="section-title">
                <i class="fas fa-file-alt"></i>
                Descripción del Problema
            </h2>
            <div style="white-space: pre-wrap;"><?php echo htmlspecialchars($ticket['description']); ?></div>
        </div>

        <!-- Respuestas -->
        <div class="responses-section">
            <h2 class="section-title">
                <i class="fas fa-comments"></i>
                Conversación (<?php echo count($responses); ?> respuestas)
            </h2>
            
            <?php if (empty($responses)): ?>
            <div style="text-align: center; color: var(--text-secondary); padding: 2rem;">
                <i class="fas fa-comment-slash" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                <p>Aún no hay respuestas para este ticket</p>
            </div>
            <?php else: ?>
                <?php foreach ($responses as $response): ?>
                <div class="response-item <?php echo $response['is_admin_response'] ? 'response-admin' : ''; ?>">
                    <div class="response-header">
                        <div class="response-author">
                            <i class="fas fa-<?php echo $response['is_admin_response'] ? 'user-shield' : 'user'; ?>"></i>
                            <span><?php echo htmlspecialchars($response['username'] ?? 'Usuario'); ?></span>
                            <?php if ($response['is_admin_response']): ?>
                            <span class="admin-badge">ADMIN</span>
                            <?php endif; ?>
                        </div>
                        <div class="response-date">
                            <?php echo date('d/m/Y H:i', strtotime($response['created_at'])); ?>
                        </div>
                    </div>
                    <div style="white-space: pre-wrap;"><?php echo htmlspecialchars($response['message']); ?></div>
                </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>

        <!-- Formulario de Respuesta -->
        <?php if ($ticket['status'] !== 'closed'): ?>
        <div class="response-form">
            <h2 class="section-title">
                <i class="fas fa-reply"></i>
                Agregar Respuesta
            </h2>
            
            <form method="POST">
                <div class="form-group">
                    <label for="message" class="form-label">Mensaje de Respuesta</label>
                    <textarea 
                        id="message" 
                        name="message" 
                        class="form-textarea" 
                        placeholder="Escribe tu respuesta aquí..."
                        required
                    ></textarea>
                </div>
                
                <button type="submit" name="add_response" class="btn btn-success">
                    <i class="fas fa-paper-plane"></i>
                    Enviar Respuesta
                </button>
            </form>
        </div>
        <?php endif; ?>
    </main>

    <script>
        // Auto-resize textarea
        const textarea = document.querySelector('.form-textarea');
        if (textarea) {
            textarea.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = this.scrollHeight + 'px';
            });
        }

        // Confirmación para cambios de estado
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', function(e) {
                const action = this.querySelector('input[name="action"]')?.value;
                if (action === 'update_status') {
                    const status = this.querySelector('input[name="status"]')?.value;
                    if (status === 'resolved') {
                        if (!confirm('¿Estás seguro de que quieres marcar este ticket como resuelto?')) {
                            e.preventDefault();
                        }
                    }
                }
            });
        });

        // Scroll suave a la sección de respuesta
        const responseForm = document.querySelector('.response-form');
        if (responseForm && window.location.hash === '#respond') {
            responseForm.scrollIntoView({ behavior: 'smooth' });
        }
    </script>
</body>
</html>
