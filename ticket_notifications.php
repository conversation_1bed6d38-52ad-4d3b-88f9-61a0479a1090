<?php
/**
 * Funciones para manejar notificaciones de tickets
 * Incluir este archivo donde se necesiten las funciones de notificación
 */

/**
 * Crear notificación para el usuario cuando se responde un ticket
 */
function createTicketResponseNotification($pdo, $ticket_id, $user_id, $admin_username = 'Soporte') {
    try {
        // Verificar que la tabla user_notifications existe
        $stmt = $pdo->query("SHOW TABLES LIKE 'user_notifications'");
        if ($stmt->rowCount() == 0) {
            error_log("Tabla user_notifications no existe. No se puede crear notificación.");
            return false;
        }
        
        // Obtener información del ticket
        $stmt = $pdo->prepare("SELECT title, status FROM support_tickets WHERE id = ?");
        $stmt->execute([$ticket_id]);
        $ticket = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$ticket) {
            error_log("Ticket #$ticket_id no encontrado para notificación.");
            return false;
        }
        
        $title = "Respuesta a tu ticket #$ticket_id";
        $message = "El equipo de $admin_username ha respondido a tu ticket \"" . substr($ticket['title'], 0, 50) . "...\". Revisa la respuesta en la sección de tickets.";
        
        // Crear la notificación
        $stmt = $pdo->prepare("
            INSERT INTO user_notifications (user_id, type, title, message, reference_id, reference_type) 
            VALUES (?, 'ticket_response', ?, ?, ?, 'support_tickets')
        ");
        $stmt->execute([$user_id, $title, $message, $ticket_id]);
        
        return true;
        
    } catch (Exception $e) {
        error_log("Error creando notificación de ticket: " . $e->getMessage());
        return false;
    }
}

/**
 * Crear notificación cuando cambia el estado de un ticket
 */
function createTicketStatusNotification($pdo, $ticket_id, $user_id, $old_status, $new_status) {
    try {
        // Verificar que la tabla user_notifications existe
        $stmt = $pdo->query("SHOW TABLES LIKE 'user_notifications'");
        if ($stmt->rowCount() == 0) {
            return false;
        }
        
        // Solo notificar cambios importantes
        $important_statuses = ['resolved', 'closed', 'in_progress'];
        if (!in_array($new_status, $important_statuses)) {
            return false;
        }
        
        // Obtener información del ticket
        $stmt = $pdo->prepare("SELECT title FROM support_tickets WHERE id = ?");
        $stmt->execute([$ticket_id]);
        $ticket = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$ticket) {
            return false;
        }
        
        // Mensajes según el estado
        $status_messages = [
            'resolved' => [
                'title' => "Ticket #$ticket_id resuelto",
                'message' => "Tu ticket \"" . substr($ticket['title'], 0, 50) . "...\" ha sido marcado como resuelto. Si necesitas más ayuda, puedes responder al ticket."
            ],
            'closed' => [
                'title' => "Ticket #$ticket_id cerrado",
                'message' => "Tu ticket \"" . substr($ticket['title'], 0, 50) . "...\" ha sido cerrado. Gracias por contactarnos."
            ],
            'in_progress' => [
                'title' => "Ticket #$ticket_id en progreso",
                'message' => "Tu ticket \"" . substr($ticket['title'], 0, 50) . "...\" está siendo atendido por nuestro equipo."
            ]
        ];
        
        if (!isset($status_messages[$new_status])) {
            return false;
        }
        
        $notification = $status_messages[$new_status];
        
        // Crear la notificación
        $stmt = $pdo->prepare("
            INSERT INTO user_notifications (user_id, type, title, message, reference_id, reference_type) 
            VALUES (?, 'ticket_status', ?, ?, ?, 'support_tickets')
        ");
        $stmt->execute([$user_id, $notification['title'], $notification['message'], $ticket_id]);
        
        return true;
        
    } catch (Exception $e) {
        error_log("Error creando notificación de estado: " . $e->getMessage());
        return false;
    }
}

/**
 * Obtener notificaciones no leídas de un usuario
 */
function getUserNotifications($pdo, $user_id, $limit = 20) {
    try {
        $stmt = $pdo->prepare("
            SELECT * FROM user_notifications 
            WHERE user_id = ? AND is_read = 0 
            ORDER BY created_at DESC 
            LIMIT ?
        ");
        $stmt->execute([$user_id, $limit]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
        
    } catch (Exception $e) {
        error_log("Error obteniendo notificaciones: " . $e->getMessage());
        return [];
    }
}

/**
 * Contar notificaciones no leídas de un usuario
 */
function countUnreadNotifications($pdo, $user_id) {
    try {
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as count FROM user_notifications 
            WHERE user_id = ? AND is_read = 0
        ");
        $stmt->execute([$user_id]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return (int)$result['count'];
        
    } catch (Exception $e) {
        error_log("Error contando notificaciones: " . $e->getMessage());
        return 0;
    }
}

/**
 * Marcar notificaciones como leídas
 */
function markNotificationsAsRead($pdo, $user_id, $notification_ids = null) {
    try {
        if ($notification_ids && is_array($notification_ids)) {
            // Marcar notificaciones específicas
            $placeholders = str_repeat('?,', count($notification_ids) - 1) . '?';
            $stmt = $pdo->prepare("
                UPDATE user_notifications 
                SET is_read = 1, read_at = NOW() 
                WHERE user_id = ? AND id IN ($placeholders)
            ");
            $params = array_merge([$user_id], $notification_ids);
            $stmt->execute($params);
        } else {
            // Marcar todas como leídas
            $stmt = $pdo->prepare("
                UPDATE user_notifications 
                SET is_read = 1, read_at = NOW() 
                WHERE user_id = ? AND is_read = 0
            ");
            $stmt->execute([$user_id]);
        }
        
        return true;
        
    } catch (Exception $e) {
        error_log("Error marcando notificaciones como leídas: " . $e->getMessage());
        return false;
    }
}

/**
 * Limpiar notificaciones antiguas (más de 30 días)
 */
function cleanOldNotifications($pdo, $days = 30) {
    try {
        $stmt = $pdo->prepare("
            DELETE FROM user_notifications 
            WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY) AND is_read = 1
        ");
        $stmt->execute([$days]);
        
        return $stmt->rowCount();
        
    } catch (Exception $e) {
        error_log("Error limpiando notificaciones antiguas: " . $e->getMessage());
        return 0;
    }
}

/**
 * Obtener estadísticas de notificaciones para admin
 */
function getNotificationStats($pdo) {
    try {
        $stats = [];
        
        // Total de notificaciones
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM user_notifications");
        $stats['total'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        // No leídas
        $stmt = $pdo->query("SELECT COUNT(*) as unread FROM user_notifications WHERE is_read = 0");
        $stats['unread'] = $stmt->fetch(PDO::FETCH_ASSOC)['unread'];
        
        // Por tipo
        $stmt = $pdo->query("
            SELECT type, COUNT(*) as count 
            FROM user_notifications 
            GROUP BY type 
            ORDER BY count DESC
        ");
        $stats['by_type'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Últimas 24 horas
        $stmt = $pdo->query("
            SELECT COUNT(*) as recent 
            FROM user_notifications 
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ");
        $stats['recent'] = $stmt->fetch(PDO::FETCH_ASSOC)['recent'];
        
        return $stats;
        
    } catch (Exception $e) {
        error_log("Error obteniendo estadísticas: " . $e->getMessage());
        return [];
    }
}
?>
