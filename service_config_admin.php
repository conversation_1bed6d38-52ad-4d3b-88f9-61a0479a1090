<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Configuración de Servicios - Admin</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #10b981;
            --primary-dark: #059669;
            --secondary-color: #6b7280;
            --background-dark: #0f172a;
            --surface-dark: #1e293b;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --border-color: rgba(255, 255, 255, 0.1);
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe <PERSON>I', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--background-dark);
            color: var(--text-primary);
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
            padding: 2rem;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(16, 185, 129, 0.3);
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .service-card {
            background: var(--surface-dark);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            transition: all 0.3s ease;
        }

        .service-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.4);
        }

        .service-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .service-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .service-status {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-active {
            background: rgba(16, 185, 129, 0.2);
            color: var(--success-color);
        }

        .status-inactive {
            background: rgba(239, 68, 68, 0.2);
            color: var(--error-color);
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: var(--text-secondary);
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 0.75rem;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            color: var(--text-primary);
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: var(--primary-color);
            background: rgba(255, 255, 255, 0.08);
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .features-list {
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .feature-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
            padding: 0.5rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 6px;
        }

        .feature-item input {
            flex: 1;
            margin: 0;
            background: transparent;
            border: none;
            color: var(--text-primary);
        }

        .feature-item input:focus {
            outline: none;
            background: rgba(255, 255, 255, 0.1);
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
            font-size: 0.95rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
        }

        .btn-secondary {
            background: var(--secondary-color);
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
            transform: translateY(-2px);
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-success:hover {
            background: #059669;
            transform: translateY(-2px);
        }

        .btn-small {
            padding: 0.5rem 1rem;
            font-size: 0.85rem;
        }

        .actions {
            display: flex;
            gap: 1rem;
            margin-top: 2rem;
            padding-top: 1.5rem;
            border-top: 1px solid var(--border-color);
        }

        .back-link {
            position: fixed;
            top: 2rem;
            left: 2rem;
            z-index: 1000;
        }

        .toast {
            position: fixed;
            top: 2rem;
            right: 2rem;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            color: white;
            font-weight: 600;
            z-index: 10000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            max-width: 400px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        }

        .toast.show {
            transform: translateX(0);
        }

        .toast.success {
            background: var(--success-color);
        }

        .toast.error {
            background: var(--error-color);
        }

        .toast.warning {
            background: var(--warning-color);
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            .services-grid {
                grid-template-columns: 1fr;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .actions {
                flex-direction: column;
            }

            .back-link {
                position: static;
                margin-bottom: 1rem;
            }
        }
    </style>
</head>
<body>
    <a href="admin.php" class="back-link btn btn-secondary">
        <i class="fas fa-arrow-left"></i>
        Volver al Admin
    </a>

    <div class="container">
        <div class="header">
            <h1><i class="fas fa-cogs"></i> Configuración de Servicios</h1>
            <p>Edita precios, promociones y características de los servicios IPTV</p>
        </div>

        <div id="servicesContainer" class="services-grid">
            <!-- Los servicios se cargarán aquí dinámicamente -->
        </div>

        <div style="text-align: center; margin-top: 3rem;">
            <button onclick="saveAllConfigs()" class="btn btn-success">
                <i class="fas fa-save"></i>
                Guardar Todos los Cambios
            </button>
            <a href="service_options.php" class="btn btn-primary">
                <i class="fas fa-eye"></i>
                Ver Servicios Públicos
            </a>
        </div>
    </div>

    <script>
        let configs = {};

        // Cargar configuraciones al inicio
        document.addEventListener('DOMContentLoaded', function() {
            loadConfigs();
        });

        async function loadConfigs() {
            try {
                const response = await fetch('api_service_config.php?action=get_all');
                const data = await response.json();
                
                if (data.success) {
                    configs = {};
                    data.configs.forEach(config => {
                        configs[config.service_type] = config;
                    });
                    renderConfigs();
                } else {
                    showToast('Error cargando configuraciones: ' + data.error, 'error');
                }
            } catch (error) {
                showToast('Error de conexión: ' + error.message, 'error');
            }
        }

        function renderConfigs() {
            const container = document.getElementById('servicesContainer');
            container.innerHTML = '';

            Object.values(configs).forEach(config => {
                const features = JSON.parse(config.features || '[]');
                
                const serviceCard = document.createElement('div');
                serviceCard.className = 'service-card';
                serviceCard.innerHTML = `
                    <div class="service-header">
                        <h3 class="service-title">${config.title}</h3>
                        <span class="service-status ${config.is_active ? 'status-active' : 'status-inactive'}">
                            ${config.is_active ? 'Activo' : 'Inactivo'}
                        </span>
                    </div>

                    <div class="form-group">
                        <label>Título del Servicio</label>
                        <input type="text" value="${config.title}" onchange="updateConfig('${config.service_type}', 'title', this.value)">
                    </div>

                    <div class="form-group">
                        <label>Subtítulo</label>
                        <input type="text" value="${config.subtitle || ''}" onchange="updateConfig('${config.service_type}', 'subtitle', this.value)">
                    </div>

                    <div class="form-group">
                        <label>Descripción</label>
                        <textarea onchange="updateConfig('${config.service_type}', 'description', this.value)">${config.description || ''}</textarea>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label>Precio Actual</label>
                            <input type="text" value="${config.price || ''}" onchange="updateConfig('${config.service_type}', 'price', this.value)">
                        </div>
                        <div class="form-group">
                            <label>Precio Original</label>
                            <input type="text" value="${config.original_price || ''}" onchange="updateConfig('${config.service_type}', 'original_price', this.value)">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label>Descuento (%)</label>
                            <input type="number" min="0" max="100" value="${config.discount_percentage || 0}" onchange="updateConfig('${config.service_type}', 'discount_percentage', this.value)">
                        </div>
                        <div class="form-group">
                            <label>Estado</label>
                            <select onchange="updateConfig('${config.service_type}', 'is_active', this.value === '1')">
                                <option value="1" ${config.is_active ? 'selected' : ''}>Activo</option>
                                <option value="0" ${!config.is_active ? 'selected' : ''}>Inactivo</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Texto del Botón</label>
                        <input type="text" value="${config.button_text || ''}" onchange="updateConfig('${config.service_type}', 'button_text', this.value)">
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label>Texto de Promoción</label>
                            <input type="text" value="${config.promotion_text || ''}" onchange="updateConfig('${config.service_type}', 'promotion_text', this.value)">
                        </div>
                        <div class="form-group">
                            <label>Badge de Promoción</label>
                            <input type="text" value="${config.promotion_badge || ''}" onchange="updateConfig('${config.service_type}', 'promotion_badge', this.value)">
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Características del Servicio</label>
                        <div class="features-list" id="features-${config.service_type}">
                            ${features.map((feature, index) => `
                                <div class="feature-item">
                                    <i class="fas fa-check" style="color: var(--success-color);"></i>
                                    <input type="text" value="${feature}" onchange="updateFeature('${config.service_type}', ${index}, this.value)">
                                    <button type="button" class="btn btn-small btn-secondary" onclick="removeFeature('${config.service_type}', ${index})">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            `).join('')}
                            <button type="button" class="btn btn-small btn-primary" onclick="addFeature('${config.service_type}')">
                                <i class="fas fa-plus"></i>
                                Agregar Característica
                            </button>
                        </div>
                    </div>

                    <div class="actions">
                        <button onclick="saveConfig('${config.service_type}')" class="btn btn-success">
                            <i class="fas fa-save"></i>
                            Guardar
                        </button>
                        <button onclick="previewService('${config.service_type}')" class="btn btn-primary">
                            <i class="fas fa-eye"></i>
                            Vista Previa
                        </button>
                    </div>
                `;
                
                container.appendChild(serviceCard);
            });
        }

        function updateConfig(serviceType, field, value) {
            if (!configs[serviceType]) return;
            configs[serviceType][field] = value;
        }

        function updateFeature(serviceType, index, value) {
            if (!configs[serviceType]) return;
            const features = JSON.parse(configs[serviceType].features || '[]');
            features[index] = value;
            configs[serviceType].features = JSON.stringify(features);
        }

        function addFeature(serviceType) {
            if (!configs[serviceType]) return;
            const features = JSON.parse(configs[serviceType].features || '[]');
            features.push('Nueva característica');
            configs[serviceType].features = JSON.stringify(features);
            renderConfigs();
        }

        function removeFeature(serviceType, index) {
            if (!configs[serviceType]) return;
            const features = JSON.parse(configs[serviceType].features || '[]');
            features.splice(index, 1);
            configs[serviceType].features = JSON.stringify(features);
            renderConfigs();
        }

        async function saveConfig(serviceType) {
            try {
                const config = configs[serviceType];
                console.log('💾 Guardando configuración para:', serviceType);
                console.log('📦 Datos a enviar:', config);

                const requestData = {
                    action: 'update',
                    service_type: serviceType,
                    config: config
                };

                console.log('🚀 Request completo:', requestData);

                const response = await fetch('api_service_config.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });

                console.log('📡 Response status:', response.status);
                console.log('📡 Response headers:', response.headers.get('content-type'));

                const responseText = await response.text();
                console.log('📄 Response text:', responseText);

                let result;
                try {
                    result = JSON.parse(responseText);
                } catch (parseError) {
                    console.error('❌ Error parseando JSON:', parseError);
                    throw new Error('Respuesta del servidor no es JSON válido: ' + responseText.substring(0, 100));
                }

                console.log('✅ Result parsed:', result);

                if (result.success) {
                    showToast(`Configuración de ${config.title} guardada exitosamente`, 'success');
                } else {
                    showToast('Error guardando configuración: ' + result.error, 'error');
                }
            } catch (error) {
                console.error('❌ Error completo:', error);
                showToast('Error de conexión: ' + error.message, 'error');
            }
        }

        async function saveAllConfigs() {
            const promises = Object.keys(configs).map(serviceType => saveConfig(serviceType));
            
            try {
                await Promise.all(promises);
                showToast('Todas las configuraciones guardadas exitosamente', 'success');
            } catch (error) {
                showToast('Error guardando algunas configuraciones', 'error');
            }
        }

        function previewService(serviceType) {
            // Abrir service_options.php en nueva pestaña
            window.open('service_options.php', '_blank');
        }

        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.textContent = message;
            
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.classList.add('show');
            }, 100);
            
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            }, 4000);
        }
    </script>
</body>
</html>
