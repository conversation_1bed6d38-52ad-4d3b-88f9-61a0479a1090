<?php
/**
 * API para obtener información de geolocalización de usuarios
 */

session_start();

// Verificar si es admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    http_response_code(403);
    die(json_encode(['error' => 'Acceso denegado']));
}

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    http_response_code(500);
    die(json_encode(['error' => 'Error de conexión a la base de datos']));
}

function getCountryByIP($ip) {
    if ($ip === '127.0.0.1' || $ip === '::1' || strpos($ip, '192.168.') === 0 || strpos($ip, '10.') === 0) {
        return [
            'country' => 'Local/Desarrollo',
            'countryCode' => 'LOCAL',
            'flag' => '🏠',
            'city' => 'Localhost',
            'region' => 'Desarrollo'
        ];
    }
    
    try {
        $url = "http://ip-api.com/json/{$ip}?fields=country,countryCode,city,regionName,status";
        $context = stream_context_create([
            'http' => [
                'timeout' => 5,
                'user_agent' => 'RGS Admin System'
            ]
        ]);
        
        $response = @file_get_contents($url, false, $context);
        
        if ($response) {
            $data = json_decode($response, true);
            if ($data && $data['status'] === 'success') {
                return [
                    'country' => $data['country'] ?? 'Desconocido',
                    'countryCode' => $data['countryCode'] ?? '',
                    'flag' => getFlagEmoji($data['countryCode'] ?? ''),
                    'city' => $data['city'] ?? 'Desconocida',
                    'region' => $data['regionName'] ?? 'Desconocida'
                ];
            }
        }
    } catch (Exception $e) {
        error_log("Error obteniendo geolocalización para IP $ip: " . $e->getMessage());
    }
    
    return [
        'country' => 'Desconocido',
        'countryCode' => '',
        'flag' => '🌍',
        'city' => 'Desconocida',
        'region' => 'Desconocida'
    ];
}

function getFlagEmoji($countryCode) {
    $flags = [
        'US' => '🇺🇸', 'MX' => '🇲🇽', 'GT' => '🇬🇹', 'BZ' => '🇧🇿', 'SV' => '🇸🇻',
        'HN' => '🇭🇳', 'NI' => '🇳🇮', 'CR' => '🇨🇷', 'PA' => '🇵🇦', 'CU' => '🇨🇺',
        'JM' => '🇯🇲', 'HT' => '🇭🇹', 'DO' => '🇩🇴', 'PR' => '🇵🇷', 'CO' => '🇨🇴',
        'VE' => '🇻🇪', 'GY' => '🇬🇾', 'SR' => '🇸🇷', 'GF' => '🇬🇫', 'BR' => '🇧🇷',
        'EC' => '🇪🇨', 'PE' => '🇵🇪', 'BO' => '🇧🇴', 'PY' => '🇵🇾', 'UY' => '🇺🇾',
        'AR' => '🇦🇷', 'CL' => '🇨🇱', 'ES' => '🇪🇸', 'FR' => '🇫🇷', 'IT' => '🇮🇹',
        'DE' => '🇩🇪', 'GB' => '🇬🇧', 'CA' => '🇨🇦', 'AU' => '🇦🇺', 'JP' => '🇯🇵',
        'CN' => '🇨🇳', 'IN' => '🇮🇳', 'RU' => '🇷🇺', 'KR' => '🇰🇷', 'TH' => '🇹🇭'
    ];
    return $flags[$countryCode] ?? '🌍';
}

if (isset($_GET['action'])) {
    switch ($_GET['action']) {
        case 'get_user_location':
            if (isset($_GET['user_id'])) {
                $user_id = $_GET['user_id'];
                
                // Obtener información del usuario y sus IPs de los logs de seguridad
                $stmt = $pdo->prepare("
                    SELECT 
                        u.username,
                        u.created_at,
                        u.last_login,
                        sl.ip_address,
                        sl.created_at as last_activity
                    FROM users u
                    LEFT JOIN security_logs sl ON u.id = sl.user_id
                    WHERE u.id = ?
                    ORDER BY sl.created_at DESC
                    LIMIT 1
                ");
                $stmt->execute([$user_id]);
                $user_data = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($user_data && $user_data['ip_address']) {
                    $location = getCountryByIP($user_data['ip_address']);
                    
                    echo json_encode([
                        'success' => true,
                        'user' => [
                            'username' => $user_data['username'],
                            'ip' => $user_data['ip_address'],
                            'last_activity' => $user_data['last_activity'],
                            'location' => $location
                        ]
                    ]);
                } else {
                    echo json_encode([
                        'success' => false,
                        'message' => 'No se encontró información de IP para este usuario'
                    ]);
                }
            } else {
                echo json_encode(['success' => false, 'message' => 'ID de usuario requerido']);
            }
            break;
            
        case 'get_all_user_locations':
            // Obtener las últimas IPs de todos los usuarios
            $stmt = $pdo->query("
                SELECT 
                    u.id,
                    u.username,
                    u.is_admin,
                    u.last_login,
                    (SELECT sl.ip_address 
                     FROM security_logs sl 
                     WHERE sl.user_id = u.id 
                     ORDER BY sl.created_at DESC 
                     LIMIT 1) as last_ip,
                    (SELECT sl.created_at 
                     FROM security_logs sl 
                     WHERE sl.user_id = u.id 
                     ORDER BY sl.created_at DESC 
                     LIMIT 1) as last_activity
                FROM users u
                ORDER BY u.created_at DESC
            ");
            $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $users_with_location = [];
            foreach ($users as $user) {
                if ($user['last_ip']) {
                    $location = getCountryByIP($user['last_ip']);
                    $user['location'] = $location;
                }
                $users_with_location[] = $user;
            }
            
            echo json_encode([
                'success' => true,
                'users' => $users_with_location
            ]);
            break;
            
        case 'get_login_history':
            if (isset($_GET['user_id'])) {
                $user_id = $_GET['user_id'];
                
                // Obtener historial de logins del usuario
                $stmt = $pdo->prepare("
                    SELECT 
                        ip_address,
                        action,
                        created_at,
                        details
                    FROM security_logs 
                    WHERE user_id = ? 
                    AND action IN ('login_success', 'login_failed', 'login_attempt')
                    ORDER BY created_at DESC 
                    LIMIT 50
                ");
                $stmt->execute([$user_id]);
                $login_history = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                // Agregar información de geolocalización a cada entrada
                $history_with_location = [];
                $ip_cache = [];
                
                foreach ($login_history as $entry) {
                    $ip = $entry['ip_address'];
                    
                    // Usar caché para evitar múltiples consultas a la misma IP
                    if (!isset($ip_cache[$ip])) {
                        $ip_cache[$ip] = getCountryByIP($ip);
                    }
                    
                    $entry['location'] = $ip_cache[$ip];
                    $history_with_location[] = $entry;
                }
                
                echo json_encode([
                    'success' => true,
                    'history' => $history_with_location
                ]);
            } else {
                echo json_encode(['success' => false, 'message' => 'ID de usuario requerido']);
            }
            break;
            
        default:
            echo json_encode(['success' => false, 'message' => 'Acción no válida']);
            break;
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Acción requerida']);
}
?>
