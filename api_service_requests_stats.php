<?php
// API para obtener estadísticas de solicitudes de servicio en tiempo real
session_start();
header('Content-Type: application/json');

// Configuración de base de datos
$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Obtener estadísticas generales de solicitudes de servicio
    $stmt = $pdo->query("
        SELECT 
            COUNT(*) as total,
            SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
            SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as in_progress,
            SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
            SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled,
            SUM(CASE WHEN service_type = 'trial' THEN 1 ELSE 0 END) as trials,
            SUM(CASE WHEN service_type = 'purchase' THEN 1 ELSE 0 END) as purchases,
            SUM(CASE WHEN service_type = 'renewal' THEN 1 ELSE 0 END) as renewals,
            SUM(CASE WHEN is_reseller = 1 THEN 1 ELSE 0 END) as resellers,
            SUM(CASE WHEN DATE(created_at) = CURDATE() THEN 1 ELSE 0 END) as today,
            SUM(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN 1 ELSE 0 END) as last_hour,
            SUM(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 ELSE 0 END) as last_24h
        FROM service_requests
    ");
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Obtener estadísticas por dispositivo
    $stmt = $pdo->query("
        SELECT 
            device_type,
            COUNT(*) as count
        FROM service_requests 
        WHERE device_type IS NOT NULL
        GROUP BY device_type
        ORDER BY count DESC
    ");
    $device_stats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Obtener solicitudes recientes (últimas 5)
    $stmt = $pdo->query("
        SELECT 
            id,
            first_name,
            last_name,
            email,
            service_type,
            device_type,
            status,
            created_at,
            is_reseller,
            renewal_period
        FROM service_requests 
        ORDER BY created_at DESC 
        LIMIT 5
    ");
    $recent_requests = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Obtener estadísticas de notificaciones
    $stmt = $pdo->query("
        SELECT 
            COUNT(*) as total_notifications,
            SUM(CASE WHEN is_read = 0 THEN 1 ELSE 0 END) as unread_notifications,
            SUM(CASE WHEN notification_type = 'service_activated' THEN 1 ELSE 0 END) as activations,
            SUM(CASE WHEN notification_type = 'credentials_sent' THEN 1 ELSE 0 END) as credentials_sent,
            SUM(CASE WHEN notification_type = 'payment_received' THEN 1 ELSE 0 END) as payments_received
        FROM client_notifications
    ");
    $notification_stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Obtener estadísticas de comunicaciones
    $stmt = $pdo->query("
        SELECT 
            COUNT(*) as total_communications,
            COUNT(DISTINCT request_id) as requests_with_communications,
            SUM(CASE WHEN sender_type = 'admin' THEN 1 ELSE 0 END) as admin_messages,
            SUM(CASE WHEN sender_type = 'client' THEN 1 ELSE 0 END) as client_messages,
            SUM(CASE WHEN DATE(created_at) = CURDATE() THEN 1 ELSE 0 END) as today_messages
        FROM service_communications
    ");
    $communication_stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Obtener estadísticas de promociones
    $stmt = $pdo->query("
        SELECT 
            COUNT(*) as total_promotions,
            SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_promotions,
            SUM(CASE WHEN wants_current_promo = 1 THEN 1 ELSE 0 END) as promo_requests
        FROM current_promotions cp
        LEFT JOIN service_requests sr ON sr.wants_current_promo = 1
    ");
    $promo_stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Calcular tendencias (comparar con período anterior)
    $stmt = $pdo->query("
        SELECT 
            SUM(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 ELSE 0 END) as this_week,
            SUM(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 14 DAY) AND created_at < DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 ELSE 0 END) as last_week
        FROM service_requests
    ");
    $trend_data = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $trend_percentage = 0;
    if ($trend_data['last_week'] > 0) {
        $trend_percentage = (($trend_data['this_week'] - $trend_data['last_week']) / $trend_data['last_week']) * 100;
    } elseif ($trend_data['this_week'] > 0) {
        $trend_percentage = 100; // 100% de incremento si no había solicitudes la semana pasada
    }
    
    // Preparar respuesta
    $response = [
        'success' => true,
        'stats' => [
            // Estadísticas principales
            'total' => (int)$stats['total'],
            'pending' => (int)$stats['pending'],
            'in_progress' => (int)$stats['in_progress'],
            'completed' => (int)$stats['completed'],
            'cancelled' => (int)$stats['cancelled'],
            
            // Por tipo de servicio
            'trials' => (int)$stats['trials'],
            'purchases' => (int)$stats['purchases'],
            'renewals' => (int)$stats['renewals'],
            'resellers' => (int)$stats['resellers'],
            
            // Por tiempo
            'today' => (int)$stats['today'],
            'last_hour' => (int)$stats['last_hour'],
            'last_24h' => (int)$stats['last_24h'],
            
            // Tendencias
            'this_week' => (int)$trend_data['this_week'],
            'last_week' => (int)$trend_data['last_week'],
            'trend_percentage' => round($trend_percentage, 1),
            'trend_direction' => $trend_percentage > 0 ? 'up' : ($trend_percentage < 0 ? 'down' : 'stable'),
            
            // Notificaciones
            'total_notifications' => (int)$notification_stats['total_notifications'],
            'unread_notifications' => (int)$notification_stats['unread_notifications'],
            'activations' => (int)$notification_stats['activations'],
            'credentials_sent' => (int)$notification_stats['credentials_sent'],
            'payments_received' => (int)$notification_stats['payments_received'],
            
            // Comunicaciones
            'total_communications' => (int)$communication_stats['total_communications'],
            'requests_with_communications' => (int)$communication_stats['requests_with_communications'],
            'admin_messages' => (int)$communication_stats['admin_messages'],
            'client_messages' => (int)$communication_stats['client_messages'],
            'today_messages' => (int)$communication_stats['today_messages'],
            
            // Promociones
            'total_promotions' => (int)$promo_stats['total_promotions'],
            'active_promotions' => (int)$promo_stats['active_promotions'],
            'promo_requests' => (int)$promo_stats['promo_requests']
        ],
        'device_stats' => $device_stats,
        'recent_requests' => $recent_requests,
        'timestamp' => time(),
        'server_time' => date('Y-m-d H:i:s')
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Error al obtener estadísticas: ' . $e->getMessage(),
        'timestamp' => time()
    ]);
}
?>
