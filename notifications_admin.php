<?php
session_start();
require_once 'config.php';

// Verificar si el usuario está logueado como admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

// Procesar acciones
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'create_notification':
                $title = $_POST['title'];
                $message = $_POST['message'];
                $type = $_POST['type'];
                $target_users = $_POST['target_users'];
                $expires_days = (int)$_POST['expires_days'];
                $admin_id = $_SESSION['admin_id'] ?? 1;
                
                $expires_at = $expires_days > 0 ? date('Y-m-d H:i:s', strtotime("+$expires_days days")) : null;
                
                $stmt = $pdo->prepare("INSERT INTO system_notifications (title, message, type, target_users, expires_at, created_by) VALUES (?, ?, ?, ?, ?, ?)");
                $stmt->execute([$title, $message, $type, $target_users, $expires_at, $admin_id]);
                
                $success_message = "Notificación creada correctamente";
                break;
                
            case 'toggle_status':
                $notification_id = (int)$_POST['notification_id'];
                
                $stmt = $pdo->prepare("UPDATE system_notifications SET is_active = NOT is_active WHERE id = ?");
                $stmt->execute([$notification_id]);
                
                $success_message = "Estado de la notificación actualizado";
                break;
                
            case 'delete_notification':
                $notification_id = (int)$_POST['notification_id'];
                
                $stmt = $pdo->prepare("DELETE FROM system_notifications WHERE id = ?");
                $stmt->execute([$notification_id]);
                
                $success_message = "Notificación eliminada correctamente";
                break;
                
            case 'extend_expiry':
                $notification_id = (int)$_POST['notification_id'];
                $extend_days = (int)$_POST['extend_days'];
                
                $stmt = $pdo->prepare("UPDATE system_notifications SET expires_at = DATE_ADD(COALESCE(expires_at, NOW()), INTERVAL ? DAY) WHERE id = ?");
                $stmt->execute([$extend_days, $notification_id]);
                
                $success_message = "Fecha de expiración extendida por $extend_days días";
                break;
        }
    }
}

// Obtener filtros
$type_filter = $_GET['type'] ?? 'all';
$status_filter = $_GET['status'] ?? 'all';
$target_filter = $_GET['target'] ?? 'all';

// Construir consulta
$where_conditions = [];
$params = [];

if ($type_filter !== 'all') {
    $where_conditions[] = "sn.type = ?";
    $params[] = $type_filter;
}

if ($status_filter !== 'all') {
    if ($status_filter === 'active') {
        $where_conditions[] = "sn.is_active = 1 AND (sn.expires_at IS NULL OR sn.expires_at > NOW())";
    } elseif ($status_filter === 'inactive') {
        $where_conditions[] = "sn.is_active = 0";
    } elseif ($status_filter === 'expired') {
        $where_conditions[] = "sn.expires_at IS NOT NULL AND sn.expires_at <= NOW()";
    }
}

if ($target_filter !== 'all') {
    $where_conditions[] = "sn.target_users = ?";
    $params[] = $target_filter;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Obtener notificaciones
$stmt = $pdo->prepare("
    SELECT sn.*, u.username as created_by_name
    FROM system_notifications sn 
    LEFT JOIN users u ON sn.created_by = u.id 
    $where_clause
    ORDER BY sn.created_at DESC
");
$stmt->execute($params);
$notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Obtener estadísticas
$stats_stmt = $pdo->query("
    SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN is_active = 1 AND (expires_at IS NULL OR expires_at > NOW()) THEN 1 ELSE 0 END) as active,
        SUM(CASE WHEN is_active = 0 THEN 1 ELSE 0 END) as inactive,
        SUM(CASE WHEN expires_at IS NOT NULL AND expires_at <= NOW() THEN 1 ELSE 0 END) as expired,
        SUM(CASE WHEN type = 'info' THEN 1 ELSE 0 END) as info_count,
        SUM(CASE WHEN type = 'warning' THEN 1 ELSE 0 END) as warning_count,
        SUM(CASE WHEN type = 'error' THEN 1 ELSE 0 END) as error_count,
        SUM(CASE WHEN DATE(created_at) = CURDATE() THEN 1 ELSE 0 END) as today
    FROM system_notifications
");
$stats = $stats_stmt->fetch(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔔 Gestión de Notificaciones - Admin Soporte</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #1e293b;
            --dark-bg: #0f172a;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --accent-color: #10b981;
            --support-color: #e91e63;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --success-color: #10b981;
            --info-color: #06b6d4;
            --border-color: #334155;
            --gradient-bg: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            --border-radius: 12px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gradient-bg);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
        }

        .header {
            background: var(--secondary-color);
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--warning-color);
            text-decoration: none;
        }

        .nav-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .nav-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            background: transparent;
            color: var(--text-secondary);
            text-decoration: none;
            border-radius: var(--border-radius);
            transition: var(--transition);
            font-weight: 500;
        }

        .nav-btn:hover {
            background: rgba(255,255,255,0.1);
            color: var(--text-primary);
        }

        .nav-btn.back-btn {
            background: var(--primary-color);
            color: white;
        }

        .main-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem 1.5rem;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .page-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--secondary-color);
            padding: 1.5rem;
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            text-align: center;
            position: relative;
        }

        .stat-card.info::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--info-color);
        }

        .stat-card.warning::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--warning-color);
        }

        .stat-card.error::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--error-color);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--accent-color);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .filters {
            background: var(--secondary-color);
            padding: 1.5rem;
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            margin-bottom: 2rem;
        }

        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .filter-label {
            font-weight: 500;
            color: var(--text-primary);
        }

        .filter-select {
            padding: 0.75rem;
            background: var(--dark-bg);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            font-size: 0.9rem;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: var(--transition);
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-warning {
            background: var(--warning-color);
            color: white;
        }

        .btn-danger {
            background: var(--error-color);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            opacity: 0.9;
        }

        .create-form {
            background: var(--secondary-color);
            padding: 2rem;
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            margin-bottom: 2rem;
            display: none;
        }

        .create-form.active {
            display: block;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .form-label {
            font-weight: 500;
            color: var(--text-primary);
        }

        .form-input, .form-select, .form-textarea {
            padding: 0.75rem;
            background: var(--dark-bg);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            font-family: inherit;
            font-size: 0.9rem;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.15);
        }

        .form-textarea {
            min-height: 120px;
            resize: vertical;
        }

        .notifications-container {
            background: var(--secondary-color);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            overflow: hidden;
        }

        .notification-item {
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
            transition: var(--transition);
            position: relative;
        }

        .notification-item:hover {
            background: rgba(255,255,255,0.05);
        }

        .notification-item:last-child {
            border-bottom: none;
        }

        .notification-info {
            border-left: 4px solid var(--info-color);
        }

        .notification-warning {
            border-left: 4px solid var(--warning-color);
        }

        .notification-error {
            border-left: 4px solid var(--error-color);
        }

        .notification-success {
            border-left: 4px solid var(--success-color);
        }

        .notification-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .notification-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .notification-meta {
            display: flex;
            gap: 1rem;
            color: var(--text-secondary);
            font-size: 0.9rem;
            flex-wrap: wrap;
        }

        .type-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .type-info { 
            background: rgba(6, 182, 212, 0.2); 
            color: var(--info-color); 
        }

        .type-warning { 
            background: rgba(245, 158, 11, 0.2); 
            color: var(--warning-color); 
        }

        .type-error { 
            background: rgba(239, 68, 68, 0.2); 
            color: var(--error-color); 
        }

        .type-success { 
            background: rgba(16, 185, 129, 0.2); 
            color: var(--success-color); 
        }

        .status-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-active { 
            background: rgba(16, 185, 129, 0.2); 
            color: var(--success-color); 
        }

        .status-inactive { 
            background: rgba(107, 114, 128, 0.2); 
            color: #6b7280; 
        }

        .status-expired { 
            background: rgba(239, 68, 68, 0.2); 
            color: var(--error-color); 
        }

        .notification-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
            margin-top: 1rem;
        }

        .notification-message {
            background: rgba(255,255,255,0.05);
            padding: 1rem;
            border-radius: var(--border-radius);
            margin: 1rem 0;
            line-height: 1.6;
        }

        .success-message {
            background: rgba(16, 185, 129, 0.2);
            color: var(--success-color);
            padding: 1rem;
            border-radius: var(--border-radius);
            margin-bottom: 1rem;
            border: 1px solid rgba(16, 185, 129, 0.3);
        }

        @media (max-width: 768px) {
            .page-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .notification-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .notification-actions {
                width: 100%;
                justify-content: flex-start;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-container">
            <a href="notifications_admin.php" class="logo">
                <i class="fas fa-bell"></i>
                <span>Gestión de Notificaciones</span>
            </a>
            
            <div class="nav-buttons">
                <a href="admin2.php" class="nav-btn back-btn">
                    <i class="fas fa-arrow-left"></i>
                    <span>Volver</span>
                </a>
                <a href="admin.php" class="nav-btn">
                    <i class="fas fa-cog"></i>
                    <span>Admin</span>
                </a>
            </div>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-bell"></i>
                Gestión de Notificaciones
            </h1>
            
            <button onclick="toggleCreateForm()" class="btn btn-success">
                <i class="fas fa-plus"></i>
                Crear Notificación
            </button>
        </div>

        <?php if (isset($success_message)): ?>
        <div class="success-message">
            <i class="fas fa-check-circle"></i>
            <?php echo htmlspecialchars($success_message); ?>
        </div>
        <?php endif; ?>

        <!-- Estadísticas -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['total']; ?></div>
                <div class="stat-label">Total Notificaciones</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['active']; ?></div>
                <div class="stat-label">Activas</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['inactive']; ?></div>
                <div class="stat-label">Inactivas</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['expired']; ?></div>
                <div class="stat-label">Expiradas</div>
            </div>
            <div class="stat-card info">
                <div class="stat-number"><?php echo $stats['info_count']; ?></div>
                <div class="stat-label">Información</div>
            </div>
            <div class="stat-card warning">
                <div class="stat-number"><?php echo $stats['warning_count']; ?></div>
                <div class="stat-label">Advertencias</div>
            </div>
            <div class="stat-card error">
                <div class="stat-number"><?php echo $stats['error_count']; ?></div>
                <div class="stat-label">Errores</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['today']; ?></div>
                <div class="stat-label">Hoy</div>
            </div>
        </div>

        <!-- Formulario para Crear Notificación -->
        <div id="createForm" class="create-form">
            <h2 style="margin-bottom: 1.5rem; color: var(--text-primary);">
                <i class="fas fa-plus"></i>
                Crear Nueva Notificación
            </h2>

            <form method="POST">
                <input type="hidden" name="action" value="create_notification">

                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">Título</label>
                        <input type="text" name="title" class="form-input" required placeholder="Título de la notificación">
                    </div>

                    <div class="form-group">
                        <label class="form-label">Tipo</label>
                        <select name="type" class="form-select" required>
                            <option value="">Seleccionar tipo</option>
                            <option value="info">Información</option>
                            <option value="warning">Advertencia</option>
                            <option value="error">Error</option>
                            <option value="success">Éxito</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label">Dirigido a</label>
                        <select name="target_users" class="form-select" required>
                            <option value="">Seleccionar destinatarios</option>
                            <option value="all">Todos los usuarios</option>
                            <option value="admins">Solo administradores</option>
                            <option value="specific">Usuarios específicos</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label">Días de Expiración</label>
                        <select name="expires_days" class="form-select">
                            <option value="0">Sin expiración</option>
                            <option value="1">1 día</option>
                            <option value="7" selected>7 días</option>
                            <option value="30">30 días</option>
                            <option value="90">90 días</option>
                        </select>
                    </div>
                </div>

                <div class="form-group" style="margin-bottom: 2rem;">
                    <label class="form-label">Mensaje</label>
                    <textarea name="message" class="form-textarea" required placeholder="Contenido de la notificación..."></textarea>
                </div>

                <div style="display: flex; gap: 1rem;">
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-bell"></i>
                        Crear Notificación
                    </button>
                    <button type="button" onclick="toggleCreateForm()" class="btn btn-warning">
                        <i class="fas fa-times"></i>
                        Cancelar
                    </button>
                </div>
            </form>
        </div>

        <!-- Filtros -->
        <div class="filters">
            <form method="GET" class="filters-grid">
                <div class="filter-group">
                    <label class="filter-label">Tipo</label>
                    <select name="type" class="filter-select" onchange="this.form.submit()">
                        <option value="all" <?php echo $type_filter === 'all' ? 'selected' : ''; ?>>Todos</option>
                        <option value="info" <?php echo $type_filter === 'info' ? 'selected' : ''; ?>>Información</option>
                        <option value="warning" <?php echo $type_filter === 'warning' ? 'selected' : ''; ?>>Advertencia</option>
                        <option value="error" <?php echo $type_filter === 'error' ? 'selected' : ''; ?>>Error</option>
                        <option value="success" <?php echo $type_filter === 'success' ? 'selected' : ''; ?>>Éxito</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label class="filter-label">Estado</label>
                    <select name="status" class="filter-select" onchange="this.form.submit()">
                        <option value="all" <?php echo $status_filter === 'all' ? 'selected' : ''; ?>>Todos</option>
                        <option value="active" <?php echo $status_filter === 'active' ? 'selected' : ''; ?>>Activas</option>
                        <option value="inactive" <?php echo $status_filter === 'inactive' ? 'selected' : ''; ?>>Inactivas</option>
                        <option value="expired" <?php echo $status_filter === 'expired' ? 'selected' : ''; ?>>Expiradas</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label class="filter-label">Destinatarios</label>
                    <select name="target" class="filter-select" onchange="this.form.submit()">
                        <option value="all" <?php echo $target_filter === 'all' ? 'selected' : ''; ?>>Todos</option>
                        <option value="all" <?php echo $target_filter === 'all' ? 'selected' : ''; ?>>Todos los usuarios</option>
                        <option value="admins" <?php echo $target_filter === 'admins' ? 'selected' : ''; ?>>Solo administradores</option>
                        <option value="specific" <?php echo $target_filter === 'specific' ? 'selected' : ''; ?>>Usuarios específicos</option>
                    </select>
                </div>
            </form>
        </div>

        <!-- Lista de Notificaciones -->
        <div class="notifications-container">
            <?php if (empty($notifications)): ?>
            <div class="notification-item" style="text-align: center; color: var(--text-secondary);">
                <i class="fas fa-bell-slash" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                <p>No hay notificaciones que coincidan con los filtros seleccionados</p>
            </div>
            <?php else: ?>
                <?php foreach ($notifications as $notification): ?>
                <?php
                $is_expired = $notification['expires_at'] && strtotime($notification['expires_at']) <= time();
                $is_active = $notification['is_active'] && !$is_expired;
                ?>
                <div class="notification-item notification-<?php echo $notification['type']; ?>">
                    <div class="notification-header">
                        <div>
                            <div class="notification-title">
                                <?php echo htmlspecialchars($notification['title']); ?>
                            </div>
                            <div class="notification-meta">
                                <span><i class="fas fa-user"></i> <?php echo htmlspecialchars($notification['created_by_name'] ?? 'Sistema'); ?></span>
                                <span><i class="fas fa-calendar"></i> <?php echo date('d/m/Y H:i', strtotime($notification['created_at'])); ?></span>
                                <span><i class="fas fa-users"></i>
                                    <?php
                                    $target_labels = [
                                        'all' => 'Todos los usuarios',
                                        'admins' => 'Solo administradores',
                                        'specific' => 'Usuarios específicos'
                                    ];
                                    echo $target_labels[$notification['target_users']] ?? $notification['target_users'];
                                    ?>
                                </span>
                                <?php if ($notification['expires_at']): ?>
                                <span><i class="fas fa-clock"></i>
                                    <?php
                                    if ($is_expired) {
                                        echo 'Expiró: ' . date('d/m/Y H:i', strtotime($notification['expires_at']));
                                    } else {
                                        $days_left = ceil((strtotime($notification['expires_at']) - time()) / 86400);
                                        echo 'Expira en ' . $days_left . ' día(s)';
                                    }
                                    ?>
                                </span>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                            <span class="type-badge type-<?php echo $notification['type']; ?>">
                                <?php
                                $type_labels = [
                                    'info' => 'Información',
                                    'warning' => 'Advertencia',
                                    'error' => 'Error',
                                    'success' => 'Éxito'
                                ];
                                echo $type_labels[$notification['type']] ?? $notification['type'];
                                ?>
                            </span>
                            <span class="status-badge status-<?php echo $is_expired ? 'expired' : ($notification['is_active'] ? 'active' : 'inactive'); ?>">
                                <?php
                                if ($is_expired) {
                                    echo 'Expirada';
                                } else {
                                    echo $notification['is_active'] ? 'Activa' : 'Inactiva';
                                }
                                ?>
                            </span>
                        </div>
                    </div>

                    <div class="notification-message">
                        <?php echo nl2br(htmlspecialchars($notification['message'])); ?>
                    </div>

                    <div class="notification-actions">
                        <?php if (!$is_expired): ?>
                        <form method="POST" style="display: inline;">
                            <input type="hidden" name="action" value="toggle_status">
                            <input type="hidden" name="notification_id" value="<?php echo $notification['id']; ?>">
                            <button type="submit" class="btn <?php echo $notification['is_active'] ? 'btn-warning' : 'btn-success'; ?>">
                                <i class="fas fa-<?php echo $notification['is_active'] ? 'pause' : 'play'; ?>"></i>
                                <?php echo $notification['is_active'] ? 'Desactivar' : 'Activar'; ?>
                            </button>
                        </form>

                        <?php if ($notification['expires_at']): ?>
                        <button onclick="showExtendForm(<?php echo $notification['id']; ?>)" class="btn btn-primary">
                            <i class="fas fa-calendar-plus"></i>
                            Extender
                        </button>
                        <?php endif; ?>
                        <?php endif; ?>

                        <form method="POST" style="display: inline;">
                            <input type="hidden" name="action" value="delete_notification">
                            <input type="hidden" name="notification_id" value="<?php echo $notification['id']; ?>">
                            <button type="submit" class="btn btn-danger"
                                    onclick="return confirm('¿Estás seguro de que quieres eliminar esta notificación?')">
                                <i class="fas fa-trash"></i>
                                Eliminar
                            </button>
                        </form>
                    </div>
                </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </main>

    <!-- Modal para extender expiración -->
    <div id="extendModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); z-index: 1000;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: var(--secondary-color); padding: 2rem; border-radius: var(--border-radius); width: 90%; max-width: 400px;">
            <h3 style="margin-bottom: 1rem; color: var(--text-primary);">Extender Expiración</h3>
            <form method="POST">
                <input type="hidden" name="action" value="extend_expiry">
                <input type="hidden" name="notification_id" id="extendNotificationId">
                <div class="form-group" style="margin-bottom: 1.5rem;">
                    <label class="form-label">Días a extender</label>
                    <select name="extend_days" class="form-select" required>
                        <option value="7">7 días</option>
                        <option value="30" selected>30 días</option>
                        <option value="90">90 días</option>
                        <option value="365">1 año</option>
                    </select>
                </div>
                <div style="display: flex; gap: 1rem;">
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-calendar-plus"></i>
                        Extender
                    </button>
                    <button type="button" onclick="closeExtendModal()" class="btn btn-warning">
                        <i class="fas fa-times"></i>
                        Cancelar
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function toggleCreateForm() {
            const form = document.getElementById('createForm');
            form.classList.toggle('active');

            if (form.classList.contains('active')) {
                form.scrollIntoView({ behavior: 'smooth' });
                const firstInput = form.querySelector('input[type="text"]');
                if (firstInput) {
                    setTimeout(() => firstInput.focus(), 300);
                }
            }
        }

        function showExtendForm(notificationId) {
            document.getElementById('extendNotificationId').value = notificationId;
            document.getElementById('extendModal').style.display = 'block';
        }

        function closeExtendModal() {
            document.getElementById('extendModal').style.display = 'none';
        }

        // Cerrar modal al hacer clic fuera
        window.onclick = function(event) {
            const extendModal = document.getElementById('extendModal');
            if (event.target === extendModal) {
                closeExtendModal();
            }
        }

        // Auto-resize textarea
        const textarea = document.querySelector('.form-textarea');
        if (textarea) {
            textarea.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = this.scrollHeight + 'px';
            });
        }

        // Confirmación para acciones críticas
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', function(e) {
                const action = this.querySelector('input[name="action"]')?.value;
                if (action === 'delete_notification') {
                    if (!confirm('¿Estás seguro de que quieres eliminar esta notificación? Esta acción no se puede deshacer.')) {
                        e.preventDefault();
                    }
                }
            });
        });

        // Resaltar notificaciones que expiran pronto
        document.querySelectorAll('.notification-item').forEach(item => {
            const metaText = item.querySelector('.notification-meta').textContent;
            if (metaText.includes('Expira en')) {
                const match = metaText.match(/Expira en (\d+) día/);
                if (match) {
                    const daysLeft = parseInt(match[1]);
                    if (daysLeft <= 1) {
                        item.style.background = 'rgba(239, 68, 68, 0.05)';
                        item.style.borderLeftWidth = '6px';
                    } else if (daysLeft <= 3) {
                        item.style.background = 'rgba(245, 158, 11, 0.05)';
                        item.style.borderLeftWidth = '6px';
                    }
                }
            }
        });

        // Contador de caracteres para el textarea
        const messageTextarea = document.querySelector('textarea[name="message"]');
        if (messageTextarea) {
            const counter = document.createElement('div');
            counter.style.cssText = 'text-align: right; color: var(--text-secondary); font-size: 0.8rem; margin-top: 0.5rem;';
            messageTextarea.parentElement.appendChild(counter);

            function updateCounter() {
                const length = messageTextarea.value.length;
                counter.textContent = `${length} caracteres`;

                if (length < 10) {
                    counter.style.color = 'var(--error-color)';
                } else if (length > 500) {
                    counter.style.color = 'var(--warning-color)';
                } else {
                    counter.style.color = 'var(--success-color)';
                }
            }

            messageTextarea.addEventListener('input', updateCounter);
            updateCounter(); // Inicial
        }

        // Auto-refresh cada 60 segundos
        setInterval(function() {
            if (document.hidden) return;

            const activeElement = document.activeElement;
            if (activeElement.tagName !== 'INPUT' && activeElement.tagName !== 'TEXTAREA' && activeElement.tagName !== 'SELECT') {
                location.reload();
            }
        }, 60000);
    </script>
</body>
</html>
