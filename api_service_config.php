<?php
// API para gestionar configuraciones de servicios
session_start();
header('Content-Type: application/json');

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Error de conexión a la base de datos'
    ]);
    exit;
}

// Logging para debug
error_log("=== API service_config ===");
error_log("REQUEST_METHOD: " . $_SERVER['REQUEST_METHOD']);
error_log("CONTENT_TYPE: " . ($_SERVER['CONTENT_TYPE'] ?? 'No definido'));
error_log("GET params: " . print_r($_GET, true));
error_log("POST params: " . print_r($_POST, true));

// Obtener datos de entrada
$input_data = null;
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $raw_input = file_get_contents('php://input');
    error_log("Raw input: " . $raw_input);

    if (!empty($raw_input)) {
        $input_data = json_decode($raw_input, true);
        error_log("Decoded input: " . print_r($input_data, true));
    }
}

// Obtener acción
$action = $_GET['action'] ?? $_POST['action'] ?? ($input_data['action'] ?? null);

error_log("Action determined: " . ($action ?? 'NULL'));

if (!$action) {
    error_log("ERROR: No action specified");
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => 'Acción no especificada',
        'debug' => [
            'method' => $_SERVER['REQUEST_METHOD'],
            'get' => $_GET,
            'post' => $_POST,
            'input_data' => $input_data
        ]
    ]);
    exit;
}

switch ($action) {
    case 'get_all':
        getAllConfigs($pdo);
        break;
        
    case 'get_by_type':
        $service_type = $_GET['service_type'] ?? null;
        if (!$service_type) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'error' => 'Tipo de servicio no especificado'
            ]);
            exit;
        }
        getConfigByType($pdo, $service_type);
        break;
        
    case 'update':
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode([
                'success' => false,
                'error' => 'Método no permitido'
            ]);
            exit;
        }
        
        // Los datos ya están en $input_data desde arriba
        if (!$input_data) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'error' => 'Datos no válidos - no se recibió JSON'
            ]);
            exit;
        }

        updateConfig($pdo, $input_data);
        break;
        
    case 'get_public':
        getPublicConfigs($pdo);
        break;
        
    default:
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => 'Acción no válida'
        ]);
        break;
}

function getAllConfigs($pdo) {
    try {
        $stmt = $pdo->query("
            SELECT * FROM service_config 
            ORDER BY 
                CASE service_type 
                    WHEN 'trial' THEN 1 
                    WHEN 'purchase' THEN 2 
                    WHEN 'renewal' THEN 3 
                    ELSE 4 
                END
        ");
        $configs = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode([
            'success' => true,
            'configs' => $configs
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'Error obteniendo configuraciones: ' . $e->getMessage()
        ]);
    }
}

function getConfigByType($pdo, $service_type) {
    try {
        $stmt = $pdo->prepare("SELECT * FROM service_config WHERE service_type = ?");
        $stmt->execute([$service_type]);
        $config = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($config) {
            echo json_encode([
                'success' => true,
                'config' => $config
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'error' => 'Configuración no encontrada'
            ]);
        }
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'Error obteniendo configuración: ' . $e->getMessage()
        ]);
    }
}

function updateConfig($pdo, $input) {
    try {
        $service_type = $input['service_type'] ?? null;
        $config = $input['config'] ?? null;
        
        if (!$service_type || !$config) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'error' => 'Datos incompletos'
            ]);
            return;
        }
        
        // Validar que el service_type existe
        $stmt = $pdo->prepare("SELECT id FROM service_config WHERE service_type = ?");
        $stmt->execute([$service_type]);
        
        if (!$stmt->fetch()) {
            http_response_code(404);
            echo json_encode([
                'success' => false,
                'error' => 'Configuración no encontrada'
            ]);
            return;
        }
        
        // Actualizar configuración
        $stmt = $pdo->prepare("
            UPDATE service_config SET
                title = :title,
                subtitle = :subtitle,
                description = :description,
                price = :price,
                original_price = :original_price,
                discount_percentage = :discount_percentage,
                features = :features,
                button_text = :button_text,
                button_color = :button_color,
                is_active = :is_active,
                promotion_text = :promotion_text,
                promotion_badge = :promotion_badge,
                updated_at = CURRENT_TIMESTAMP
            WHERE service_type = :service_type
        ");
        
        $result = $stmt->execute([
            'service_type' => $service_type,
            'title' => $config['title'] ?? '',
            'subtitle' => $config['subtitle'] ?? '',
            'description' => $config['description'] ?? '',
            'price' => $config['price'] ?? '',
            'original_price' => $config['original_price'] ?? null,
            'discount_percentage' => (int)($config['discount_percentage'] ?? 0),
            'features' => $config['features'] ?? '[]',
            'button_text' => $config['button_text'] ?? '',
            'button_color' => $config['button_color'] ?? 'primary',
            'is_active' => $config['is_active'] ? 1 : 0,
            'promotion_text' => $config['promotion_text'] ?? '',
            'promotion_badge' => $config['promotion_badge'] ?? ''
        ]);
        
        if ($result) {
            echo json_encode([
                'success' => true,
                'message' => 'Configuración actualizada correctamente'
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'error' => 'Error actualizando configuración'
            ]);
        }
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'Error actualizando configuración: ' . $e->getMessage()
        ]);
    }
}

function getPublicConfigs($pdo) {
    try {
        $stmt = $pdo->query("
            SELECT * FROM service_config 
            WHERE is_active = 1
            ORDER BY 
                CASE service_type 
                    WHEN 'trial' THEN 1 
                    WHEN 'purchase' THEN 2 
                    WHEN 'renewal' THEN 3 
                    ELSE 4 
                END
        ");
        $configs = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Procesar features para cada configuración
        foreach ($configs as &$config) {
            $features = $config['features'] ?? '[]';
            // Si features es una cadena JSON válida, decodificarla
            if (is_string($features)) {
                $decoded = json_decode($features, true);
                $config['features'] = $decoded !== null ? $decoded : [];
            } else {
                $config['features'] = [];
            }
        }
        
        echo json_encode([
            'success' => true,
            'configs' => $configs
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'Error obteniendo configuraciones públicas: ' . $e->getMessage()
        ]);
    }
}
?>
