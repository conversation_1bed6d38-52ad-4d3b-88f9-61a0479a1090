<?php
// Buscador simple y robusto de contenido M3U
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();

if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Error de conexión: " . $e->getMessage());
}

$search_query = $_GET['q'] ?? '';
$results = [];
$total_content = 0;

// Obtener estadísticas generales
try {
    $stmt = $pdo->query("SELECT COUNT(*) FROM m3u_content c LEFT JOIN m3u_lists l ON c.list_id = l.id WHERE l.is_active = 1");
    $total_content = $stmt->fetchColumn();
} catch (Exception $e) {
    $total_content = 0;
}

// Realizar búsqueda si hay query
if ($search_query && strlen($search_query) >= 2) {
    try {
        $search_term = "%$search_query%";
        $stmt = $pdo->prepare("
            SELECT c.*, l.name as list_name, l.folder_name
            FROM m3u_content c 
            LEFT JOIN m3u_lists l ON c.list_id = l.id 
            WHERE l.is_active = 1 
            AND (c.title LIKE ? OR c.clean_title LIKE ?)
            ORDER BY 
                CASE 
                    WHEN c.title LIKE ? THEN 1
                    WHEN c.clean_title LIKE ? THEN 2
                    ELSE 3
                END,
                c.title
            LIMIT 100
        ");
        
        $exact_term = "%$search_query%";
        $stmt->execute([$search_term, $search_term, $exact_term, $exact_term]);
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
    } catch (Exception $e) {
        $error_message = "Error en la búsqueda: " . $e->getMessage();
    }
}

// Obtener pedidos recientes para sugerencias
$recent_orders = [];
try {
    $stmt = $pdo->query("
        SELECT DISTINCT title 
        FROM orders 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        AND title IS NOT NULL 
        AND title != ''
        ORDER BY created_at DESC 
        LIMIT 15
    ");
    $recent_orders = $stmt->fetchAll(PDO::FETCH_COLUMN);
} catch (Exception $e) {
    // Silenciar error de pedidos
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Buscar Contenido M3U - RGS TOOL</title>
    <style>
        :root {
            /* Paleta de colores negro y rojo */
            --primary-color: #dc2626;
            --primary-dark: #b91c1c;
            --secondary-color: #1f1f1f;
            --dark-bg: #000000;
            --darker-bg: #0a0a0a;
            --text-primary: #ffffff;
            --text-secondary: #d1d5db;
            --accent-color: #dc2626;
            --accent-dark: #b91c1c;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --success-color: #10b981;
            --info-color: #06b6d4;
            --border-color: #334155;
            --border-light: #475569;
            --gradient-primary: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            --gradient-secondary: linear-gradient(135deg, #10b981 0%, #059669 100%);
            --gradient-dark: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
            --gradient-bg: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            --shadow-light: 0 2px 8px rgba(0,0,0,0.3);
            --shadow-medium: 0 4px 16px rgba(0,0,0,0.4);
            --shadow-heavy: 0 8px 32px rgba(0,0,0,0.6);
            --border-radius: 12px;
            --border-radius-lg: 16px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: var(--gradient-bg);
            color: var(--text-primary);
            min-height: 100vh;
        }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { text-align: center; margin-bottom: 2rem; }
        .header h1 { color: var(--accent-color); margin-bottom: 0.5rem; }
        .search-box { background: var(--secondary-color); padding: 2rem; border-radius: var(--border-radius-lg); margin-bottom: 2rem; border: 1px solid var(--border-color); }
        .search-form { display: flex; gap: 1rem; align-items: center; flex-wrap: wrap; }
        .search-input { flex: 1; min-width: 300px; padding: 12px; border: 1px solid var(--border-color); border-radius: var(--border-radius); background: var(--dark-bg); color: var(--text-primary); font-size: 16px; }
        .search-input:focus { outline: none; border-color: var(--primary-color); box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.15); }
        .search-btn { padding: 12px 24px; background: var(--gradient-primary); color: white; border: none; border-radius: var(--border-radius); cursor: pointer; font-weight: bold; transition: var(--transition); }
        .search-btn:hover { background: var(--primary-dark); transform: translateY(-2px); }
        .stats { display: flex; gap: 2rem; justify-content: center; margin-bottom: 2rem; flex-wrap: wrap; }
        .stat { text-align: center; }
        .stat-number { font-size: 2rem; color: var(--accent-color); font-weight: bold; }
        .stat-label { color: var(--text-secondary); font-size: 0.9rem; }
        .suggestions { margin-top: 1rem; }
        .suggestions h4 { color: var(--text-secondary); margin-bottom: 0.5rem; }
        .suggestion-tags { display: flex; flex-wrap: wrap; gap: 0.5rem; }
        .suggestion-tag { background: var(--dark-bg); border: 1px solid var(--border-color); padding: 0.3rem 0.8rem; border-radius: 15px; font-size: 0.8rem; cursor: pointer; transition: var(--transition); }
        .suggestion-tag:hover { background: var(--accent-color); color: white; }
        .results { background: var(--secondary-color); padding: 2rem; border-radius: var(--border-radius-lg); border: 1px solid var(--border-color); }
        .result-item { background: var(--dark-bg); padding: 1.5rem; margin-bottom: 1rem; border-radius: var(--border-radius); border: 1px solid var(--border-color); }
        .result-title { color: var(--accent-color); font-size: 1.1rem; font-weight: bold; margin-bottom: 0.5rem; }
        .result-meta { display: flex; flex-wrap: wrap; gap: 1rem; margin-bottom: 1rem; color: var(--text-secondary); font-size: 0.9rem; }
        .result-actions { display: flex; gap: 0.5rem; flex-wrap: wrap; }
        .btn { padding: 0.5rem 1rem; border: none; border-radius: var(--border-radius); cursor: pointer; text-decoration: none; font-size: 0.8rem; display: inline-flex; align-items: center; gap: 0.3rem; transition: var(--transition); }
        .btn-copy { background: var(--success-color); color: white; }
        .btn-copy:hover { background: var(--accent-dark); }
        .btn-info { background: var(--info-color); color: white; }
        .btn-info:hover { background: #0891b2; }
        .no-results { text-align: center; padding: 3rem; color: var(--text-secondary); }
        .back-link { color: var(--accent-color); text-decoration: none; margin-bottom: 2rem; display: inline-block; }
        .back-link:hover { color: var(--accent-dark); }
        .error { background: rgba(239, 68, 68, 0.2); color: var(--error-color); padding: 1rem; border-radius: var(--border-radius); margin-bottom: 1rem; border: 1px solid rgba(239, 68, 68, 0.3); }
        .success { background: rgba(16, 185, 129, 0.2); color: var(--success-color); padding: 1rem; border-radius: var(--border-radius); margin-bottom: 1rem; border: 1px solid rgba(16, 185, 129, 0.3); }
    </style>
</head>
<body>
    <div class="container">
        <a href="admin.php" class="back-link">← Volver al Panel Admin</a>
        
        <div class="header">
            <h1>🔍 Buscar Contenido M3U</h1>
            <p>Encuentra series y películas en tus listas IPTV</p>
        </div>

        <!-- Estadísticas -->
        <div class="stats">
            <div class="stat">
                <div class="stat-number"><?php echo number_format($total_content); ?></div>
                <div class="stat-label">Contenido Total</div>
            </div>
            <div class="stat">
                <div class="stat-number"><?php echo count($results); ?></div>
                <div class="stat-label">Resultados</div>
            </div>
        </div>

        <!-- Formulario de búsqueda -->
        <div class="search-box">
            <form method="GET" class="search-form">
                <input type="text" 
                       name="q" 
                       class="search-input" 
                       value="<?php echo htmlspecialchars($search_query); ?>" 
                       placeholder="Buscar series, películas... (ej: Breaking Bad, Avengers)"
                       autofocus>
                <button type="submit" class="search-btn">🔍 Buscar</button>
            </form>

            <?php if (!empty($recent_orders)): ?>
            <div class="suggestions">
                <h4>Pedidos recientes (haz clic para buscar):</h4>
                <div class="suggestion-tags">
                    <?php foreach ($recent_orders as $order_title): ?>
                    <span class="suggestion-tag" onclick="searchFor('<?php echo addslashes($order_title); ?>')">
                        <?php echo htmlspecialchars($order_title); ?>
                    </span>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php endif; ?>
        </div>

        <!-- Resultados -->
        <?php if (isset($error_message)): ?>
        <div class="error"><?php echo htmlspecialchars($error_message); ?></div>
        <?php endif; ?>

        <?php if ($search_query): ?>
        <div class="results">
            <h2>Resultados para: "<?php echo htmlspecialchars($search_query); ?>"</h2>
            
            <?php if (empty($results)): ?>
            <div class="no-results">
                <h3>😔 No se encontraron resultados</h3>
                <p>Intenta con:</p>
                <ul style="text-align: left; display: inline-block;">
                    <li>Términos más generales (ej: "Breaking" en lugar de "Breaking Bad")</li>
                    <li>Solo el nombre principal (ej: "Avengers" en lugar de "Avengers Endgame")</li>
                    <li>Verificar que tengas listas M3U analizadas</li>
                </ul>
                <p style="margin-top: 1rem;">
                    <a href="m3u_content_viewer.php" class="btn btn-info">📺 Ver Todo el Contenido</a>
                </p>
            </div>
            <?php else: ?>
            <?php foreach ($results as $item): ?>
            <div class="result-item">
                <div class="result-title"><?php echo htmlspecialchars($item['title']); ?></div>
                
                <div class="result-meta">
                    <span>📁 Lista: <?php echo htmlspecialchars($item['list_name'] ?? 'N/A'); ?></span>
                    <span>🎭 Tipo: <?php 
                        echo $item['media_type'] === 'movie' ? '🎬 Película' : 
                             ($item['media_type'] === 'tv' ? '📺 Serie' : '❓ Desconocido'); 
                    ?></span>
                    <?php if ($item['year']): ?>
                    <span>📅 Año: <?php echo $item['year']; ?></span>
                    <?php endif; ?>
                    <?php if ($item['season']): ?>
                    <span>📺 T<?php echo $item['season']; ?></span>
                    <?php endif; ?>
                    <?php if ($item['episode']): ?>
                    <span>📺 E<?php echo $item['episode']; ?></span>
                    <?php endif; ?>
                </div>
                
                <div class="result-actions">
                    <button class="btn btn-copy" onclick="copyToClipboard('<?php echo addslashes($item['url']); ?>')">
                        📋 Copiar URL
                    </button>
                    <a href="m3u_content_viewer.php?search=<?php echo urlencode($item['title']); ?>" class="btn btn-info">
                        👁️ Ver Detalles
                    </a>
                </div>
            </div>
            <?php endforeach; ?>
            
            <?php if (count($results) >= 100): ?>
            <div class="success">
                ℹ️ Se muestran los primeros 100 resultados. Usa términos más específicos para refinar la búsqueda.
            </div>
            <?php endif; ?>
            <?php endif; ?>
        </div>
        <?php endif; ?>

        <div style="margin-top: 2rem; text-align: center; color: #666;">
            <p>
                <a href="m3u_manager.php" style="color: #46d347;">⚙️ Gestionar Listas</a> | 
                <a href="m3u_content_viewer.php" style="color: #46d347;">📺 Ver Todo</a> | 
                <a href="admin.php" style="color: #46d347;">🏠 Admin</a>
            </p>
        </div>
    </div>

    <script>
        function searchFor(title) {
            document.querySelector('input[name="q"]').value = title;
            document.querySelector('form').submit();
        }

        function copyToClipboard(text) {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(function() {
                    showMessage('✅ URL copiada al portapapeles');
                }).catch(function(err) {
                    fallbackCopy(text);
                });
            } else {
                fallbackCopy(text);
            }
        }

        function fallbackCopy(text) {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            
            try {
                document.execCommand('copy');
                showMessage('✅ URL copiada al portapapeles');
            } catch (err) {
                showMessage('❌ Error al copiar. Copia manualmente la URL.');
            }
            
            document.body.removeChild(textArea);
        }

        function showMessage(message) {
            const div = document.createElement('div');
            div.style.cssText = 'position:fixed;top:20px;right:20px;background:#28a745;color:white;padding:1rem;border-radius:8px;z-index:1000;';
            div.textContent = message;
            document.body.appendChild(div);
            
            setTimeout(() => {
                document.body.removeChild(div);
            }, 3000);
        }

        // Auto-focus en el campo de búsqueda
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.querySelector('input[name="q"]');
            if (searchInput && !searchInput.value) {
                searchInput.focus();
            }
        });
    </script>
</body>
</html>
