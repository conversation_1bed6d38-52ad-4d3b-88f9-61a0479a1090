<?php
session_start();
require_once 'config.php';

header('Content-Type: application/json');

$action = $_GET['action'] ?? '';

try {
    switch ($action) {
        case 'start_chat':
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('Método no permitido');
            }
            
            $user_id = $_SESSION['user_id'] ?? 1; // Usuario demo
            $initial_message = clean_input($_POST['initial_message'] ?? '');
            
            if (empty($initial_message)) {
                throw new Exception('Mensaje inicial requerido');
            }
            
            // Crear nueva sesión de chat
            $stmt = $pdo->prepare("INSERT INTO chat_sessions (user_id, status) VALUES (?, 'waiting')");
            $stmt->execute([$user_id]);
            $session_id = $pdo->lastInsertId();
            
            // Insertar mensaje inicial
            $stmt = $pdo->prepare("INSERT INTO chat_messages (session_id, sender_id, message, is_admin) VALUES (?, ?, ?, 0)");
            $stmt->execute([$session_id, $user_id, $initial_message]);
            
            // Crear estado de chat
            $stmt = $pdo->prepare("INSERT INTO chat_status (session_id, user_online, last_user_activity) VALUES (?, 1, NOW())");
            $stmt->execute([$session_id]);
            
            // Crear notificación para admin
            $stmt = $pdo->prepare("INSERT INTO admin_notifications (type, title, message, reference_id, reference_type) VALUES (?, ?, ?, ?, ?)");
            $stmt->execute(['chat', 'Nueva sesión de chat', 'Usuario solicita ayuda en chat en vivo', $session_id, 'chat_sessions']);
            
            echo json_encode(['success' => true, 'session_id' => $session_id]);
            break;
            
        case 'send_message':
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('Método no permitido');
            }
            
            $session_id = (int)($_POST['session_id'] ?? 0);
            $message = clean_input($_POST['message'] ?? '');
            $is_admin = isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;
            
            if (!$session_id || empty($message)) {
                throw new Exception('Datos incompletos');
            }
            
            $sender_id = $is_admin ? ($_SESSION['admin_id'] ?? 2) : ($_SESSION['user_id'] ?? 1);
            
            // Insertar mensaje
            $stmt = $pdo->prepare("INSERT INTO chat_messages (session_id, sender_id, message, is_admin) VALUES (?, ?, ?, ?)");
            $stmt->execute([$session_id, $sender_id, $message, $is_admin ? 1 : 0]);
            
            // Actualizar estado de la sesión
            if ($is_admin) {
                $stmt = $pdo->prepare("UPDATE chat_sessions SET status = 'active' WHERE id = ? AND status = 'waiting'");
                $stmt->execute([$session_id]);
                
                $stmt = $pdo->prepare("UPDATE chat_status SET admin_online = 1, last_admin_activity = NOW() WHERE session_id = ?");
                $stmt->execute([$session_id]);
            } else {
                $stmt = $pdo->prepare("UPDATE chat_status SET user_online = 1, last_user_activity = NOW() WHERE session_id = ?");
                $stmt->execute([$session_id]);
            }
            
            echo json_encode(['success' => true]);
            break;
            
        case 'get_messages':
            $session_id = (int)($_GET['session_id'] ?? 0);
            $last_message_id = (int)($_GET['last_id'] ?? 0);
            
            if (!$session_id) {
                throw new Exception('ID de sesión requerido');
            }
            
            $stmt = $pdo->prepare("
                SELECT cm.*, u.username 
                FROM chat_messages cm
                LEFT JOIN users u ON cm.sender_id = u.id
                WHERE cm.session_id = ? AND cm.id > ?
                ORDER BY cm.sent_at ASC
            ");
            $stmt->execute([$session_id, $last_message_id]);
            $messages = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Obtener estado del chat
            $stmt = $pdo->prepare("SELECT * FROM chat_status WHERE session_id = ?");
            $stmt->execute([$session_id]);
            $status = $stmt->fetch(PDO::FETCH_ASSOC);
            
            echo json_encode([
                'success' => true, 
                'messages' => $messages,
                'status' => $status
            ]);
            break;
            
        case 'get_sessions':
            if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
                throw new Exception('Acceso denegado');
            }
            
            $stmt = $pdo->prepare("
                SELECT cs.*, u.username, cs_status.admin_online, cs_status.user_online,
                       (SELECT COUNT(*) FROM chat_messages WHERE session_id = cs.id) as message_count,
                       (SELECT message FROM chat_messages WHERE session_id = cs.id ORDER BY sent_at DESC LIMIT 1) as last_message
                FROM chat_sessions cs
                LEFT JOIN users u ON cs.user_id = u.id
                LEFT JOIN chat_status cs_status ON cs.id = cs_status.session_id
                WHERE cs.status IN ('waiting', 'active')
                ORDER BY cs.started_at DESC
            ");
            $stmt->execute();
            $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo json_encode(['success' => true, 'sessions' => $sessions]);
            break;
            
        case 'update_admin_status':
            if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
                throw new Exception('Acceso denegado');
            }
            
            $session_id = (int)($_POST['session_id'] ?? 0);
            $online = (bool)($_POST['online'] ?? false);
            
            if ($session_id) {
                $stmt = $pdo->prepare("UPDATE chat_status SET admin_online = ?, last_admin_activity = NOW() WHERE session_id = ?");
                $stmt->execute([$online ? 1 : 0, $session_id]);
            }
            
            echo json_encode(['success' => true]);
            break;
            
        case 'update_user_status':
            $session_id = (int)($_POST['session_id'] ?? 0);
            $online = (bool)($_POST['online'] ?? false);
            
            if ($session_id) {
                $stmt = $pdo->prepare("UPDATE chat_status SET user_online = ?, last_user_activity = NOW() WHERE session_id = ?");
                $stmt->execute([$online ? 1 : 0, $session_id]);
            }
            
            echo json_encode(['success' => true]);
            break;
            
        case 'end_chat':
            $session_id = (int)($_POST['session_id'] ?? 0);
            
            if (!$session_id) {
                throw new Exception('ID de sesión requerido');
            }
            
            // Finalizar sesión
            $stmt = $pdo->prepare("UPDATE chat_sessions SET status = 'ended', ended_at = NOW() WHERE id = ?");
            $stmt->execute([$session_id]);
            
            // Actualizar estado
            $stmt = $pdo->prepare("UPDATE chat_status SET admin_online = 0, user_online = 0 WHERE session_id = ?");
            $stmt->execute([$session_id]);
            
            echo json_encode(['success' => true]);
            break;
            
        default:
            throw new Exception('Acción no válida');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => $e->getMessage()]);
}
?>
