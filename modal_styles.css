/* 🎨 Estilos Modernos para Modales del Sistema de Solicitudes */

/* Variables CSS para consistencia */
:root {
    --modal-backdrop: rgba(3, 7, 18, 0.85);
    --modal-border: rgba(255, 255, 255, 0.15);
    --modal-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.4);
    --modal-glow: 0 0 0 1px rgba(255, 255, 255, 0.05);
    --modal-inner-glow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
    --input-bg: rgba(255, 255, 255, 0.05);
    --input-border: rgba(255, 255, 255, 0.15);
    --input-focus-bg: rgba(255, 255, 255, 0.08);
    --input-focus-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

/* Modal base */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--modal-backdrop);
    backdrop-filter: blur(12px) saturate(150%);
    z-index: 10000;
    align-items: center;
    justify-content: center;
    padding: 1rem;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.modal.show {
    display: flex;
    opacity: 1;
}

/* Contenido del modal */
.modal-content {
    background: var(--gradient-card);
    border: 1px solid var(--modal-border);
    border-radius: var(--radius-xl);
    max-width: min(95vw, 900px);
    max-height: 95vh;
    overflow: hidden;
    box-shadow: var(--modal-shadow), var(--modal-glow), var(--modal-inner-glow);
    animation: modalSlideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
    position: relative;
}

.modal-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--gradient-primary);
    opacity: 0.8;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Header del modal */
.modal-header {
    padding: 1.5rem 2rem;
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.05) 100%);
    border-bottom: 1px solid rgba(16, 185, 129, 0.2);
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    backdrop-filter: blur(10px);
}

.modal-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.modal-title i {
    color: var(--primary-color);
    font-size: 1.3rem;
}

.modal-close {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: var(--text-secondary);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.75rem;
    border-radius: 50%;
    transition: var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
}

.modal-close:hover {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
    transform: scale(1.1);
}

/* Cuerpo del modal */
.modal-body {
    padding: 0;
    max-height: calc(95vh - 120px);
    overflow-y: auto;
}

/* Scrollbar personalizado */
.modal-body::-webkit-scrollbar {
    width: 8px;
}

.modal-body::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
}

.modal-body::-webkit-scrollbar-thumb {
    background: var(--gradient-primary);
    border-radius: 4px;
}

.modal-body::-webkit-scrollbar-thumb:hover {
    background: var(--primary-dark);
}

/* Pasos del formulario */
.form-step {
    padding: 2rem;
    display: none;
    animation: fadeInUp 0.3s ease-out;
}

.form-step.active {
    display: block;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.step-header {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.step-header h4 {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.step-header p {
    color: var(--text-secondary);
    font-size: 0.95rem;
}

/* Grid de formulario */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group label {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 0.875rem 1rem;
    background: var(--input-bg);
    border: 1px solid var(--input-border);
    border-radius: var(--radius-lg);
    color: var(--text-primary);
    font-size: 0.95rem;
    transition: var(--transition-normal);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    background: var(--input-focus-bg);
    box-shadow: var(--input-focus-shadow);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: var(--text-muted);
}

.form-group small {
    color: var(--text-muted);
    font-size: 0.8rem;
    margin-top: 0.25rem;
}

/* Checkboxes y radios personalizados */
.checkbox-label,
.radio-label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--radius-md);
    transition: var(--transition-normal);
}

.checkbox-label:hover,
.radio-label:hover {
    background: rgba(255, 255, 255, 0.05);
}

.checkbox-label input[type="checkbox"],
.radio-label input[type="radio"] {
    width: 18px;
    height: 18px;
    accent-color: var(--primary-color);
}

/* Botones del formulario */
.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: space-between;
    padding: 1.5rem 2rem;
    background: rgba(255, 255, 255, 0.02);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin: 0 -2rem -2rem -2rem;
    border-radius: 0 0 var(--radius-xl) var(--radius-xl);
}

.btn {
    padding: 0.875rem 1.5rem;
    border: none;
    border-radius: var(--radius-lg);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    text-decoration: none;
    font-size: 0.95rem;
    min-width: 120px;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover:not(:disabled) {
    background: var(--gradient-primary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md), var(--shadow-glow);
}

.btn-secondary {
    background: var(--gradient-surface);
    color: var(--text-primary);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-secondary:hover:not(:disabled) {
    background: var(--gradient-elevated);
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.btn-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    box-shadow: var(--shadow-sm);
}

.btn-success:hover:not(:disabled) {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md), var(--shadow-glow);
}

/* Indicador de progreso */
.progress-indicator {
    display: flex;
    justify-content: center;
    gap: 1rem;
    padding: 1.5rem 2rem;
    background: rgba(255, 255, 255, 0.02);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.progress-step {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-muted);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.9rem;
    transition: var(--transition-normal);
    border: 2px solid transparent;
}

.progress-step.active {
    background: var(--gradient-primary);
    color: white;
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-glow);
    transform: scale(1.1);
}

/* Efectos adicionales */
.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    transform: translateY(-1px);
}

.btn:active {
    transform: translateY(0) !important;
}

/* Animaciones para elementos del formulario */
.form-group {
    animation: slideInUp 0.3s ease-out;
}

.form-group:nth-child(1) { animation-delay: 0.1s; }
.form-group:nth-child(2) { animation-delay: 0.2s; }
.form-group:nth-child(3) { animation-delay: 0.3s; }
.form-group:nth-child(4) { animation-delay: 0.4s; }

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Mejoras visuales para checkboxes y radios */
.form-group input[type="checkbox"],
.form-group input[type="radio"] {
    transform: scale(1.2);
    margin-right: 0.5rem;
}

/* Estilos para campos requeridos */
.form-group label.required::after {
    content: ' *';
    color: #ef4444;
    font-weight: bold;
}

/* Indicadores de validación */
.form-group.valid input,
.form-group.valid select,
.form-group.valid textarea {
    border-color: #10b981;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.form-group.invalid input,
.form-group.invalid select,
.form-group.invalid textarea {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* Responsive */
@media (max-width: 768px) {
    .modal-content {
        max-width: 95vw;
        max-height: 95vh;
        margin: 0.5rem;
    }

    .modal-header {
        padding: 1.5rem 1.5rem 1rem 1.5rem;
    }

    .modal-title {
        font-size: 1.3rem;
    }

    .form-step {
        padding: 1.5rem;
    }

    .form-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .form-actions {
        flex-direction: column;
        gap: 0.75rem;
        padding: 1rem;
        margin: 0 -1.5rem -1.5rem -1.5rem;
    }

    .btn {
        width: 100%;
    }

    .progress-indicator {
        padding: 1rem;
        gap: 0.75rem;
    }

    .progress-step {
        width: 35px;
        height: 35px;
        font-size: 0.8rem;
    }
}
