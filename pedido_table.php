<?php
session_start();

// Verificar si es admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die('Error de conexión: ' . $e->getMessage());
}

// Archivar pedidos antiguos automáticamente
$archive_days = 7; // Días después de "Listo" para archivar
$pdo->exec("
    CREATE TABLE IF NOT EXISTS orders_history (
        id INT AUTO_INCREMENT PRIMARY KEY,
        original_order_id INT,
        user_id INT,
        tmdb_id INT,
        title VARCHAR(255),
        media_type VARCHAR(32),
        year VARCHAR(8),
        country VARCHAR(64),
        city VARCHAR(64),
        ip_address VARCHAR(45),
        status VARCHAR(32),
        created_at DATETIME,
        updated_at DATETIME,
        archived_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        notif_seen TINYINT(1) DEFAULT 1
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
");

// Mover pedidos antiguos al historial
$stmt = $pdo->prepare("
    INSERT INTO orders_history 
    (original_order_id, user_id, tmdb_id, title, media_type, year, country, city, ip_address, status, created_at, updated_at)
    SELECT id, user_id, tmdb_id, title, media_type, year, country, city, ip_address, 'Disponible', created_at, updated_at
    FROM orders 
    WHERE status = 'Listo' AND updated_at < DATE_SUB(NOW(), INTERVAL ? DAY)
");
$stmt->execute([$archive_days]);
$archived_count = $stmt->rowCount();

// Eliminar de la tabla principal
$pdo->prepare("DELETE FROM orders WHERE status = 'Listo' AND updated_at < DATE_SUB(NOW(), INTERVAL ? DAY)")
    ->execute([$archive_days]);

// Obtener pedidos recientes (últimos 30 días)
$stmt = $pdo->query("
    SELECT o.*, u.username, u.is_cliente_actual, u.is_mavistv, u.is_tvdigital, u.is_limites507
    FROM orders o
    LEFT JOIN users u ON o.user_id = u.id
    WHERE o.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
    ORDER BY o.created_at DESC
");
$recent_orders = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Obtener historial de pedidos (disponibles)
$stmt = $pdo->query("
    SELECT h.*, u.username, u.is_cliente_actual, u.is_mavistv, u.is_tvdigital, u.is_limites507
    FROM orders_history h
    LEFT JOIN users u ON h.user_id = u.id
    ORDER BY h.archived_at DESC
    LIMIT 100
");
$archived_orders = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Estadísticas
$stats = [];
$stats['recent_total'] = count($recent_orders);
$stats['archived_total'] = count($archived_orders);
$stats['pending'] = count(array_filter($recent_orders, fn($o) => in_array($o['status'], ['Recibido', 'En Cola'])));
$stats['processing'] = count(array_filter($recent_orders, fn($o) => $o['status'] === 'Procesando'));
$stats['completed'] = count(array_filter($recent_orders, fn($o) => $o['status'] === 'Listo'));
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 Gestión de Pedidos - RogsMediaTV</title>
    
    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📊</text></svg>">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        :root {
            /* Paleta de colores profesional para soporte técnico */
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #1e293b;
            --dark-bg: #0f172a;
            --darker-bg: #020617;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --accent-color: #10b981;
            --accent-dark: #059669;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --success-color: #10b981;
            --info-color: #06b6d4;
            --border-color: #334155;
            --border-light: #475569;
            --gradient-primary: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            --gradient-secondary: linear-gradient(135deg, #10b981 0%, #059669 100%);
            --gradient-dark: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
            --gradient-bg: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            --shadow-light: 0 2px 8px rgba(0,0,0,0.3);
            --shadow-medium: 0 4px 16px rgba(0,0,0,0.4);
            --shadow-heavy: 0 8px 32px rgba(0,0,0,0.6);
            --border-radius: 12px;
            --border-radius-lg: 16px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gradient-bg);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
        }

        /* Header */
        .header {
            background: var(--gradient-dark);
            padding: 1.5rem 0;
            border-bottom: 1px solid var(--border-color);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .header-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .page-title {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .page-title h1 {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .nav-links {
            display: flex;
            gap: 1rem;
        }

        .nav-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            background: rgba(255,255,255,0.1);
            color: var(--text-primary);
            text-decoration: none;
            border-radius: var(--border-radius);
            transition: var(--transition);
            font-weight: 500;
        }

        .nav-btn:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }

        /* Stats Section */
        .stats-section {
            padding: 2rem 0;
            background: var(--dark-bg);
        }

        .stats-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 1.5rem;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
        }

        .stat-card {
            background: var(--secondary-color);
            padding: 1.5rem;
            border-radius: 12px;
            border: 1px solid var(--border-color);
            text-align: center;
            transition: var(--transition);
        }

        .stat-card:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-medium);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Tabs */
        .tabs-section {
            background: var(--dark-bg);
            padding: 1rem 0;
            border-bottom: 1px solid var(--border-color);
        }

        .tabs-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 1.5rem;
        }

        .tabs {
            display: flex;
            gap: 0.5rem;
            overflow-x: auto;
        }

        .tab-btn {
            padding: 0.75rem 1.5rem;
            background: transparent;
            border: 2px solid var(--border-color);
            color: var(--text-secondary);
            border-radius: 50px;
            cursor: pointer;
            transition: var(--transition);
            white-space: nowrap;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .tab-btn:hover {
            border-color: var(--primary-color);
            color: var(--text-primary);
        }

        .tab-btn.active {
            background: var(--gradient-primary);
            border-color: var(--primary-color);
            color: white;
        }

        /* Content */
        .content-section {
            padding: 2rem 0;
        }

        .content-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 1.5rem;
        }

        .section-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 2rem;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .archive-info {
            background: rgba(70, 211, 105, 0.1);
            color: var(--accent-color);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.85rem;
            border: 1px solid rgba(70, 211, 105, 0.3);
        }

        /* Table */
        .table-container {
            background: var(--secondary-color);
            border-radius: 12px;
            border: 1px solid var(--border-color);
            overflow: hidden;
            margin-bottom: 2rem;
        }

        .orders-table {
            width: 100%;
            border-collapse: collapse;
        }

        .orders-table th,
        .orders-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .orders-table th {
            background: var(--dark-bg);
            color: var(--text-primary);
            font-weight: 600;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .orders-table td {
            color: var(--text-secondary);
        }

        .orders-table tr:hover {
            background: rgba(255,255,255,0.05);
        }

        .status-badge {
            padding: 0.3rem 0.6rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
            text-align: center;
        }

        .status-recibido { background: rgba(74, 144, 226, 0.2); color: #4a90e2; }
        .status-en-cola { background: rgba(255, 193, 7, 0.2); color: #ffc107; }
        .status-procesando { background: rgba(255, 152, 0, 0.2); color: #ff9800; }
        .status-listo { background: rgba(76, 175, 80, 0.2); color: #4caf50; }
        .status-disponible { background: rgba(156, 39, 176, 0.2); color: #9c27b0; }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .header-container {
                flex-direction: column;
                text-align: center;
            }

            .stats-container {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }

            .table-container {
                overflow-x: auto;
            }

            .orders-table {
                min-width: 800px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-container">
            <div class="page-title">
                <h1>
                    <i class="fas fa-table"></i>
                    Gestión de Pedidos
                </h1>
            </div>

            <nav class="nav-links">
                <a href="admin.php" class="nav-btn">
                    <i class="fas fa-arrow-left"></i>
                    Volver al Admin
                </a>
                <a href="admin_logout.php" class="nav-btn">
                    <i class="fas fa-sign-out-alt"></i>
                    Salir
                </a>
            </nav>
        </div>
    </header>

    <!-- Stats Section -->
    <section class="stats-section">
        <div class="stats-container">
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['recent_total']; ?></div>
                <div class="stat-label">Pedidos Recientes</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['pending']; ?></div>
                <div class="stat-label">Pendientes</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['processing']; ?></div>
                <div class="stat-label">Procesando</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['completed']; ?></div>
                <div class="stat-label">Completados</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['archived_total']; ?></div>
                <div class="stat-label">Disponibles</div>
            </div>
        </div>
    </section>

    <!-- Tabs -->
    <section class="tabs-section">
        <div class="tabs-container">
            <div class="tabs">
                <button class="tab-btn active" onclick="switchTab('recent')">
                    <i class="fas fa-clock"></i>
                    Pedidos Recientes (30 días)
                </button>
                <button class="tab-btn" onclick="switchTab('archived')">
                    <i class="fas fa-archive"></i>
                    Historial Disponible
                </button>
            </div>
        </div>
    </section>

    <!-- Content -->
    <section class="content-section">
        <div class="content-container">
            <!-- Recent Orders -->
            <div id="recent" class="tab-content active">
                <div class="section-header">
                    <h2 class="section-title">
                        <i class="fas fa-list-ul"></i>
                        Pedidos Recientes
                    </h2>
                    <?php if ($archived_count > 0): ?>
                    <div class="archive-info">
                        <i class="fas fa-info-circle"></i>
                        <?php echo $archived_count; ?> pedidos archivados automáticamente
                    </div>
                    <?php endif; ?>
                </div>

                <div class="table-container">
                    <table class="orders-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Usuario</th>
                                <th>Título</th>
                                <th>Tipo</th>
                                <th>Estado</th>
                                <th>Ubicación</th>
                                <th>Fecha</th>
                                <th>TMDB</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recent_orders as $order): ?>
                            <tr>
                                <td><strong>#<?php echo $order['id']; ?></strong></td>
                                <td>
                                    <div>
                                        <strong><?php echo htmlspecialchars($order['username'] ?? 'Usuario #' . $order['user_id']); ?></strong>
                                        <div style="font-size: 0.8rem; color: var(--text-secondary);">
                                            <?php
                                            $badges = [];
                                            if ($order['is_cliente_actual']) $badges[] = 'Cliente';
                                            if ($order['is_mavistv']) $badges[] = 'MavisTV';
                                            if ($order['is_tvdigital']) $badges[] = 'TVDigital';
                                            if ($order['is_limites507']) $badges[] = 'Limites507';
                                            echo implode(' • ', $badges);
                                            ?>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <strong><?php echo htmlspecialchars($order['title']); ?></strong>
                                        <div style="font-size: 0.8rem; color: var(--text-secondary);">
                                            <?php echo $order['year'] ? '(' . $order['year'] . ')' : ''; ?>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <i class="fas fa-<?php echo $order['media_type'] === 'movie' ? 'film' : 'tv'; ?>"></i>
                                    <?php echo $order['media_type'] === 'movie' ? 'Película' : 'Serie'; ?>
                                </td>
                                <td>
                                    <span class="status-badge status-<?php echo strtolower(str_replace(' ', '-', $order['status'])); ?>">
                                        <?php echo $order['status']; ?>
                                    </span>
                                </td>
                                <td>
                                    <div style="font-size: 0.8rem;">
                                        <div><?php echo htmlspecialchars($order['city'] ?? 'N/A'); ?></div>
                                        <div style="color: var(--text-secondary);"><?php echo htmlspecialchars($order['country'] ?? 'N/A'); ?></div>
                                    </div>
                                </td>
                                <td>
                                    <div style="font-size: 0.8rem;">
                                        <div><?php echo date('d/m/Y', strtotime($order['created_at'])); ?></div>
                                        <div style="color: var(--text-secondary);"><?php echo date('H:i', strtotime($order['created_at'])); ?></div>
                                    </div>
                                </td>
                                <td>
                                    <a href="https://www.themoviedb.org/<?php echo $order['media_type']; ?>/<?php echo $order['tmdb_id']; ?>?language=es-MX"
                                       target="_blank" style="color: #01b4e4; text-decoration: none;">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Archived Orders -->
            <div id="archived" class="tab-content">
                <div class="section-header">
                    <h2 class="section-title">
                        <i class="fas fa-archive"></i>
                        Historial - Contenido Disponible
                    </h2>
                    <div class="archive-info">
                        <i class="fas fa-info-circle"></i>
                        Pedidos completados hace más de <?php echo $archive_days; ?> días
                    </div>
                </div>

                <div class="table-container">
                    <table class="orders-table">
                        <thead>
                            <tr>
                                <th>ID Original</th>
                                <th>Usuario</th>
                                <th>Título</th>
                                <th>Tipo</th>
                                <th>Estado</th>
                                <th>Ubicación</th>
                                <th>Archivado</th>
                                <th>TMDB</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($archived_orders as $order): ?>
                            <tr>
                                <td><strong>#<?php echo $order['original_order_id']; ?></strong></td>
                                <td>
                                    <div>
                                        <strong><?php echo htmlspecialchars($order['username'] ?? 'Usuario #' . $order['user_id']); ?></strong>
                                        <div style="font-size: 0.8rem; color: var(--text-secondary);">
                                            <?php
                                            $badges = [];
                                            if ($order['is_cliente_actual']) $badges[] = 'Cliente';
                                            if ($order['is_mavistv']) $badges[] = 'MavisTV';
                                            if ($order['is_tvdigital']) $badges[] = 'TVDigital';
                                            if ($order['is_limites507']) $badges[] = 'Limites507';
                                            echo implode(' • ', $badges);
                                            ?>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <strong><?php echo htmlspecialchars($order['title']); ?></strong>
                                        <div style="font-size: 0.8rem; color: var(--text-secondary);">
                                            <?php echo $order['year'] ? '(' . $order['year'] . ')' : ''; ?>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <i class="fas fa-<?php echo $order['media_type'] === 'movie' ? 'film' : 'tv'; ?>"></i>
                                    <?php echo $order['media_type'] === 'movie' ? 'Película' : 'Serie'; ?>
                                </td>
                                <td>
                                    <span class="status-badge status-disponible">
                                        Disponible
                                    </span>
                                </td>
                                <td>
                                    <div style="font-size: 0.8rem;">
                                        <div><?php echo htmlspecialchars($order['city'] ?? 'N/A'); ?></div>
                                        <div style="color: var(--text-secondary);"><?php echo htmlspecialchars($order['country'] ?? 'N/A'); ?></div>
                                    </div>
                                </td>
                                <td>
                                    <div style="font-size: 0.8rem;">
                                        <div><?php echo date('d/m/Y', strtotime($order['archived_at'])); ?></div>
                                        <div style="color: var(--text-secondary);"><?php echo date('H:i', strtotime($order['archived_at'])); ?></div>
                                    </div>
                                </td>
                                <td>
                                    <a href="https://www.themoviedb.org/<?php echo $order['media_type']; ?>/<?php echo $order['tmdb_id']; ?>?language=es-MX"
                                       target="_blank" style="color: #01b4e4; text-decoration: none;">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </section>

    <script>
        function switchTab(tabName) {
            // Ocultar todos los contenidos
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });

            // Desactivar todos los botones
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // Mostrar el contenido seleccionado
            document.getElementById(tabName).classList.add('active');

            // Activar el botón correspondiente
            event.target.classList.add('active');
        }
    </script>
</body>
</html>
