<!-- Incluir estilos modernos de modales -->
<link rel="stylesheet" href="modal_styles.css">

<!-- Modal de Renovación de Servicio -->
<div id="renewalModal" class="modal" style="display: none;">
    <div class="modal-content renewal-modal">
        <div class="modal-body">
        <div class="modal-header">
            <h3 class="modal-title">
                <i class="fas fa-sync-alt"></i>
                Renovar Servicio IPTV
            </h3>
            <button class="modal-close" onclick="closeRenewalModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <form id="renewalForm" class="renewal-form">
            <!-- Paso 1: Información de Cuenta -->
            <div class="form-step active" id="renewalStep1">
                <div class="step-header">
                    <h4><i class="fas fa-user-circle"></i> Información de tu Cuenta</h4>
                    <p>Ingresa los datos de tu servicio actual</p>
                </div>
                
                <div class="form-grid">
                    <div class="form-group">
                        <label for="renewalFirstName">Nombre *</label>
                        <input type="text" id="renewalFirstName" name="firstName" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="renewalLastName">Apellido *</label>
                        <input type="text" id="renewalLastName" name="lastName" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="renewalEmail">Correo Electrónico *</label>
                        <input type="email" id="renewalEmail" name="email" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="renewalPhone">Teléfono *</label>
                        <input type="tel" id="renewalPhone" name="phone" required placeholder="+57 ************">
                    </div>
                    
                    <div class="form-group full-width">
                        <label for="platformId">ID de la Plataforma *</label>
                        <input type="text" id="platformId" name="platformId" required placeholder="Ej: user123, cliente456, etc.">
                        <small>Este es tu usuario o ID actual en nuestro sistema</small>
                    </div>
                </div>
                
                <div class="form-actions">
                    <button type="button" class="btn btn-primary" onclick="nextRenewalStep(2)">
                        Siguiente <i class="fas fa-arrow-right"></i>
                    </button>
                </div>
            </div>
            
            <!-- Paso 2: Plan de Renovación -->
            <div class="form-step" id="renewalStep2">
                <div class="step-header">
                    <h4><i class="fas fa-calendar-alt"></i> Selecciona tu Plan</h4>
                    <p>Elige el período de renovación que prefieras</p>
                </div>
                
                <div class="renewal-plans">
                    <div class="plan-card">
                        <input type="radio" id="plan1Month" name="renewalPeriod" value="1_month">
                        <label for="plan1Month" class="plan-label">
                            <div class="plan-icon">
                                <i class="fas fa-calendar"></i>
                            </div>
                            <h5>1 Mes</h5>
                            <div class="plan-price">$29.99</div>
                            <div class="plan-features">
                                <span>✓ Acceso completo</span>
                                <span>✓ Soporte técnico</span>
                                <span>✓ Actualizaciones</span>
                            </div>
                        </label>
                    </div>
                    
                    <div class="plan-card popular">
                        <div class="plan-badge">Más Popular</div>
                        <input type="radio" id="plan3Months" name="renewalPeriod" value="3_months" checked>
                        <label for="plan3Months" class="plan-label">
                            <div class="plan-icon">
                                <i class="fas fa-calendar-check"></i>
                            </div>
                            <h5>3 Meses</h5>
                            <div class="plan-price">$79.99 <span class="plan-discount">$89.97</span></div>
                            <div class="plan-savings">Ahorras $9.98</div>
                            <div class="plan-features">
                                <span>✓ Acceso completo</span>
                                <span>✓ Soporte prioritario</span>
                                <span>✓ Descuento del 11%</span>
                            </div>
                        </label>
                    </div>
                    
                    <div class="plan-card">
                        <input type="radio" id="plan6Months" name="renewalPeriod" value="6_months">
                        <label for="plan6Months" class="plan-label">
                            <div class="plan-icon">
                                <i class="fas fa-calendar-plus"></i>
                            </div>
                            <h5>6 Meses</h5>
                            <div class="plan-price">$149.99 <span class="plan-discount">$179.94</span></div>
                            <div class="plan-savings">Ahorras $29.95</div>
                            <div class="plan-features">
                                <span>✓ Acceso completo</span>
                                <span>✓ Soporte VIP</span>
                                <span>✓ Descuento del 17%</span>
                            </div>
                        </label>
                    </div>
                    
                    <div class="plan-card best-value">
                        <div class="plan-badge">Mejor Valor</div>
                        <input type="radio" id="plan1Year" name="renewalPeriod" value="1_year">
                        <label for="plan1Year" class="plan-label">
                            <div class="plan-icon">
                                <i class="fas fa-crown"></i>
                            </div>
                            <h5>1 Año</h5>
                            <div class="plan-price">$279.99 <span class="plan-discount">$359.88</span></div>
                            <div class="plan-savings">Ahorras $79.89</div>
                            <div class="plan-features">
                                <span>✓ Acceso completo</span>
                                <span>✓ Soporte VIP 24/7</span>
                                <span>✓ Descuento del 22%</span>
                            </div>
                        </label>
                    </div>
                </div>
                
                <!-- Promoción Actual -->
                <div class="promo-section">
                    <div class="promo-card">
                        <div class="promo-header">
                            <i class="fas fa-gift"></i>
                            <h4>¡Promoción Especial Activa!</h4>
                        </div>
                        <div class="promo-content">
                            <p>Descuento adicional del 15% en renovaciones de 3 meses o más</p>
                            <label class="promo-checkbox">
                                <input type="checkbox" id="wantsCurrentPromo" name="wantsCurrentPromo">
                                <span class="checkmark"></span>
                                Quiero aplicar la promoción actual
                            </label>
                        </div>
                    </div>
                </div>
                
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="prevRenewalStep(1)">
                        <i class="fas fa-arrow-left"></i> Anterior
                    </button>
                    <button type="button" class="btn btn-primary" onclick="nextRenewalStep(3)">
                        Siguiente <i class="fas fa-arrow-right"></i>
                    </button>
                </div>
            </div>
            
            <!-- Paso 3: Comprobante de Pago -->
            <div class="form-step" id="renewalStep3">
                <div class="step-header">
                    <h4><i class="fas fa-receipt"></i> Comprobante de Pago</h4>
                    <p>Sube tu comprobante de pago para procesar la renovación</p>
                </div>
                
                <div class="payment-info">
                    <div class="payment-methods">
                        <h5>Métodos de Pago Disponibles:</h5>
                        <div class="payment-grid">
                            <div class="payment-method">
                                <i class="fas fa-university"></i>
                                <span>Transferencia Bancaria</span>
                            </div>
                            <div class="payment-method">
                                <i class="fas fa-mobile-alt"></i>
                                <span>Nequi / Daviplata</span>
                            </div>
                            <div class="payment-method">
                                <i class="fas fa-credit-card"></i>
                                <span>PSE</span>
                            </div>
                            <div class="payment-method">
                                <i class="fab fa-paypal"></i>
                                <span>PayPal</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="upload-section">
                        <div class="upload-area" id="uploadArea">
                            <div class="upload-icon">
                                <i class="fas fa-cloud-upload-alt"></i>
                            </div>
                            <div class="upload-text">
                                <h4>Arrastra tu comprobante aquí</h4>
                                <p>o haz clic para seleccionar archivo</p>
                                <small>Formatos: JPG, PNG, PDF (máx. 5MB)</small>
                            </div>
                            <input type="file" id="paymentProof" name="paymentProof" accept="image/*,.pdf" style="display: none;">
                        </div>
                        
                        <div id="uploadPreview" class="upload-preview" style="display: none;">
                            <div class="preview-content">
                                <div class="preview-icon">
                                    <i class="fas fa-file-image"></i>
                                </div>
                                <div class="preview-info">
                                    <div class="file-name"></div>
                                    <div class="file-size"></div>
                                </div>
                                <button type="button" class="remove-file" onclick="removeUploadedFile()">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="prevRenewalStep(2)">
                        <i class="fas fa-arrow-left"></i> Anterior
                    </button>
                    <button type="button" class="btn btn-primary" onclick="nextRenewalStep(4)">
                        Siguiente <i class="fas fa-arrow-right"></i>
                    </button>
                </div>
            </div>
            
            <!-- Paso 4: Confirmación -->
            <div class="form-step" id="renewalStep4">
                <div class="step-header">
                    <h4><i class="fas fa-check-circle"></i> Confirmar Renovación</h4>
                    <p>Revisa los detalles de tu renovación antes de enviar</p>
                </div>
                
                <div class="renewal-summary">
                    <div class="summary-section">
                        <h5>Resumen de Renovación</h5>
                        <div class="summary-item">
                            <span>Plan seleccionado:</span>
                            <span id="summaryPlan">-</span>
                        </div>
                        <div class="summary-item">
                            <span>ID de Plataforma:</span>
                            <span id="summaryPlatformId">-</span>
                        </div>
                        <div class="summary-item">
                            <span>Promoción aplicada:</span>
                            <span id="summaryPromo">-</span>
                        </div>
                        <div class="summary-item">
                            <span>Comprobante:</span>
                            <span id="summaryPayment">-</span>
                        </div>
                    </div>
                    
                    <div class="terms-section">
                        <label class="checkbox-label">
                            <input type="checkbox" id="renewalTerms" name="acceptTerms" required>
                            <span class="checkmark"></span>
                            Acepto los términos y condiciones de renovación
                        </label>
                    </div>
                </div>
                
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="prevRenewalStep(3)">
                        <i class="fas fa-arrow-left"></i> Anterior
                    </button>
                    <button type="submit" class="btn btn-success" id="submitRenewalBtn">
                        <i class="fas fa-paper-plane"></i> Enviar Solicitud de Renovación
                    </button>
                </div>
            </div>
        </form>
        
        </div> <!-- Cierre de modal-body -->

        <!-- Indicador de progreso -->
        <div class="progress-indicator">
            <div class="progress-step active" data-step="1">1</div>
            <div class="progress-step" data-step="2">2</div>
            <div class="progress-step" data-step="3">3</div>
            <div class="progress-step" data-step="4">4</div>
        </div>
    </div>
</div>

<style>
/* Estilos específicos para el modal de renovación */
.renewal-modal {
    max-width: 900px;
}

.full-width {
    grid-column: 1 / -1;
}

.renewal-plans {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-lg);
    margin-bottom: var(--space-xl);
}

.plan-card {
    position: relative;
    border-radius: var(--radius-xl);
    overflow: hidden;
}

.plan-card input[type="radio"] {
    display: none;
}

.plan-label {
    display: block;
    padding: var(--space-lg);
    background: var(--gradient-surface);
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-xl);
    cursor: pointer;
    transition: var(--transition-normal);
    text-align: center;
    height: 100%;
}

.plan-card input[type="radio"]:checked + .plan-label {
    border-color: var(--primary-color);
    background: var(--gradient-elevated);
    box-shadow: var(--shadow-md), var(--shadow-glow);
    transform: translateY(-4px);
}

.plan-badge {
    position: absolute;
    top: 0.5rem;
    left: 50%;
    transform: translateX(-50%);
    background: var(--gradient-primary);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: var(--radius-full);
    font-size: 0.7rem;
    font-weight: 600;
    z-index: 10;
}

.plan-card.best-value .plan-badge {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
}

.plan-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-md);
    font-size: 1.5rem;
    color: white;
}

.plan-price {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.plan-discount {
    font-size: 1rem;
    text-decoration: line-through;
    color: var(--text-muted);
    margin-left: 0.5rem;
}

.plan-savings {
    color: var(--success-color);
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: var(--space-md);
}

.plan-features {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.plan-features span {
    font-size: 0.85rem;
    color: var(--text-secondary);
}

.promo-section {
    margin-bottom: var(--space-xl);
}

.promo-card {
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.3);
    border-radius: var(--radius-lg);
    padding: var(--space-lg);
}

.promo-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: var(--space-md);
    color: var(--primary-color);
}

.promo-checkbox {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    margin-top: var(--space-md);
}

.payment-methods {
    margin-bottom: var(--space-xl);
}

.payment-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--space-md);
    margin-top: var(--space-md);
}

.payment-method {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: var(--space-md);
    background: var(--gradient-surface);
    border-radius: var(--radius-lg);
    text-align: center;
}

.payment-method i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.upload-area {
    border: 2px dashed rgba(255, 255, 255, 0.3);
    border-radius: var(--radius-lg);
    padding: var(--space-xl);
    text-align: center;
    cursor: pointer;
    transition: var(--transition-normal);
}

.upload-area:hover {
    border-color: var(--primary-color);
    background: rgba(255, 255, 255, 0.02);
}

.upload-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: var(--space-md);
}

.upload-preview {
    background: var(--gradient-surface);
    border-radius: var(--radius-lg);
    padding: var(--space-lg);
}

.preview-content {
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.preview-icon {
    font-size: 2rem;
    color: var(--primary-color);
}

.remove-file {
    background: var(--gradient-surface);
    border: none;
    color: var(--text-secondary);
    padding: 0.5rem;
    border-radius: 50%;
    cursor: pointer;
    margin-left: auto;
}

.renewal-summary {
    background: rgba(255, 255, 255, 0.03);
    border-radius: var(--radius-lg);
    padding: var(--space-lg);
}

.summary-item {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.summary-item:last-child {
    border-bottom: none;
}

.terms-section {
    margin-top: var(--space-lg);
    padding-top: var(--space-lg);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

@media (max-width: 768px) {
    .renewal-plans {
        grid-template-columns: 1fr;
    }
    
    .payment-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}
</style>
