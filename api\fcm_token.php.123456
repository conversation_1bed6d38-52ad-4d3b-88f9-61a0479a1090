<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Log all requests for debugging
error_log("🔥 FCM Token endpoint called - Method: " . $_SERVER['REQUEST_METHOD']);
error_log("🔥 FCM Token endpoint called - URI: " . $_SERVER['REQUEST_URI']);

// Only allow POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        "success" => false,
        "error" => "Method not allowed",
        "message" => "Solo se permite POST",
        "received_method" => $_SERVER['REQUEST_METHOD']
    ]);
    exit;
}

try {
    // Get JSON input
    $input = file_get_contents('php://input');
    error_log("📤 FCM Token raw input: " . $input);
    
    $data = json_decode($input, true);
    error_log("📤 FCM Token parsed data: " . print_r($data, true));
    
    // Validate required fields
    if (!isset($data['user_id']) || !isset($data['fcm_token'])) {
        throw new Exception("Missing required fields: user_id or fcm_token");
    }
    
    $user_id = $data['user_id'];
    $fcm_token = $data['fcm_token'];
    $device_type = $data['device_type'] ?? 'unknown';
    $app_version = $data['app_version'] ?? '1.0.0';
    $timestamp = $data['timestamp'] ?? time() * 1000;
    
    error_log("✅ FCM Token processing - User: $user_id, Device: $device_type");
    
    // For now, just log and return success (you can add database later)
    error_log("🔑 FCM Token received for user '$user_id': " . substr($fcm_token, 0, 50) . "...");
    
    // Success response
    echo json_encode([
        "success" => true,
        "message" => "FCM token received successfully",
        "data" => [
            "user_id" => $user_id,
            "device_type" => $device_type,
            "app_version" => $app_version,
            "token_length" => strlen($fcm_token),
            "timestamp" => date('Y-m-d H:i:s')
        ],
        "debug" => [
            "endpoint" => "fcm_token.php",
            "method" => $_SERVER['REQUEST_METHOD'],
            "content_type" => $_SERVER['CONTENT_TYPE'] ?? 'unknown'
        ]
    ]);
    
    error_log("✅ FCM Token response sent successfully for user: $user_id");
    
} catch (Exception $e) {
    error_log("❌ FCM Token error: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        "success" => false,
        "error" => "Internal server error",
        "message" => $e->getMessage(),
        "debug" => [
            "endpoint" => "fcm_token.php",
            "input_received" => !empty($input),
            "json_valid" => json_last_error() === JSON_ERROR_NONE
        ]
    ]);
}
?>