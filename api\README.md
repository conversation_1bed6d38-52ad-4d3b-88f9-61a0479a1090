# 🚀 RGS TOOL API REST

API REST para aplicación Android nativa del sistema RGS TOOL.

## 📋 Información General

- **Versión:** 1.0
- **Base URL:** `http://tu-dominio.com/api/`
- **Formato:** JSON
- **Charset:** UTF-8
- **CORS:** Habilitado

## 🔐 Autenticación

### Login de Usuario
```http
POST /api/auth/login
Content-Type: application/json

{
    "username": "usuario",
    "password": "contraseña"
}
```

**Respuesta exitosa:**
```json
{
    "success": true,
    "message": "Login exitoso",
    "data": {
        "user_id": 123,
        "username": "usuario",
        "token": "abc123...",
        "services": {
            "is_cliente_actual": true,
            "is_mavistv": false,
            "is_tvdigital": true,
            "is_limites507": false,
            "is_worldtv": false,
            "is_infest84": true,
            "is_rogsmediatv": false,
            "is_saul": false
        }
    },
    "timestamp": "2024-01-15 14:30:00"
}
```

## 📺 Contenido

### Contenido Trending
```http
GET /api/content/trending
```

**Respuesta:**
```json
{
    "success": true,
    "data": {
        "movies": [
            {
                "id": 550,
                "title": "Fight Club",
                "poster_path": "/pB8BM7pdSp6B6Ih7QZ4DrQ3PmJK.jpg",
                "release_date": "1999-10-15",
                "vote_average": 8.4,
                "overview": "Un empleado de oficina..."
            }
        ],
        "tv_shows": [
            {
                "id": 1399,
                "name": "Game of Thrones",
                "poster_path": "/u3bZgnGQ9T01sWNhyveQz0wH0Hl.jpg",
                "first_air_date": "2011-04-17",
                "vote_average": 9.3,
                "overview": "Siete reinos nobles..."
            }
        ]
    },
    "timestamp": "2024-01-15 14:30:00"
}
```

### Búsqueda de Contenido
```http
GET /api/content/search?q=breaking+bad
```

**Respuesta:**
```json
{
    "success": true,
    "data": {
        "query": "breaking bad",
        "results": [
            {
                "id": 1396,
                "name": "Breaking Bad",
                "media_type": "tv",
                "poster_path": "/ggFHVNu6YYI5L9pCfOacjizRGt.jpg",
                "first_air_date": "2008-01-20",
                "vote_average": 9.5,
                "overview": "Un profesor de química..."
            }
        ],
        "total_results": 1
    },
    "timestamp": "2024-01-15 14:30:00"
}
```

## 📦 Pedidos

### Crear Pedido
```http
POST /api/orders/create
Content-Type: application/json

{
    "user_id": 123,
    "tmdb_id": 1396,
    "title": "Breaking Bad",
    "media_type": "tv",
    "year": "2008"
}
```

**Respuesta:**
```json
{
    "success": true,
    "message": "Pedido creado exitosamente",
    "data": {
        "order_id": 456,
        "status": "Recibido"
    },
    "timestamp": "2024-01-15 14:30:00"
}
```

### Obtener Pedidos de Usuario
```http
GET /api/orders/user/123
```

**Respuesta:**
```json
{
    "success": true,
    "data": {
        "user_id": "123",
        "orders": [
            {
                "id": 456,
                "tmdb_id": 1396,
                "title": "Breaking Bad",
                "media_type": "tv",
                "year": "2008",
                "status": "Recibido",
                "country": "Panama",
                "city": "Panama City",
                "created_at": "2024-01-15 14:30:00",
                "updated_at": "2024-01-15 14:30:00"
            }
        ],
        "total_orders": 1
    },
    "timestamp": "2024-01-15 14:30:00"
}
```

## 📊 Estadísticas

### Estadísticas Generales
```http
GET /api/stats
```

**Respuesta:**
```json
{
    "success": true,
    "data": {
        "totals": {
            "orders": 1250,
            "users": 89
        },
        "orders_by_status": [
            {"status": "Recibido", "count": "45"},
            {"status": "Pendiente", "count": "23"},
            {"status": "Listo", "count": "1182"}
        ],
        "recent_activity": [
            {"date": "2024-01-15", "count": "12"},
            {"date": "2024-01-14", "count": "8"}
        ],
        "top_countries": [
            {"country": "Panama", "count": "856"},
            {"country": "Colombia", "count": "234"}
        ]
    },
    "timestamp": "2024-01-15 14:30:00"
}
```

## 👤 Usuario

### Perfil de Usuario
```http
GET /api/user/profile/123
```

**Respuesta:**
```json
{
    "success": true,
    "data": {
        "user": {
            "id": "123",
            "username": "usuario",
            "created_at": "2024-01-01 10:00:00",
            "last_login": "2024-01-15 14:25:00",
            "is_cliente_actual": "1",
            "is_mavistv": "0",
            "is_tvdigital": "1"
        },
        "stats": {
            "total_orders": 15,
            "orders_by_status": [
                {"status": "Listo", "count": "12"},
                {"status": "Pendiente", "count": "3"}
            ]
        }
    },
    "timestamp": "2024-01-15 14:30:00"
}
```

## ❌ Manejo de Errores

### Formato de Error Estándar
```json
{
    "success": false,
    "error": {
        "code": 1003,
        "type": "MISSING_PARAMETER",
        "message": "Campo requerido: user_id"
    },
    "timestamp": "2024-01-15 14:30:00"
}
```

### Códigos de Error Comunes

| Código | Tipo | Descripción |
|--------|------|-------------|
| 1001 | INVALID_ENDPOINT | Endpoint no válido |
| 1002 | METHOD_NOT_ALLOWED | Método HTTP no permitido |
| 1003 | MISSING_PARAMETER | Parámetro requerido faltante |
| 1004 | INVALID_PARAMETER | Parámetro inválido |
| 1005 | AUTHENTICATION_FAILED | Fallo en autenticación |
| 1007 | RESOURCE_NOT_FOUND | Recurso no encontrado |
| 1008 | DATABASE_ERROR | Error de base de datos |

## 🔧 Configuración para Android

### Ejemplo de uso en Android (Kotlin)
```kotlin
// Configuración base
const val BASE_URL = "http://tu-dominio.com/api/"

// Headers recomendados
val headers = mapOf(
    "Content-Type" to "application/json",
    "Accept" to "application/json",
    "User-Agent" to "RGS-TOOL-Android/1.0"
)

// Ejemplo de login
suspend fun login(username: String, password: String): LoginResponse {
    val body = mapOf(
        "username" to username,
        "password" to password
    )
    
    return apiService.post("auth/login", body)
}
```

## 🚀 URLs de Ejemplo

- **Login:** `POST http://tu-dominio.com/api/auth/login`
- **Trending:** `GET http://tu-dominio.com/api/content/trending`
- **Búsqueda:** `GET http://tu-dominio.com/api/content/search?q=avatar`
- **Crear Pedido:** `POST http://tu-dominio.com/api/orders/create`
- **Pedidos Usuario:** `GET http://tu-dominio.com/api/orders/user/123`
- **Estadísticas:** `GET http://tu-dominio.com/api/stats`
- **Perfil:** `GET http://tu-dominio.com/api/user/profile/123`

## 📝 Notas Importantes

1. **CORS:** Habilitado para todos los orígenes (configurar en producción)
2. **Rate Limiting:** Deshabilitado (habilitar en producción)
3. **Autenticación:** Token simple (considerar JWT en producción)
4. **Cache:** Deshabilitado (habilitar para mejor rendimiento)
5. **Logs:** Habilitados en `/api/logs/api.log`

## 🔒 Seguridad

- Usar HTTPS en producción
- Implementar rate limiting
- Validar y sanitizar todas las entradas
- Configurar CORS específicamente
- Implementar autenticación robusta (JWT)
- Monitorear logs regularmente
