<?php
/**
 * Probar el endpoint FCM con POST real
 */

header('Content-Type: application/json; charset=utf-8');

// Datos de prueba
$test_data = [
    'user_id' => 'Casa122',
    'fcm_token' => 'test_token_from_post_test',
    'device_type' => 'android',
    'app_version' => '1.0.0'
];

$json_data = json_encode($test_data);

// Probar ambos endpoints
$endpoints = [
    'routing_endpoint' => 'https://rogsmediatv.xyz/series/api/fcm/token',
    'direct_endpoint' => 'https://rogsmediatv.xyz/series/api/fcm_token.php'
];

$results = [];

foreach ($endpoints as $name => $url) {
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => [
                'Content-Type: application/json',
                'Content-Length: ' . strlen($json_data)
            ],
            'content' => $json_data,
            'timeout' => 10
        ]
    ]);

    $start_time = microtime(true);
    $response = @file_get_contents($url, false, $context);
    $end_time = microtime(true);
    
    $http_code = 'Unknown';
    if (isset($http_response_header[0])) {
        preg_match('/HTTP\/\d\.\d\s+(\d+)/', $http_response_header[0], $matches);
        $http_code = $matches[1] ?? 'Unknown';
    }
    
    $results[$name] = [
        'url' => $url,
        'http_code' => $http_code,
        'response_time_ms' => round(($end_time - $start_time) * 1000, 2),
        'response_length' => strlen($response ?: ''),
        'response_preview' => substr($response ?: '', 0, 200),
        'success' => $response !== false && $http_code == '200'
    ];
}

echo json_encode([
    'test_data' => $test_data,
    'results' => $results,
    'summary' => [
        'routing_works' => $results['routing_endpoint']['success'],
        'direct_works' => $results['direct_endpoint']['success'],
        'recommendation' => $results['direct_endpoint']['success'] ? 'Use fcm_token.php' : 'Both endpoints failed'
    ],
    'timestamp' => date('Y-m-d H:i:s')
], JSON_PRETTY_PRINT);
?>
