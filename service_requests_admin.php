<?php
session_start();

// Verificar si el usuario está logueado como admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

// Configuración MySQL
$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Error de conexión a la base de datos: " . $e->getMessage());
}

// Crear tablas si no existen
$pdo->exec("CREATE TABLE IF NOT EXISTS service_requests (
    id INT AUTO_INCREMENT PRIMARY KEY,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    preferred_username VARCHAR(50),
    preferred_password VARCHAR(100),
    admin_chooses_credentials BOOLEAN DEFAULT FALSE,
    service_type ENUM('trial', 'direct_purchase') NOT NULL,
    device_type ENUM('android', 'ios', 'smart_tv_samsung', 'smart_tv_lg', 'smart_tv_other', 'pc_windows', 'pc_mac', 'other') NOT NULL,
    device_info TEXT,
    mac_address VARCHAR(17),
    serial_key VARCHAR(100),
    terms_accepted BOOLEAN DEFAULT FALSE,
    status ENUM('pending', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending',
    admin_notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    assigned_to INT,
    client_notified BOOLEAN DEFAULT FALSE,
    admin_notified BOOLEAN DEFAULT TRUE,
    INDEX idx_status (status),
    INDEX idx_device_type (device_type),
    INDEX idx_created_at (created_at)
)");

$pdo->exec("CREATE TABLE IF NOT EXISTS service_communications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    request_id INT NOT NULL,
    sender_type ENUM('client', 'admin') NOT NULL,
    sender_name VARCHAR(100) NOT NULL,
    message TEXT NOT NULL,
    attachment_url VARCHAR(500),
    is_read BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_request_id (request_id),
    INDEX idx_sender_type (sender_type),
    INDEX idx_created_at (created_at)
)");

// Procesar acciones
$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'update_status':
                $request_id = (int)$_POST['request_id'];
                $new_status = $_POST['status'];
                $admin_notes = $_POST['admin_notes'] ?? '';
                
                $stmt = $pdo->prepare("UPDATE service_requests SET status = ?, admin_notes = ?, updated_at = NOW() WHERE id = ?");
                if ($stmt->execute([$new_status, $admin_notes, $request_id])) {
                    $message = "Estado actualizado correctamente.";
                    $message_type = 'success';
                } else {
                    $message = "Error al actualizar el estado.";
                    $message_type = 'error';
                }
                break;
                
            case 'send_message':
                $request_id = (int)$_POST['request_id'];
                $message_text = $_POST['message'];
                $sender_name = $_SESSION['admin_username'] ?? 'Administrador';
                
                $stmt = $pdo->prepare("INSERT INTO service_communications (request_id, sender_type, sender_name, message) VALUES (?, 'admin', ?, ?)");
                if ($stmt->execute([$request_id, $sender_name, $message_text])) {
                    $message = "Mensaje enviado correctamente.";
                    $message_type = 'success';
                } else {
                    $message = "Error al enviar el mensaje.";
                    $message_type = 'error';
                }
                break;
        }
    }
}

// Obtener filtros
$status_filter = $_GET['status'] ?? 'all';
$device_filter = $_GET['device'] ?? 'all';
$service_filter = $_GET['service'] ?? 'all';

// Construir consulta con filtros
$where_conditions = [];
$params = [];

if ($status_filter !== 'all') {
    $where_conditions[] = "status = ?";
    $params[] = $status_filter;
}

if ($device_filter !== 'all') {
    $where_conditions[] = "device_type = ?";
    $params[] = $device_filter;
}

if ($service_filter !== 'all') {
    $where_conditions[] = "service_type = ?";
    $params[] = $service_filter;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Obtener solicitudes
$stmt = $pdo->prepare("SELECT * FROM service_requests $where_clause ORDER BY created_at DESC");
$stmt->execute($params);
$requests = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Obtener estadísticas
$stats_stmt = $pdo->query("
    SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
        SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as in_progress,
        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
        SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled,
        SUM(CASE WHEN service_type = 'trial' THEN 1 ELSE 0 END) as trials,
        SUM(CASE WHEN service_type = 'direct_purchase' THEN 1 ELSE 0 END) as purchases
    FROM service_requests
");
$stats = $stats_stmt->fetch(PDO::FETCH_ASSOC);

// Función para obtener comunicaciones de una solicitud
function getCommunications($pdo, $request_id) {
    $stmt = $pdo->prepare("SELECT * FROM service_communications WHERE request_id = ? ORDER BY created_at ASC");
    $stmt->execute([$request_id]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 Solicitudes de Servicio - Admin</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            /* Paleta de colores profesional con verde como principal */
            --primary-color: #10b981;
            --primary-dark: #059669;
            --primary-light: #34d399;
            --secondary-color: #1f2937;
            --dark-bg: #111827;
            --darker-bg: #030712;
            --surface: #374151;
            --surface-light: #4b5563;
            --surface-elevated: #6b7280;

            /* Colores de texto profesionales */
            --text-primary: #f9fafb;
            --text-secondary: #d1d5db;
            --text-muted: #9ca3af;
            --text-accent: #34d399;

            /* Colores de estado para solicitudes */
            --pending-color: #d97706;
            --in-progress-color: #2563eb;
            --completed-color: #16a34a;
            --cancelled-color: #dc2626;
            --trial-color: #8b5cf6;
            --purchase-color: #10b981;

            /* Gradientes corporativos 3D con verde */
            --gradient-primary: linear-gradient(135deg, #10b981 0%, #059669 50%, #047857 100%);
            --gradient-secondary: linear-gradient(135deg, #34d399 0%, #10b981 50%, #059669 100%);
            --gradient-surface: linear-gradient(135deg, #1f2937 0%, #374151 50%, #4b5563 100%);
            --gradient-hero: linear-gradient(135deg, #030712 0%, #111827 25%, #1f2937 50%, #374151 100%);
            --gradient-card: linear-gradient(145deg, #1f2937 0%, #374151 100%);
            --gradient-elevated: linear-gradient(145deg, #374151 0%, #4b5563 100%);

            /* Sombras 3D profesionales */
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            --shadow-3d: 0 8px 16px rgba(0, 0, 0, 0.15), 0 4px 8px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);
            --shadow-elevated: 0 12px 24px rgba(0, 0, 0, 0.2), 0 6px 12px rgba(0, 0, 0, 0.15);
            --shadow-glow: 0 0 20px rgba(16, 185, 129, 0.4);

            /* Espaciado consistente */
            --space-xs: 0.25rem;
            --space-sm: 0.5rem;
            --space-md: 1rem;
            --space-lg: 1.5rem;
            --space-xl: 2rem;
            --space-2xl: 3rem;

            /* Border radius moderno */
            --radius-sm: 6px;
            --radius-md: 8px;
            --radius-lg: 12px;
            --radius-xl: 16px;
            --radius-full: 9999px;

            /* Transiciones y efectos 3D */
            --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-bounce: 400ms cubic-bezier(0.68, -0.55, 0.265, 1.55);

            /* Transformaciones 3D */
            --transform-hover: translateY(-4px) scale(1.02);
            --transform-active: translateY(-2px) scale(0.98);
            --transform-3d: perspective(1000px) rotateX(2deg) rotateY(-2deg);
            --transform-card: perspective(1000px) rotateX(0deg) rotateY(0deg);

            /* Filtros profesionales */
            --blur-glass: blur(16px) saturate(180%);
            --blur-backdrop: blur(12px) saturate(150%);
            --brightness-hover: brightness(1.1);
            --contrast-elevated: contrast(1.05);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gradient-hero);
            background-attachment: fixed;
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
            font-feature-settings: "kern" 1, "liga" 1;
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            position: relative;
            overflow-x: hidden;
        }

        /* Efectos de fondo 3D para solicitudes */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 25% 25%, rgba(16, 185, 129, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(52, 211, 153, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 50% 50%, rgba(5, 150, 105, 0.06) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        /* Header Administrativo */
        .header {
            background: rgba(31, 41, 55, 0.95);
            backdrop-filter: var(--blur-glass);
            padding: var(--space-lg) 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.12);
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: var(--shadow-elevated);
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--gradient-primary);
            opacity: 0.8;
        }

        .header-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.5rem;
            font-weight: 800;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-decoration: none;
            letter-spacing: -0.025em;
        }

        .nav-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .nav-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.6rem 1rem;
            background: var(--gradient-card);
            border: 1px solid rgba(255, 255, 255, 0.12);
            color: var(--text-primary);
            text-decoration: none;
            border-radius: var(--radius-xl);
            backdrop-filter: var(--blur-backdrop);
            transition: var(--transition-normal);
            font-weight: 500;
            font-size: 0.9rem;
            cursor: pointer;
            box-shadow: var(--shadow-3d);
        }

        .nav-btn:hover {
            background: var(--gradient-elevated);
            border-color: var(--primary-color);
            transform: var(--transform-hover);
            box-shadow: var(--shadow-elevated), var(--shadow-glow);
        }

        .nav-btn.back-btn {
            background: var(--gradient-primary);
            border-color: var(--primary-color);
            color: white;
            box-shadow: var(--shadow-3d), var(--shadow-glow);
        }

        /* Contenido Principal */
        .main-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: var(--space-xl) 1.5rem;
        }

        .page-title {
            font-size: 2rem;
            font-weight: 800;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .page-subtitle {
            color: var(--text-secondary);
            font-size: 1.1rem;
            margin-bottom: var(--space-xl);
        }

        /* Estadísticas */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--space-lg);
            margin-bottom: var(--space-xl);
        }

        .stat-card {
            background: var(--gradient-card);
            border: 1px solid rgba(255, 255, 255, 0.12);
            border-radius: var(--radius-xl);
            backdrop-filter: var(--blur-backdrop);
            padding: var(--space-lg);
            transition: var(--transition-normal);
            box-shadow: var(--shadow-3d);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--gradient-primary);
            transform: scaleX(0);
            transition: var(--transition-normal);
        }

        .stat-card:hover {
            background: var(--gradient-elevated);
            border-color: var(--primary-color);
            transform: var(--transform-hover);
            box-shadow: var(--shadow-2xl), var(--shadow-glow);
        }

        .stat-card:hover::before {
            transform: scaleX(1);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 800;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        /* Filtros */
        .filters-section {
            background: var(--gradient-card);
            border: 1px solid rgba(255, 255, 255, 0.12);
            border-radius: var(--radius-xl);
            backdrop-filter: var(--blur-backdrop);
            padding: var(--space-lg);
            margin-bottom: var(--space-xl);
            box-shadow: var(--shadow-sm);
        }

        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--space-md);
            align-items: end;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .form-group label {
            font-weight: 600;
            color: var(--text-primary);
            font-size: 0.9rem;
        }

        .form-input {
            padding: 0.75rem;
            background: var(--gradient-surface);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: var(--radius-lg);
            color: var(--text-primary);
            font-size: 0.9rem;
            transition: var(--transition-normal);
            backdrop-filter: var(--blur-backdrop);
            box-shadow: var(--shadow-sm);
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: var(--shadow-md), var(--shadow-glow);
            transform: translateY(-2px);
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: var(--radius-lg);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-normal);
            display: flex;
            align-items: center;
            gap: 0.5rem;
            backdrop-filter: var(--blur-backdrop);
            box-shadow: var(--shadow-sm);
            text-decoration: none;
        }

        .btn-primary {
            background: var(--gradient-primary);
            color: white;
            box-shadow: var(--shadow-sm), var(--shadow-glow);
        }

        .btn-primary:hover {
            background: var(--gradient-primary);
            transform: var(--transform-hover);
            box-shadow: var(--shadow-md), var(--shadow-glow);
        }

        /* Tabla de solicitudes */
        .requests-table {
            background: var(--gradient-card);
            border: 1px solid rgba(255, 255, 255, 0.12);
            border-radius: var(--radius-xl);
            backdrop-filter: var(--blur-backdrop);
            overflow: hidden;
            box-shadow: var(--shadow-3d);
        }

        .table-header {
            background: var(--gradient-primary);
            padding: var(--space-lg);
            color: white;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .requests-list {
            max-height: 600px;
            overflow-y: auto;
        }

        .request-item {
            border-bottom: 1px solid rgba(255, 255, 255, 0.08);
            padding: var(--space-lg);
            transition: var(--transition-normal);
            cursor: pointer;
        }

        .request-item:hover {
            background: rgba(255, 255, 255, 0.03);
        }

        .request-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-md);
        }

        .request-id {
            font-weight: 600;
            color: var(--primary-color);
        }

        .request-status {
            padding: 0.25rem 0.75rem;
            border-radius: var(--radius-full);
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .status-pending {
            background: rgba(217, 119, 6, 0.2);
            color: var(--pending-color);
            border: 1px solid rgba(217, 119, 6, 0.3);
        }

        .status-in-progress {
            background: rgba(37, 99, 235, 0.2);
            color: var(--in-progress-color);
            border: 1px solid rgba(37, 99, 235, 0.3);
        }

        .status-completed {
            background: rgba(22, 163, 74, 0.2);
            color: var(--completed-color);
            border: 1px solid rgba(22, 163, 74, 0.3);
        }

        .status-cancelled {
            background: rgba(220, 38, 38, 0.2);
            color: var(--cancelled-color);
            border: 1px solid rgba(220, 38, 38, 0.3);
        }

        .request-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--space-md);
            margin-bottom: var(--space-md);
        }

        .info-item {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }

        .info-label {
            font-size: 0.8rem;
            color: var(--text-muted);
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .info-value {
            color: var(--text-primary);
            font-weight: 500;
        }

        .request-actions {
            display: flex;
            gap: var(--space-sm);
            flex-wrap: wrap;
        }

        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.8rem;
        }

        .btn-secondary {
            background: var(--gradient-surface);
            color: var(--text-primary);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .btn-secondary:hover {
            background: var(--gradient-elevated);
            border-color: var(--primary-color);
            transform: var(--transform-hover);
        }

        /* Modal */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            z-index: 2000;
            backdrop-filter: blur(8px);
        }

        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
            animation: fadeIn 0.3s ease;
        }

        .modal-content {
            background: var(--gradient-card);
            border: 1px solid rgba(255, 255, 255, 0.12);
            border-radius: var(--radius-xl);
            padding: var(--space-xl);
            max-width: 800px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: var(--shadow-2xl), var(--shadow-glow);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-lg);
            padding-bottom: var(--space-md);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .modal-title {
            font-size: 1.5rem;
            font-weight: 700;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .modal-close {
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: 1.5rem;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: var(--radius-lg);
            transition: var(--transition-normal);
        }

        .modal-close:hover {
            color: var(--text-primary);
            background: rgba(255, 255, 255, 0.1);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .header-container {
                flex-direction: column;
                gap: var(--space-md);
                text-align: center;
            }

            .nav-buttons {
                flex-wrap: wrap;
                justify-content: center;
            }

            .nav-btn span {
                display: none;
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }

            .filters-grid {
                grid-template-columns: 1fr;
            }

            .request-info {
                grid-template-columns: 1fr;
            }

            .request-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-container">
            <a href="#" class="logo">
                <i class="fas fa-hand-holding-heart"></i>
                <span>Solicitudes de Servicio</span>
            </a>

            <div class="nav-buttons">
                <a href="admin.php" class="nav-btn back-btn">
                    <i class="fas fa-arrow-left"></i>
                    <span>Volver al Admin</span>
                </a>
                <a href="tickets_admin.php" class="nav-btn">
                    <i class="fas fa-ticket-alt"></i>
                    <span>Tickets</span>
                </a>
                <a href="tv_channels_admin.php" class="nav-btn">
                    <i class="fas fa-tv"></i>
                    <span>Canales TV</span>
                </a>
                <a href="logout.php" class="nav-btn">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Salir</span>
                </a>
            </div>
        </div>
    </header>

    <!-- Contenido Principal -->
    <main class="main-content">
        <!-- Header de Página -->
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-hand-holding-heart"></i>
                Solicitudes de Servicio IPTV
            </h1>
            <p class="page-subtitle">
                Gestiona las solicitudes de servicio de los clientes con comunicación bidireccional y seguimiento completo.
            </p>
        </div>

        <!-- Mensajes -->
        <?php if (!empty($message)): ?>
        <div class="message <?php echo $message_type; ?>" style="padding: 1rem; margin-bottom: 1.5rem; border-radius: 12px; background: rgba(16, 185, 129, 0.15); border: 1px solid rgba(16, 185, 129, 0.3); color: #10b981;">
            <i class="fas fa-<?php echo $message_type === 'success' ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
            <?php echo htmlspecialchars($message); ?>
        </div>
        <?php endif; ?>

        <!-- Estadísticas -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['total'] ?? 0; ?></div>
                <div class="stat-label">Total Solicitudes</div>
            </div>

            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['pending'] ?? 0; ?></div>
                <div class="stat-label">Pendientes</div>
            </div>

            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['in_progress'] ?? 0; ?></div>
                <div class="stat-label">En Progreso</div>
            </div>

            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['completed'] ?? 0; ?></div>
                <div class="stat-label">Completadas</div>
            </div>

            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['trials'] ?? 0; ?></div>
                <div class="stat-label">Pruebas</div>
            </div>

            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['purchases'] ?? 0; ?></div>
                <div class="stat-label">Compras</div>
            </div>
        </div>

        <!-- Filtros -->
        <div class="filters-section">
            <form method="GET" class="filters-grid">
                <div class="form-group">
                    <label for="status">Estado</label>
                    <select name="status" id="status" class="form-input">
                        <option value="all">Todos los estados</option>
                        <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>Pendiente</option>
                        <option value="in_progress" <?php echo $status_filter === 'in_progress' ? 'selected' : ''; ?>>En progreso</option>
                        <option value="completed" <?php echo $status_filter === 'completed' ? 'selected' : ''; ?>>Completado</option>
                        <option value="cancelled" <?php echo $status_filter === 'cancelled' ? 'selected' : ''; ?>>Cancelado</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="device">Dispositivo</label>
                    <select name="device" id="device" class="form-input">
                        <option value="all">Todos los dispositivos</option>
                        <option value="android" <?php echo $device_filter === 'android' ? 'selected' : ''; ?>>Android</option>
                        <option value="ios" <?php echo $device_filter === 'ios' ? 'selected' : ''; ?>>iOS</option>
                        <option value="smart_tv_samsung" <?php echo $device_filter === 'smart_tv_samsung' ? 'selected' : ''; ?>>Smart TV Samsung</option>
                        <option value="smart_tv_lg" <?php echo $device_filter === 'smart_tv_lg' ? 'selected' : ''; ?>>Smart TV LG</option>
                        <option value="smart_tv_other" <?php echo $device_filter === 'smart_tv_other' ? 'selected' : ''; ?>>Otra Smart TV</option>
                        <option value="pc_windows" <?php echo $device_filter === 'pc_windows' ? 'selected' : ''; ?>>PC Windows</option>
                        <option value="pc_mac" <?php echo $device_filter === 'pc_mac' ? 'selected' : ''; ?>>Mac</option>
                        <option value="other" <?php echo $device_filter === 'other' ? 'selected' : ''; ?>>Otro</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="service">Tipo de Servicio</label>
                    <select name="service" id="service" class="form-input">
                        <option value="all">Todos los tipos</option>
                        <option value="trial" <?php echo $service_filter === 'trial' ? 'selected' : ''; ?>>Prueba</option>
                        <option value="direct_purchase" <?php echo $service_filter === 'direct_purchase' ? 'selected' : ''; ?>>Compra directa</option>
                    </select>
                </div>

                <div style="display: flex; gap: 0.5rem; align-items: end;">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter"></i>
                        Filtrar
                    </button>
                    <a href="?" class="btn btn-secondary">
                        <i class="fas fa-times"></i>
                        Limpiar
                    </a>
                </div>
            </form>
        </div>

        <!-- Lista de Solicitudes -->
        <div class="requests-table">
            <div class="table-header">
                <i class="fas fa-list"></i>
                Solicitudes de Servicio (<?php echo count($requests); ?>)
            </div>

            <div class="requests-list">
                <?php if (empty($requests)): ?>
                <div style="text-align: center; padding: 3rem; color: var(--text-secondary);">
                    <i class="fas fa-hand-holding-heart" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                    <h3 style="font-size: 1.2rem; margin-bottom: 0.5rem; color: var(--text-primary);">No hay solicitudes</h3>
                    <p>No se encontraron solicitudes con los filtros aplicados.</p>
                </div>
                <?php else: ?>
                    <?php foreach ($requests as $request): ?>
                    <div class="request-item" onclick="openRequestModal(<?php echo $request['id']; ?>)">
                        <div class="request-header">
                            <span class="request-id">#<?php echo $request['id']; ?></span>
                            <span class="request-status status-<?php echo $request['status']; ?>">
                                <?php echo ucfirst(str_replace('_', ' ', $request['status'])); ?>
                            </span>
                        </div>

                        <div class="request-info">
                            <div class="info-item">
                                <span class="info-label">Cliente</span>
                                <span class="info-value"><?php echo htmlspecialchars($request['first_name'] . ' ' . $request['last_name']); ?></span>
                            </div>

                            <div class="info-item">
                                <span class="info-label">Email</span>
                                <span class="info-value"><?php echo htmlspecialchars($request['email']); ?></span>
                            </div>

                            <div class="info-item">
                                <span class="info-label">Teléfono</span>
                                <span class="info-value"><?php echo htmlspecialchars($request['phone']); ?></span>
                            </div>

                            <div class="info-item">
                                <span class="info-label">Tipo de Servicio</span>
                                <span class="info-value"><?php echo $request['service_type'] === 'trial' ? 'Prueba' : 'Compra directa'; ?></span>
                            </div>

                            <div class="info-item">
                                <span class="info-label">Dispositivo</span>
                                <span class="info-value"><?php echo ucfirst(str_replace('_', ' ', $request['device_type'])); ?></span>
                            </div>

                            <div class="info-item">
                                <span class="info-label">Fecha</span>
                                <span class="info-value"><?php echo date('d/m/Y H:i', strtotime($request['created_at'])); ?></span>
                            </div>
                        </div>

                        <div class="request-actions" onclick="event.stopPropagation();">
                            <button onclick="openRequestModal(<?php echo $request['id']; ?>)" class="btn btn-primary btn-sm">
                                <i class="fas fa-eye"></i>
                                Ver Detalles
                            </button>
                            <button onclick="openCommunicationModal(<?php echo $request['id']; ?>)" class="btn btn-secondary btn-sm">
                                <i class="fas fa-comments"></i>
                                Comunicación
                            </button>
                            <button onclick="updateStatus(<?php echo $request['id']; ?>, 'in_progress')" class="btn btn-secondary btn-sm">
                                <i class="fas fa-play"></i>
                                En Progreso
                            </button>
                            <button onclick="updateStatus(<?php echo $request['id']; ?>, 'completed')" class="btn btn-secondary btn-sm">
                                <i class="fas fa-check"></i>
                                Completar
                            </button>
                        </div>
                    </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </main>

    <!-- Modal de Detalles de Solicitud -->
    <div id="requestModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-info-circle"></i>
                    Detalles de Solicitud
                </h3>
                <button class="modal-close" onclick="closeModal('requestModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="requestDetails">
                <!-- Los detalles se cargarán dinámicamente -->
            </div>
        </div>
    </div>

    <!-- Modal de Comunicación -->
    <div id="communicationModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-comments"></i>
                    Comunicación con Cliente
                </h3>
                <button class="modal-close" onclick="closeModal('communicationModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="communicationContent">
                <!-- El contenido se cargará dinámicamente -->
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        let currentRequestId = null;

        // Abrir modal de detalles
        function openRequestModal(requestId) {
            currentRequestId = requestId;
            fetch(`get_request_details.php?id=${requestId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('requestDetails').innerHTML = data.html;
                        document.getElementById('requestModal').classList.add('show');
                    } else {
                        alert('Error al cargar los detalles');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error al cargar los detalles');
                });
        }

        // Abrir modal de comunicación
        function openCommunicationModal(requestId) {
            currentRequestId = requestId;
            fetch(`get_request_communication.php?id=${requestId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('communicationContent').innerHTML = data.html;
                        document.getElementById('communicationModal').classList.add('show');
                        scrollToBottom();
                    } else {
                        alert('Error al cargar la comunicación');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error al cargar la comunicación');
                });
        }

        // Cerrar modal
        function closeModal(modalId) {
            document.getElementById(modalId).classList.remove('show');
        }

        // Actualizar estado
        function updateStatus(requestId, status) {
            if (confirm(`¿Estás seguro de cambiar el estado a "${status.replace('_', ' ')}"?`)) {
                const formData = new FormData();
                formData.append('action', 'update_status');
                formData.append('request_id', requestId);
                formData.append('status', status);
                formData.append('admin_notes', '');

                fetch('', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.text())
                .then(() => {
                    location.reload();
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error al actualizar el estado');
                });
            }
        }

        // Enviar mensaje
        function sendMessage() {
            const messageInput = document.getElementById('messageInput');
            const message = messageInput.value.trim();

            if (!message) {
                alert('Por favor escribe un mensaje');
                return;
            }

            const formData = new FormData();
            formData.append('action', 'send_message');
            formData.append('request_id', currentRequestId);
            formData.append('message', message);

            fetch('', {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(() => {
                messageInput.value = '';
                openCommunicationModal(currentRequestId); // Recargar comunicación
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error al enviar el mensaje');
            });
        }

        // Scroll al final de la comunicación
        function scrollToBottom() {
            const chatContainer = document.querySelector('.chat-messages');
            if (chatContainer) {
                chatContainer.scrollTop = chatContainer.scrollHeight;
            }
        }

        // Cerrar modales al hacer clic fuera
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('modal')) {
                e.target.classList.remove('show');
            }
        });

        // Auto-refresh cada 30 segundos
        setInterval(() => {
            if (!document.querySelector('.modal.show')) {
                location.reload();
            }
        }, 30000);
    </script>
</body>
</html>
