<?php
session_start();

// Verificar autenticación de admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

require_once 'backupsql/backup_database.php';

$message = '';
$message_type = '';

// Procesar acciones
if ($_POST['action'] ?? '' === 'create_backup') {
    $result = createDatabaseBackup($db_config, $backup_config);
    
    if ($result['success']) {
        $message = "✅ Backup creado exitosamente: {$result['filename']} ({$result['size']})";
        $message_type = 'success';
    } else {
        $message = "❌ Error al crear backup: {$result['error']}";
        $message_type = 'error';
    }
}

if ($_GET['action'] ?? '' === 'download' && !empty($_GET['file'])) {
    $filename = basename($_GET['file']);
    $filepath = __DIR__ . '/backupsql/' . $filename;
    
    if (file_exists($filepath) && strpos($filename, 'backup_rgstool_') === 0) {
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Content-Length: ' . filesize($filepath));
        readfile($filepath);
        exit;
    }
}

if ($_POST['action'] ?? '' === 'delete_backup' && !empty($_POST['filename'])) {
    $filename = basename($_POST['filename']);
    $filepath = __DIR__ . '/backupsql/' . $filename;
    
    if (file_exists($filepath) && strpos($filename, 'backup_rgstool_') === 0) {
        if (unlink($filepath)) {
            $message = "✅ Backup eliminado: {$filename}";
            $message_type = 'success';
        } else {
            $message = "❌ Error al eliminar backup";
            $message_type = 'error';
        }
    }
}

// Obtener lista de backups
$backups = getBackupsList(__DIR__ . '/backupsql');
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>💾 Gestor de Backups - RGS TOOL</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>💾</text></svg>">
    <style>
        :root {
            /* Paleta de colores profesional para soporte técnico */
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #1e293b;
            --dark-bg: #0f172a;
            --darker-bg: #020617;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --accent-color: #10b981;
            --accent-dark: #059669;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --success-color: #10b981;
            --info-color: #06b6d4;
            --border-color: #334155;
            --border-light: #475569;
            --gradient-primary: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            --gradient-secondary: linear-gradient(135deg, #10b981 0%, #059669 100%);
            --gradient-dark: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
            --gradient-bg: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            --shadow-light: 0 2px 8px rgba(0,0,0,0.3);
            --shadow-medium: 0 4px 16px rgba(0,0,0,0.4);
            --shadow-heavy: 0 8px 32px rgba(0,0,0,0.6);
            --border-radius: 12px;
            --border-radius-lg: 16px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gradient-bg);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .back-link {
            color: var(--accent-color);
            text-decoration: none;
            margin-bottom: 2rem;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 500;
            transition: var(--transition);
        }

        .back-link:hover {
            color: #3bc73c;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            background: linear-gradient(135deg, var(--accent-color), #28a745);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .header p {
            color: var(--text-secondary);
            font-size: 1.1rem;
        }

        .message {
            padding: 1rem 1.5rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            font-weight: 500;
        }

        .message.success {
            background: rgba(40, 167, 69, 0.1);
            border: 1px solid var(--success-color);
            color: var(--success-color);
        }

        .message.error {
            background: rgba(220, 53, 69, 0.1);
            border: 1px solid var(--error-color);
            color: var(--error-color);
        }

        .backup-controls {
            background: var(--secondary-color);
            padding: 2rem;
            border-radius: 12px;
            margin-bottom: 2rem;
            border: 1px solid var(--border-color);
        }

        .backup-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .info-card {
            background: var(--primary-color);
            padding: 1.5rem;
            border-radius: 8px;
            border: 1px solid var(--border-color);
            text-align: center;
        }

        .info-number {
            font-size: 2rem;
            color: var(--accent-color);
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .info-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .btn {
            padding: 1rem 2rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            font-size: 1rem;
            transition: var(--transition);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: var(--accent-color);
            color: var(--primary-color);
        }

        .btn-primary:hover {
            background: #3bc73c;
            transform: translateY(-2px);
        }

        .btn-small {
            padding: 0.5rem 1rem;
            font-size: 0.8rem;
        }

        .btn-danger {
            background: var(--error-color);
            color: white;
        }

        .btn-info {
            background: #17a2b8;
            color: white;
        }

        .backups-table {
            background: var(--secondary-color);
            border-radius: 12px;
            overflow: hidden;
            border: 1px solid var(--border-color);
        }

        .table-header {
            background: var(--primary-color);
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
        }

        .table-header h3 {
            color: var(--accent-color);
            margin: 0;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        th {
            background: var(--primary-color);
            color: var(--accent-color);
            font-weight: 600;
        }

        tr:hover {
            background: rgba(70, 211, 71, 0.05);
        }

        .no-backups {
            text-align: center;
            padding: 3rem;
            color: var(--text-secondary);
        }

        .no-backups i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .backup-info {
                grid-template-columns: 1fr;
            }
            
            .actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="admin.php" class="back-link">
            <i class="fas fa-arrow-left"></i>
            Volver al Admin
        </a>

        <div class="header">
            <h1>💾 Gestor de Backups</h1>
            <p>Administra los backups de la base de datos de RGS TOOL</p>
        </div>

        <?php if ($message): ?>
        <div class="message <?php echo $message_type; ?>">
            <?php echo htmlspecialchars($message); ?>
        </div>
        <?php endif; ?>

        <div class="backup-controls">
            <div class="backup-info">
                <div class="info-card">
                    <div class="info-number"><?php echo count($backups); ?></div>
                    <div class="info-label">Backups Disponibles</div>
                </div>
                <div class="info-card">
                    <div class="info-number"><?php echo $backup_config['max_backups']; ?></div>
                    <div class="info-label">Máximo Permitido</div>
                </div>
                <div class="info-card">
                    <div class="info-number"><?php echo $backup_config['compress'] ? 'SÍ' : 'NO'; ?></div>
                    <div class="info-label">Compresión</div>
                </div>
            </div>

            <form method="POST" style="text-align: center;">
                <input type="hidden" name="action" value="create_backup">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    Crear Nuevo Backup
                </button>
            </form>
        </div>

        <div class="backups-table">
            <div class="table-header">
                <h3>📋 Lista de Backups</h3>
            </div>
            
            <?php if (empty($backups)): ?>
            <div class="no-backups">
                <i class="fas fa-database"></i>
                <h3>No hay backups disponibles</h3>
                <p>Crea tu primer backup usando el botón de arriba</p>
            </div>
            <?php else: ?>
            <table>
                <thead>
                    <tr>
                        <th>📁 Archivo</th>
                        <th>📅 Fecha</th>
                        <th>💾 Tamaño</th>
                        <th>⚙️ Acciones</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($backups as $backup): ?>
                    <tr>
                        <td>
                            <code><?php echo htmlspecialchars($backup['filename']); ?></code>
                        </td>
                        <td><?php echo $backup['date']; ?></td>
                        <td><?php echo $backup['size']; ?></td>
                        <td>
                            <div class="actions">
                                <a href="?action=download&file=<?php echo urlencode($backup['filename']); ?>" 
                                   class="btn btn-small btn-info">
                                    <i class="fas fa-download"></i>
                                    Descargar
                                </a>
                                <form method="POST" style="display: inline;" 
                                      onsubmit="return confirm('¿Estás seguro de eliminar este backup?')">
                                    <input type="hidden" name="action" value="delete_backup">
                                    <input type="hidden" name="filename" value="<?php echo htmlspecialchars($backup['filename']); ?>">
                                    <button type="submit" class="btn btn-small btn-danger">
                                        <i class="fas fa-trash"></i>
                                        Eliminar
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            <?php endif; ?>
        </div>

        <div style="margin-top: 2rem; padding: 1.5rem; background: rgba(70, 211, 71, 0.1); border: 1px solid var(--accent-color); border-radius: 8px;">
            <h4 style="color: var(--accent-color); margin-bottom: 1rem;">
                <i class="fas fa-info-circle"></i>
                Información sobre Backups
            </h4>
            <ul style="color: var(--text-secondary); line-height: 1.8;">
                <li><strong>Automático:</strong> Los backups antiguos se eliminan automáticamente (máximo <?php echo $backup_config['max_backups']; ?>)</li>
                <li><strong>Compresión:</strong> Los archivos se comprimen con gzip para ahorrar espacio</li>
                <li><strong>Contenido:</strong> Incluye estructura y datos de todas las tablas</li>
                <li><strong>Seguridad:</strong> Los archivos están protegidos contra acceso directo</li>
                <li><strong>Migración:</strong> Usa estos backups para migrar a otro hosting</li>
            </ul>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
</body>
</html>
