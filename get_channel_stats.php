<?php
session_start();

// Verificar si el usuario está logueado como admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    http_response_code(403);
    echo json_encode(['error' => 'No autorizado']);
    exit;
}

// Configuración MySQL
$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Error de base de datos']);
    exit;
}

// Obtener estadísticas actualizadas
try {
    $stmt = $pdo->query("
        SELECT 
            COUNT(*) as total,
            COUNT(DISTINCT category) as categories,
            SUM(CASE WHEN status = 'online' THEN 1 ELSE 0 END) as online,
            SUM(CASE WHEN status = 'offline' THEN 1 ELSE 0 END) as offline,
            SUM(CASE WHEN status = 'unknown' THEN 1 ELSE 0 END) as unknown,
            AVG(CASE WHEN status = 'online' AND response_time IS NOT NULL THEN response_time END) as avg_response_time,
            COUNT(CASE WHEN last_check >= DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN 1 END) as recently_checked
        FROM tv_channels
    ");
    
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Formatear respuesta
    $response = [
        'total' => (int)$stats['total'],
        'categories' => (int)$stats['categories'],
        'online' => (int)$stats['online'],
        'offline' => (int)$stats['offline'],
        'unknown' => (int)$stats['unknown'],
        'avg_response_time' => $stats['avg_response_time'] ? round($stats['avg_response_time']) : null,
        'recently_checked' => (int)$stats['recently_checked'],
        'last_update' => date('Y-m-d H:i:s')
    ];
    
    // Calcular porcentajes
    if ($response['total'] > 0) {
        $response['online_percentage'] = round(($response['online'] / $response['total']) * 100, 1);
        $response['offline_percentage'] = round(($response['offline'] / $response['total']) * 100, 1);
        $response['unknown_percentage'] = round(($response['unknown'] / $response['total']) * 100, 1);
    } else {
        $response['online_percentage'] = 0;
        $response['offline_percentage'] = 0;
        $response['unknown_percentage'] = 0;
    }
    
    header('Content-Type: application/json');
    echo json_encode($response);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Error al obtener estadísticas',
        'details' => $e->getMessage()
    ]);
}
?>
