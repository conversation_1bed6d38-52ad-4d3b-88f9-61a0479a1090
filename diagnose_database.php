<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Diagnóstico de Base de Datos</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .header {
            text-align: center;
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            border-radius: 8px;
        }
        
        .status {
            padding: 0.5rem 1rem;
            border-radius: 6px;
            margin: 0.5rem 0;
            font-weight: 600;
        }
        
        .status.success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }
        
        .status.error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fecaca;
        }
        
        .status.warning {
            background: #fef3c7;
            color: #92400e;
            border: 1px solid #fde68a;
        }
        
        .status.info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #93c5fd;
        }
        
        .section {
            margin: 2rem 0;
            padding: 1.5rem;
            background: #f8fafc;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }
        
        .section h3 {
            margin-top: 0;
            color: #374151;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        th, td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }
        
        th {
            background: #f8fafc;
            font-weight: 600;
            color: #374151;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            background: #10b981;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            margin: 0.5rem;
        }
        
        .btn:hover {
            background: #059669;
        }
        
        .btn.secondary {
            background: #6b7280;
        }
        
        .btn.secondary:hover {
            background: #4b5563;
        }
        
        pre {
            background: #1f2937;
            color: #f8fafc;
            padding: 1rem;
            border-radius: 8px;
            overflow-x: auto;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Diagnóstico de Base de Datos</h1>
            <p>Verificación completa del estado de la base de datos y tablas</p>
        </div>

        <?php
        $db_host = 'localhost';
        $db_name = 'u170528143_php';
        $db_user = 'u170528143_php';
        $db_pass = '&T4v!$=i';

        try {
            echo '<div class="section">';
            echo '<h3>🔌 Conexión a Base de Datos</h3>';
            
            $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            echo '<div class="status success">✅ Conexión exitosa a la base de datos</div>';
            echo '<p><strong>Host:</strong> ' . $db_host . '</p>';
            echo '<p><strong>Base de datos:</strong> ' . $db_name . '</p>';
            echo '<p><strong>Usuario:</strong> ' . $db_user . '</p>';
            echo '</div>';
            
            // Verificar versión de MySQL
            echo '<div class="section">';
            echo '<h3>📊 Información del Servidor</h3>';
            $version = $pdo->query("SELECT VERSION()")->fetchColumn();
            echo '<p><strong>Versión MySQL:</strong> ' . $version . '</p>';
            
            $charset = $pdo->query("SELECT @@character_set_database")->fetchColumn();
            echo '<p><strong>Charset:</strong> ' . $charset . '</p>';
            echo '</div>';
            
            // Listar todas las tablas
            echo '<div class="section">';
            echo '<h3>📋 Tablas Existentes</h3>';
            $stmt = $pdo->query("SHOW TABLES");
            $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            if (empty($tables)) {
                echo '<div class="status warning">⚠️ No se encontraron tablas en la base de datos</div>';
            } else {
                echo '<div class="status success">✅ Se encontraron ' . count($tables) . ' tablas</div>';
                echo '<table>';
                echo '<thead><tr><th>Tabla</th><th>Estado</th></tr></thead>';
                echo '<tbody>';
                
                $requiredTables = ['service_config', 'service_requests', 'service_communications'];
                
                foreach ($tables as $table) {
                    $isRequired = in_array($table, $requiredTables);
                    $status = $isRequired ? '<span style="color: #10b981;">✅ Requerida</span>' : '<span style="color: #6b7280;">ℹ️ Opcional</span>';
                    echo "<tr><td>$table</td><td>$status</td></tr>";
                }
                echo '</tbody></table>';
            }
            echo '</div>';
            
            // Verificar tabla service_config específicamente
            echo '<div class="section">';
            echo '<h3>🔧 Tabla service_config</h3>';
            
            $serviceConfigExists = in_array('service_config', $tables);
            
            if ($serviceConfigExists) {
                echo '<div class="status success">✅ La tabla service_config existe</div>';
                
                // Mostrar estructura de la tabla
                $stmt = $pdo->query("DESCRIBE service_config");
                $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                echo '<h4>Estructura de la tabla:</h4>';
                echo '<table>';
                echo '<thead><tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Clave</th><th>Default</th></tr></thead>';
                echo '<tbody>';
                foreach ($columns as $column) {
                    echo '<tr>';
                    echo '<td>' . $column['Field'] . '</td>';
                    echo '<td>' . $column['Type'] . '</td>';
                    echo '<td>' . $column['Null'] . '</td>';
                    echo '<td>' . $column['Key'] . '</td>';
                    echo '<td>' . $column['Default'] . '</td>';
                    echo '</tr>';
                }
                echo '</tbody></table>';
                
                // Contar registros
                $count = $pdo->query("SELECT COUNT(*) FROM service_config")->fetchColumn();
                echo '<p><strong>Registros:</strong> ' . $count . '</p>';
                
                if ($count > 0) {
                    echo '<h4>Datos existentes:</h4>';
                    $stmt = $pdo->query("SELECT service_type, title, price, is_active FROM service_config ORDER BY service_type");
                    $configs = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    
                    echo '<table>';
                    echo '<thead><tr><th>Tipo</th><th>Título</th><th>Precio</th><th>Activo</th></tr></thead>';
                    echo '<tbody>';
                    foreach ($configs as $config) {
                        $active = $config['is_active'] ? '✅ Sí' : '❌ No';
                        echo '<tr>';
                        echo '<td>' . $config['service_type'] . '</td>';
                        echo '<td>' . $config['title'] . '</td>';
                        echo '<td>' . $config['price'] . '</td>';
                        echo '<td>' . $active . '</td>';
                        echo '</tr>';
                    }
                    echo '</tbody></table>';
                }
                
            } else {
                echo '<div class="status error">❌ La tabla service_config NO existe</div>';
                echo '<p>Esta tabla es necesaria para el sistema de configuración de servicios.</p>';
            }
            echo '</div>';
            
            // Verificar otras tablas importantes
            echo '<div class="section">';
            echo '<h3>📊 Estado de Tablas del Sistema</h3>';
            
            $systemTables = [
                'service_requests' => 'Solicitudes de servicio',
                'service_communications' => 'Comunicaciones de servicio',
                'uploaded_files' => 'Archivos subidos',
                'available_apps' => 'Aplicaciones disponibles'
            ];
            
            echo '<table>';
            echo '<thead><tr><th>Tabla</th><th>Descripción</th><th>Estado</th><th>Registros</th></tr></thead>';
            echo '<tbody>';
            
            foreach ($systemTables as $tableName => $description) {
                $exists = in_array($tableName, $tables);
                $status = $exists ? '<span style="color: #10b981;">✅ Existe</span>' : '<span style="color: #ef4444;">❌ No existe</span>';
                $count = 'N/A';
                
                if ($exists) {
                    try {
                        $count = $pdo->query("SELECT COUNT(*) FROM $tableName")->fetchColumn();
                    } catch (Exception $e) {
                        $count = 'Error';
                    }
                }
                
                echo '<tr>';
                echo '<td>' . $tableName . '</td>';
                echo '<td>' . $description . '</td>';
                echo '<td>' . $status . '</td>';
                echo '<td>' . $count . '</td>';
                echo '</tr>';
            }
            echo '</tbody></table>';
            echo '</div>';
            
        } catch (Exception $e) {
            echo '<div class="section">';
            echo '<div class="status error">❌ Error de conexión: ' . $e->getMessage() . '</div>';
            echo '</div>';
        }
        ?>

        <div class="section">
            <h3>🛠️ Acciones Disponibles</h3>
            <a href="setup_service_config.php" class="btn">🔧 Crear Tabla service_config</a>
            <a href="service_config_admin.php" class="btn">⚙️ Panel de Configuración</a>
            <a href="api_service_config.php?action=get_all" class="btn secondary">📡 Probar API</a>
            <a href="service_options.php" class="btn secondary">👁️ Ver Servicios</a>
        </div>

        <div class="section">
            <h3>📝 Script SQL Manual</h3>
            <p>Si necesitas crear la tabla manualmente, ejecuta este SQL:</p>
            <pre>CREATE TABLE service_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    service_type VARCHAR(20) NOT NULL,
    title VARCHAR(255) NOT NULL,
    subtitle VARCHAR(255),
    description TEXT,
    price VARCHAR(50),
    original_price VARCHAR(50),
    discount_percentage INT DEFAULT 0,
    features TEXT,
    button_text VARCHAR(100),
    button_color VARCHAR(20) DEFAULT 'primary',
    is_active TINYINT(1) DEFAULT 1,
    promotion_text VARCHAR(255),
    promotion_badge VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insertar datos por defecto
INSERT INTO service_config (service_type, title, subtitle, description, price, features, button_text, promotion_text, promotion_badge) VALUES
('trial', 'Prueba Gratuita', '24-48 horas de acceso completo', 'Experimenta nuestro servicio IPTV premium sin costo alguno.', 'GRATIS', '["Acceso completo por 24-48 horas","Más de 5000 canales en HD/4K","Deportes, películas, series y más","Compatible con todos los dispositivos","Soporte técnico incluido","Sin compromisos ni pagos"]', 'Solicitar Prueba Gratuita', '¡Prueba sin riesgo!', 'GRATIS'),
('purchase', 'Compra Directa', 'Acceso inmediato al mejor entretenimiento', 'Obtén acceso instantáneo a nuestro servicio IPTV premium.', '$29.99', '["Más de 5000 canales premium","Películas y series en 4K","Deportes en vivo y PPV","Compatible con Smart TV, móvil, PC","Soporte 24/7","Activación inmediata"]', 'Contratar Ahora', '¡Oferta especial por tiempo limitado!', '25% OFF'),
('renewal', 'Renovar Servicio', 'Continúa disfrutando sin interrupciones', 'Renueva tu servicio IPTV existente y mantén el acceso.', 'Desde $19.99', '["Descuentos por renovación","Planes de 1, 3, 6 meses y 1 año","Sin interrupciones en el servicio","Mantén tu configuración actual","Promociones exclusivas para clientes","Soporte prioritario"]', 'Renovar Mi Servicio', '¡Descuentos especiales para clientes existentes!', 'DESCUENTO');</pre>
        </div>
    </div>
</body>
</html>
