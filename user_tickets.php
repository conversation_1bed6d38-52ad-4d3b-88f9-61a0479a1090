<?php
session_start();
require_once 'config.php';

// Para demo, usar usuario por defecto si no está logueado
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['username'] = 'usuario';
}

$user_id = $_SESSION['user_id'];
$username = $_SESSION['username'] ?? 'usuario';

// Procesar creación de ticket
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_ticket'])) {
    try {
        $subject = clean_input($_POST['subject']);
        $description = clean_input($_POST['description']);
        $priority = clean_input($_POST['priority']);
        $category = clean_input($_POST['category']);

        // Validar campos requeridos
        if (empty($subject) || empty($description) || empty($priority) || empty($category)) {
            $error_message = "Por favor completa todos los campos requeridos.";
        } else {
            $stmt = $pdo->prepare("INSERT INTO support_tickets (user_id, subject, description, priority, category, status) VALUES (?, ?, ?, ?, ?, 'open')");
            $stmt->execute([$user_id, $subject, $description, $priority, $category]);

            $success_message = "Ticket creado correctamente. Te notificaremos cuando recibas una respuesta.";

            // Limpiar formulario
            $_POST = array();
        }
    } catch (Exception $e) {
        $error_message = "Error al crear ticket: " . $e->getMessage();
    }
}

// Obtener tickets del usuario
try {
    $stmt = $pdo->prepare("
        SELECT st.*, u.username as assigned_to_name
        FROM support_tickets st
        LEFT JOIN users u ON st.assigned_to = u.id
        WHERE st.user_id = ?
        ORDER BY st.created_at DESC
    ");
    $stmt->execute([$user_id]);
    $tickets = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $tickets = [];
    $error_message = "Error al cargar tickets: " . $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎫 Mis Tickets de Soporte - RGS</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e293b;
            --dark-bg: #0f172a;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --accent-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --success-color: #10b981;
            --border-color: #334155;
            --gradient-bg: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            --border-radius: 12px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gradient-bg);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
        }

        .header {
            background: var(--secondary-color);
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
            text-decoration: none;
        }

        .nav-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .nav-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            background: transparent;
            color: var(--text-secondary);
            text-decoration: none;
            border-radius: var(--border-radius);
            transition: var(--transition);
            font-weight: 500;
        }

        .nav-btn:hover {
            background: rgba(255,255,255,0.1);
            color: var(--text-primary);
        }

        .nav-btn.back-btn {
            background: var(--primary-color);
            color: white;
        }

        .main-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem 1.5rem;
        }

        .page-header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .page-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .page-subtitle {
            color: var(--text-secondary);
            font-size: 1.1rem;
        }

        .create-ticket-section {
            background: var(--secondary-color);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            padding: 2rem;
            margin-bottom: 3rem;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .form-label {
            font-weight: 500;
            color: var(--text-primary);
        }

        .form-input, .form-select, .form-textarea {
            padding: 0.75rem;
            background: var(--dark-bg);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            font-family: inherit;
            font-size: 0.9rem;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.15);
        }

        .form-textarea {
            min-height: 120px;
            resize: vertical;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: var(--transition);
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            opacity: 0.9;
        }

        .tickets-section {
            background: var(--secondary-color);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            overflow: hidden;
        }

        .tickets-header {
            background: var(--dark-bg);
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
        }

        .tickets-list {
            padding: 1.5rem;
        }

        .ticket-item {
            background: rgba(255,255,255,0.05);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            margin-bottom: 1rem;
            border-left: 4px solid var(--border-color);
            transition: var(--transition);
        }

        .ticket-item:hover {
            background: rgba(255,255,255,0.08);
        }

        .ticket-item.status-open {
            border-left-color: var(--warning-color);
        }

        .ticket-item.status-in_progress {
            border-left-color: var(--primary-color);
        }

        .ticket-item.status-resolved {
            border-left-color: var(--success-color);
        }

        .ticket-item.status-closed {
            border-left-color: var(--border-color);
        }

        .ticket-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .ticket-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .ticket-meta {
            display: flex;
            gap: 1rem;
            color: var(--text-secondary);
            font-size: 0.9rem;
            flex-wrap: wrap;
        }

        .status-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-open { 
            background: rgba(245, 158, 11, 0.2); 
            color: #f59e0b; 
        }

        .status-in_progress { 
            background: rgba(37, 99, 235, 0.2); 
            color: #2563eb; 
        }

        .status-resolved { 
            background: rgba(16, 185, 129, 0.2); 
            color: #10b981; 
        }

        .status-closed { 
            background: rgba(107, 114, 128, 0.2); 
            color: #6b7280; 
        }

        .priority-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .priority-low { 
            background: rgba(107, 114, 128, 0.2); 
            color: #6b7280; 
        }

        .priority-medium { 
            background: rgba(245, 158, 11, 0.2); 
            color: #f59e0b; 
        }

        .priority-high { 
            background: rgba(239, 68, 68, 0.2); 
            color: #ef4444; 
        }

        .priority-urgent { 
            background: rgba(220, 38, 38, 0.2); 
            color: #dc2626; 
        }

        .ticket-description {
            color: var(--text-secondary);
            margin-bottom: 1rem;
            line-height: 1.6;
        }

        .ticket-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .success-message {
            background: rgba(16, 185, 129, 0.2);
            color: var(--success-color);
            padding: 1rem;
            border-radius: var(--border-radius);
            margin-bottom: 1rem;
            border: 1px solid rgba(16, 185, 129, 0.3);
        }

        .empty-state {
            text-align: center;
            padding: 3rem;
            color: var(--text-secondary);
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }

            .ticket-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .ticket-actions {
                width: 100%;
                justify-content: flex-start;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-container">
            <a href="user_tickets.php" class="logo">
                <i class="fas fa-ticket-alt"></i>
                <span>Mis Tickets</span>
            </a>
            
            <div class="nav-buttons">
                <a href="index2.php" class="nav-btn back-btn">
                    <i class="fas fa-arrow-left"></i>
                    <span>Volver a Servicios</span>
                </a>
                <a href="index.php" class="nav-btn">
                    <i class="fas fa-home"></i>
                    <span>Inicio</span>
                </a>
            </div>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-ticket-alt" style="color: var(--primary-color);"></i>
                Mis Tickets de Soporte
            </h1>
            <p class="page-subtitle">
                Gestiona tus solicitudes de soporte técnico y mantente al día con las respuestas
            </p>
        </div>

        <?php if (isset($success_message)): ?>
        <div class="success-message">
            <i class="fas fa-check-circle"></i>
            <?php echo htmlspecialchars($success_message); ?>
        </div>
        <?php endif; ?>

        <!-- Crear Nuevo Ticket -->
        <div class="create-ticket-section">
            <h2 class="section-title">
                <i class="fas fa-plus"></i>
                Crear Nuevo Ticket
            </h2>
            
            <form method="POST">
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">Asunto</label>
                        <input type="text" name="subject" class="form-input" required placeholder="Describe brevemente tu problema">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Categoría</label>
                        <select name="category" class="form-select" required>
                            <option value="">Seleccionar categoría</option>
                            <option value="technical">Problema Técnico</option>
                            <option value="billing">Facturación</option>
                            <option value="account">Cuenta de Usuario</option>
                            <option value="content">Contenido/Canales</option>
                            <option value="app">Aplicaciones</option>
                            <option value="other">Otro</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Prioridad</label>
                        <select name="priority" class="form-select" required>
                            <option value="low">Baja</option>
                            <option value="medium" selected>Media</option>
                            <option value="high">Alta</option>
                            <option value="urgent">Urgente</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group" style="margin-bottom: 2rem;">
                    <label class="form-label">Descripción del Problema</label>
                    <textarea name="description" class="form-textarea" required placeholder="Describe detalladamente tu problema, incluyendo pasos para reproducirlo si es posible..."></textarea>
                </div>
                
                <button type="submit" name="create_ticket" class="btn btn-primary">
                    <i class="fas fa-paper-plane"></i>
                    Crear Ticket
                </button>
            </form>
        </div>

        <!-- Lista de Tickets -->
        <div class="tickets-section">
            <div class="tickets-header">
                <h2 class="section-title">
                    <i class="fas fa-list"></i>
                    Mis Tickets (<?php echo count($tickets); ?>)
                </h2>
            </div>
            
            <div class="tickets-list">
                <?php if (empty($tickets)): ?>
                <div class="empty-state">
                    <i class="fas fa-ticket-alt"></i>
                    <h3>No tienes tickets de soporte</h3>
                    <p>Cuando crees tu primer ticket, aparecerá aquí</p>
                </div>
                <?php else: ?>
                    <?php foreach ($tickets as $ticket): ?>
                    <div class="ticket-item status-<?php echo $ticket['status']; ?>">
                        <div class="ticket-header">
                            <div>
                                <div class="ticket-title">
                                    #<?php echo $ticket['id']; ?> - <?php echo htmlspecialchars($ticket['subject']); ?>
                                </div>
                                <div class="ticket-meta">
                                    <span><i class="fas fa-calendar"></i> <?php echo date('d/m/Y H:i', strtotime($ticket['created_at'])); ?></span>
                                    <span><i class="fas fa-tag"></i> <?php echo ucfirst($ticket['category']); ?></span>
                                    <?php if ($ticket['assigned_to_name']): ?>
                                    <span><i class="fas fa-user"></i> Asignado a: <?php echo htmlspecialchars($ticket['assigned_to_name']); ?></span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                                <span class="status-badge status-<?php echo $ticket['status']; ?>">
                                    <?php 
                                    $status_labels = [
                                        'open' => 'Abierto',
                                        'in_progress' => 'En Progreso',
                                        'resolved' => 'Resuelto',
                                        'closed' => 'Cerrado'
                                    ];
                                    echo $status_labels[$ticket['status']] ?? $ticket['status'];
                                    ?>
                                </span>
                                <span class="priority-badge priority-<?php echo $ticket['priority']; ?>">
                                    <?php 
                                    $priority_labels = [
                                        'low' => 'Baja',
                                        'medium' => 'Media',
                                        'high' => 'Alta',
                                        'urgent' => 'Urgente'
                                    ];
                                    echo $priority_labels[$ticket['priority']] ?? $ticket['priority'];
                                    ?>
                                </span>
                            </div>
                        </div>
                        
                        <div class="ticket-description">
                            <?php echo nl2br(htmlspecialchars(substr($ticket['description'], 0, 200))); ?>
                            <?php if (strlen($ticket['description']) > 200): ?>...<?php endif; ?>
                        </div>
                        
                        <div class="ticket-actions">
                            <a href="user_ticket_detail.php?id=<?php echo $ticket['id']; ?>" class="btn btn-primary">
                                <i class="fas fa-eye"></i>
                                Ver Detalles
                            </a>
                        </div>
                    </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </main>

    <script>
        // Auto-resize textarea
        const textarea = document.querySelector('.form-textarea');
        if (textarea) {
            textarea.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = this.scrollHeight + 'px';
            });
        }

        // Validación del formulario
        document.querySelector('form').addEventListener('submit', function(e) {
            const subject = document.querySelector('input[name="subject"]').value.trim();
            const description = document.querySelector('textarea[name="description"]').value.trim();
            
            if (subject.length < 5) {
                alert('El asunto debe tener al menos 5 caracteres');
                e.preventDefault();
                return;
            }
            
            if (description.length < 20) {
                alert('La descripción debe tener al menos 20 caracteres');
                e.preventDefault();
                return;
            }
        });

        // Animación de entrada para los tickets
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        });

        document.querySelectorAll('.ticket-item').forEach(item => {
            item.style.opacity = '0';
            item.style.transform = 'translateY(20px)';
            item.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(item);
        });
    </script>
</body>
</html>
