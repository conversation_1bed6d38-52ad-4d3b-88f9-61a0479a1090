<?php
session_start();
require_once 'config.php';

// Para demo, usar usuario por defecto si no está logueado
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['username'] = 'usuario';
}

$user_id = $_SESSION['user_id'];
$username = $_SESSION['username'] ?? 'usuario';
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>💬 Chat en Tiempo Real - RGS</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e293b;
            --dark-bg: #0f172a;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --accent-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --success-color: #10b981;
            --border-color: #334155;
            --gradient-bg: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            --border-radius: 12px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gradient-bg);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
        }

        .header {
            background: var(--secondary-color);
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 0 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--accent-color);
            text-decoration: none;
        }

        .nav-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .nav-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            background: transparent;
            color: var(--text-secondary);
            text-decoration: none;
            border-radius: var(--border-radius);
            transition: var(--transition);
            font-weight: 500;
        }

        .nav-btn:hover {
            background: rgba(255,255,255,0.1);
            color: var(--text-primary);
        }

        .nav-btn.back-btn {
            background: var(--primary-color);
            color: white;
        }

        .main-content {
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem 1.5rem;
        }

        .page-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .page-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .chat-container {
            background: var(--secondary-color);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            overflow: hidden;
            height: 600px;
            display: flex;
            flex-direction: column;
        }

        .chat-header {
            background: var(--dark-bg);
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .chat-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--warning-color);
        }

        .status-indicator.active {
            background: var(--success-color);
        }

        .chat-messages {
            flex: 1;
            padding: 1rem;
            overflow-y: auto;
            background: var(--dark-bg);
        }

        .message {
            margin-bottom: 1rem;
            padding: 0.75rem;
            border-radius: var(--border-radius);
            max-width: 80%;
            animation: fadeIn 0.3s ease;
        }

        .message.user {
            background: rgba(37, 99, 235, 0.2);
            margin-left: auto;
            text-align: right;
        }

        .message.admin {
            background: rgba(16, 185, 129, 0.2);
            margin-right: auto;
        }

        .message-sender {
            font-size: 0.8rem;
            color: var(--text-secondary);
            margin-bottom: 0.25rem;
        }

        .message-text {
            color: var(--text-primary);
        }

        .message-time {
            font-size: 0.7rem;
            color: var(--text-secondary);
            margin-top: 0.25rem;
        }

        .chat-input {
            padding: 1rem;
            border-top: 1px solid var(--border-color);
            background: var(--secondary-color);
        }

        .input-form {
            display: flex;
            gap: 0.5rem;
        }

        .message-input {
            flex: 1;
            padding: 0.75rem;
            background: var(--dark-bg);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            resize: none;
        }

        .send-btn {
            padding: 0.75rem 1.5rem;
            background: var(--accent-color);
            color: white;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: var(--transition);
        }

        .send-btn:hover {
            background: var(--success-color);
        }

        .send-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .start-chat-form {
            padding: 2rem;
            text-align: center;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: var(--text-primary);
            text-align: left;
        }

        .form-textarea {
            width: 100%;
            padding: 1rem;
            background: var(--dark-bg);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            font-family: inherit;
            font-size: 1rem;
            resize: vertical;
            min-height: 100px;
        }

        .btn {
            padding: 1rem 2rem;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: var(--transition);
        }

        .btn-primary {
            background: var(--accent-color);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            opacity: 0.9;
        }

        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: var(--text-secondary);
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .typing-indicator {
            display: none;
            padding: 0.5rem;
            color: var(--text-secondary);
            font-style: italic;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .loading {
            animation: pulse 1s infinite;
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-container">
            <a href="user_chat_realtime.php" class="logo">
                <i class="fas fa-comments"></i>
                <span>Chat en Tiempo Real</span>
            </a>
            
            <div class="nav-buttons">
                <a href="index2.php" class="nav-btn back-btn">
                    <i class="fas fa-arrow-left"></i>
                    <span>Volver a Servicios</span>
                </a>
            </div>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-comments" style="color: var(--accent-color);"></i>
                Chat en Tiempo Real
            </h1>
        </div>

        <div class="chat-container">
            <!-- Formulario para iniciar chat -->
            <div id="startChatForm" class="start-chat-form">
                <h2>Iniciar Nueva Conversación</h2>
                <div class="form-group">
                    <label class="form-label">¿En qué podemos ayudarte?</label>
                    <textarea id="initialMessage" class="form-textarea" placeholder="Describe tu problema o pregunta..."></textarea>
                </div>
                <button onclick="startChat()" class="btn btn-primary">
                    <i class="fas fa-comments"></i>
                    Iniciar Chat
                </button>
            </div>

            <!-- Chat activo -->
            <div id="chatActive" style="display: none; height: 100%; flex-direction: column;">
                <div class="chat-header">
                    <div class="chat-status">
                        <div id="statusIndicator" class="status-indicator"></div>
                        <span id="statusText">Conectando...</span>
                    </div>
                    <button onclick="endChat()" style="background: var(--error-color); color: white; border: none; padding: 0.5rem 1rem; border-radius: var(--border-radius); cursor: pointer;">
                        <i class="fas fa-times"></i> Finalizar
                    </button>
                </div>
                
                <div id="chatMessages" class="chat-messages">
                    <div class="empty-state">
                        <i class="fas fa-clock"></i>
                        <h3>Esperando respuesta del agente...</h3>
                    </div>
                </div>
                
                <div class="typing-indicator" id="typingIndicator">
                    El agente está escribiendo...
                </div>
                
                <div class="chat-input">
                    <div class="input-form">
                        <textarea id="messageInput" class="message-input" placeholder="Escribe tu mensaje..." rows="1"></textarea>
                        <button id="sendBtn" onclick="sendMessage()" class="send-btn">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        let currentSessionId = null;
        let messageInterval = null;
        let lastMessageCount = 0;

        // Verificar si ya hay una sesión activa al cargar
        window.onload = function() {
            checkExistingSession();
        };

        function checkExistingSession() {
            fetch('api_chat.php?action=get_user_session')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.session) {
                        currentSessionId = data.session.id;
                        showChatInterface();
                        updateStatus(data.session.status);
                        loadMessages();
                        startMessagePolling();
                    }
                })
                .catch(error => console.error('Error:', error));
        }

        function startChat() {
            const initialMessage = document.getElementById('initialMessage').value.trim();
            if (!initialMessage) {
                alert('Por favor describe tu problema');
                return;
            }

            const formData = new FormData();
            formData.append('initial_message', initialMessage);

            fetch('api_chat.php?action=start_chat', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    currentSessionId = data.session_id;
                    showChatInterface();
                    updateStatus('waiting');
                    loadMessages();
                    startMessagePolling();
                } else {
                    alert('Error al iniciar chat: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error de conexión');
            });
        }

        function showChatInterface() {
            document.getElementById('startChatForm').style.display = 'none';
            document.getElementById('chatActive').style.display = 'flex';
        }

        function updateStatus(status) {
            const indicator = document.getElementById('statusIndicator');
            const text = document.getElementById('statusText');
            
            switch(status) {
                case 'waiting':
                    indicator.className = 'status-indicator';
                    text.textContent = 'Esperando agente...';
                    break;
                case 'active':
                    indicator.className = 'status-indicator active';
                    text.textContent = 'Conectado con agente';
                    break;
                case 'ended':
                    indicator.className = 'status-indicator';
                    text.textContent = 'Chat finalizado';
                    break;
            }
        }

        function loadMessages() {
            if (!currentSessionId) return;

            fetch(`api_chat_improved.php?action=get_messages&session_id=${currentSessionId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayMessages(data.messages);

                        // Detectar nuevos mensajes
                        if (data.messages.length > lastMessageCount) {
                            lastMessageCount = data.messages.length;
                            scrollToBottom();
                        }

                        // Actualizar estado del admin si está disponible
                        if (data.status) {
                            updateAdminStatus(data.status.admin_online);
                        }
                    }
                })
                .catch(error => console.error('Error:', error));
        }

        function updateAdminStatus(isOnline) {
            const statusElement = document.querySelector('.chat-status');
            if (statusElement) {
                if (isOnline) {
                    statusElement.innerHTML = `
                        <div style="display: flex; align-items: center; gap: 0.5rem; color: var(--success-color);">
                            <div style="width: 8px; height: 8px; background: var(--success-color); border-radius: 50%; animation: pulse 2s infinite;"></div>
                            <span>Agente en línea</span>
                        </div>
                    `;
                } else {
                    statusElement.innerHTML = `
                        <div style="display: flex; align-items: center; gap: 0.5rem; color: var(--warning-color);">
                            <div style="width: 8px; height: 8px; background: var(--warning-color); border-radius: 50%;"></div>
                            <span>Esperando agente...</span>
                        </div>
                    `;
                }
            }
        }

        function displayMessages(messages) {
            const container = document.getElementById('chatMessages');
            
            if (messages.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-clock"></i>
                        <h3>Esperando respuesta del agente...</h3>
                    </div>
                `;
                return;
            }

            container.innerHTML = '';
            messages.forEach(msg => {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${msg.is_admin == 1 ? 'admin' : 'user'}`;
                
                const time = new Date(msg.sent_at).toLocaleTimeString('es-ES', {
                    hour: '2-digit',
                    minute: '2-digit'
                });
                
                messageDiv.innerHTML = `
                    <div class="message-sender">${msg.is_admin == 1 ? 'Agente de Soporte' : (msg.username || 'Tú')}</div>
                    <div class="message-text">${msg.message.replace(/\n/g, '<br>')}</div>
                    <div class="message-time">${time}</div>
                `;
                
                container.appendChild(messageDiv);
            });
        }

        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message || !currentSessionId) return;

            const formData = new FormData();
            formData.append('session_id', currentSessionId);
            formData.append('message', message);

            document.getElementById('sendBtn').disabled = true;

            fetch('api_chat.php?action=send_message', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    input.value = '';
                    loadMessages();
                } else {
                    alert('Error al enviar mensaje: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error de conexión');
            })
            .finally(() => {
                document.getElementById('sendBtn').disabled = false;
            });
        }

        function endChat() {
            if (!currentSessionId) return;

            if (!confirm('¿Estás seguro de que quieres finalizar el chat?')) return;

            const formData = new FormData();
            formData.append('session_id', currentSessionId);

            fetch('api_chat.php?action=end_chat', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    stopMessagePolling();
                    updateStatus('ended');

                    // Mostrar mensaje de finalización sin reload
                    const chatMessages = document.getElementById('chatMessages');
                    const endMessage = document.createElement('div');
                    endMessage.className = 'system-message';
                    endMessage.innerHTML = `
                        <div style="text-align: center; padding: 1rem; background: rgba(239, 68, 68, 0.1); border: 1px solid rgba(239, 68, 68, 0.3); border-radius: 8px; margin: 1rem 0;">
                            <i class="fas fa-times-circle" style="color: #ef4444; margin-right: 0.5rem;"></i>
                            <strong>Chat finalizado</strong>
                            <p style="margin: 0.5rem 0 0 0; font-size: 0.9rem; color: var(--text-secondary);">
                                La sesión de chat ha sido cerrada. Puedes iniciar una nueva conversación cuando lo necesites.
                            </p>
                        </div>
                    `;
                    chatMessages.appendChild(endMessage);
                    chatMessages.scrollTop = chatMessages.scrollHeight;

                    // Deshabilitar envío de mensajes
                    document.getElementById('messageInput').disabled = true;
                    document.querySelector('.send-btn').disabled = true;
                }
            })
            .catch(error => console.error('Error:', error));
        }

        function startMessagePolling() {
            // Polling más inteligente: más frecuente cuando hay actividad
            messageInterval = setInterval(() => {
                loadMessages();
                updateUserStatus(); // Mantener estado online
            }, 2000); // Cada 2 segundos para mejor responsividad
        }

        function updateUserStatus() {
            if (currentSessionId) {
                fetch('api_chat_improved.php?action=update_user_status', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `session_id=${currentSessionId}&online=true`
                }).catch(error => console.error('Error updating status:', error));
            }
        }

        function stopMessagePolling() {
            if (messageInterval) {
                clearInterval(messageInterval);
                messageInterval = null;
            }
        }

        function scrollToBottom() {
            const container = document.getElementById('chatMessages');
            container.scrollTop = container.scrollHeight;
        }

        // Enviar mensaje con Enter
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // Limpiar interval al cerrar la página
        window.addEventListener('beforeunload', function() {
            stopMessagePolling();
        });
    </script>
</body>
</html>
