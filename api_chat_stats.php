<?php
header('Content-Type: application/json');
session_start();
require_once 'config.php';

// Verificar si el usuario está logueado como admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit;
}

try {
    // Obtener estadísticas de chat en tiempo real
    $stmt = $pdo->query("
        SELECT 
            COUNT(*) as total,
            SUM(CASE WHEN status = 'waiting' THEN 1 ELSE 0 END) as waiting,
            SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active,
            SUM(CASE WHEN status = 'ended' THEN 1 ELSE 0 END) as ended,
            SUM(CASE WHEN DATE(started_at) = CURDATE() THEN 1 ELSE 0 END) as today,
            SUM(CASE WHEN started_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR) AND status = 'waiting' THEN 1 ELSE 0 END) as urgent_waiting
        FROM chat_sessions
    ");
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Obtener sesiones más recientes
    $stmt = $pdo->query("
        SELECT cs.id, cs.status, cs.started_at, u.username
        FROM chat_sessions cs
        LEFT JOIN users u ON cs.user_id = u.id
        WHERE cs.status IN ('waiting', 'active')
        ORDER BY cs.started_at DESC
        LIMIT 5
    ");
    $recent_sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $response = [
        'success' => true,
        'stats' => $stats,
        'recent_sessions' => $recent_sessions,
        'timestamp' => time()
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
