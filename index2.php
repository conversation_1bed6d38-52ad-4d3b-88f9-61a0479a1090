<?php
session_start();

// Verificar si el usuario está logueado
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

$user_id = $_SESSION['user_id'];
$username = $_SESSION['username'] ?? '';

// Configuración MySQL
$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    error_log('Error de base de datos: ' . $e->getMessage());
    die('Error de conexión a la base de datos.');
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🛠️ RGS TOOL - Servicios de Soporte</title>
    <meta name="description" content="Centro de servicios de soporte técnico para IPTV. Gestiona tickets, chat en vivo, aplicaciones y más.">
    
    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🛠️</text></svg>">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        :root {
            /* Paleta de colores profesional con verde como principal */
            --primary-color: #10b981;
            --primary-dark: #059669;
            --primary-light: #34d399;
            --secondary-color: #1f2937;
            --dark-bg: #111827;
            --darker-bg: #030712;
            --surface: #374151;
            --surface-light: #4b5563;
            --surface-elevated: #6b7280;

            /* Colores de texto profesionales */
            --text-primary: #f9fafb;
            --text-secondary: #d1d5db;
            --text-muted: #9ca3af;
            --text-accent: #34d399;

            /* Colores de acento para soporte */
            --accent-color: #10b981;
            --accent-secondary: #06b6d4;
            --warning-color: #d97706;
            --danger-color: #dc2626;
            --success-color: #16a34a;
            --support-color: #10b981;

            /* Gradientes corporativos 3D con verde */
            --gradient-primary: linear-gradient(135deg, #10b981 0%, #059669 50%, #047857 100%);
            --gradient-secondary: linear-gradient(135deg, #34d399 0%, #10b981 50%, #059669 100%);
            --gradient-surface: linear-gradient(135deg, #1f2937 0%, #374151 50%, #4b5563 100%);
            --gradient-hero: linear-gradient(135deg, #030712 0%, #111827 25%, #1f2937 50%, #374151 100%);
            --gradient-support: linear-gradient(135deg, #10b981 0%, #059669 50%, #047857 100%);
            --gradient-card: linear-gradient(145deg, #1f2937 0%, #374151 100%);
            --gradient-elevated: linear-gradient(145deg, #374151 0%, #4b5563 100%);

            /* Sombras 3D profesionales con verde */
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            --shadow-3d: 0 8px 16px rgba(0, 0, 0, 0.15), 0 4px 8px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);
            --shadow-elevated: 0 12px 24px rgba(0, 0, 0, 0.2), 0 6px 12px rgba(0, 0, 0, 0.15);
            --shadow-glow: 0 0 20px rgba(16, 185, 129, 0.4);
            --shadow-support: 0 0 20px rgba(16, 185, 129, 0.3);

            /* Espaciado consistente */
            --space-xs: 0.25rem;
            --space-sm: 0.5rem;
            --space-md: 1rem;
            --space-lg: 1.5rem;
            --space-xl: 2rem;
            --space-2xl: 3rem;

            /* Border radius moderno */
            --radius-sm: 6px;
            --radius-md: 8px;
            --radius-lg: 12px;
            --radius-xl: 16px;
            --radius-full: 9999px;

            /* Transiciones y efectos 3D */
            --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-bounce: 400ms cubic-bezier(0.68, -0.55, 0.265, 1.55);

            /* Transformaciones 3D */
            --transform-hover: translateY(-4px) scale(1.02);
            --transform-active: translateY(-2px) scale(0.98);
            --transform-3d: perspective(1000px) rotateX(2deg) rotateY(-2deg);
            --transform-card: perspective(1000px) rotateX(0deg) rotateY(0deg);

            /* Filtros profesionales */
            --blur-glass: blur(16px) saturate(180%);
            --blur-backdrop: blur(12px) saturate(150%);
            --brightness-hover: brightness(1.1);
            --contrast-elevated: contrast(1.05);

            /* Compatibilidad con variables anteriores */
            --border-color: #334155;
            --border-light: #475569;
            --border-radius: var(--radius-lg);
            --border-radius-lg: var(--radius-xl);
            --transition: var(--transition-normal);
            --shadow-light: var(--shadow-md);
            --shadow-medium: var(--shadow-lg);
            --shadow-heavy: var(--shadow-xl);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gradient-hero);
            background-attachment: fixed;
            color: var(--text-primary);
            line-height: 1.6;
            overflow-x: hidden;
            min-height: 100vh;
            font-feature-settings: "kern" 1, "liga" 1;
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            position: relative;
        }

        /* Efectos de fondo 3D para soporte */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 25% 25%, rgba(16, 185, 129, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(52, 211, 153, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 50% 50%, rgba(5, 150, 105, 0.06) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        /* Header Moderno con Glassmorphism */
        .header {
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(20px) saturate(180%);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 1000;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: var(--shadow-lg);
        }

        .header-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1.5rem;
            font-weight: 800;
            background: var(--gradient-support);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-decoration: none;
            letter-spacing: -0.025em;
        }

        .logo i {
            font-size: 2rem;
        }

        .nav-breadcrumb {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .nav-breadcrumb a {
            color: var(--primary-color);
            text-decoration: none;
            transition: var(--transition);
        }

        .nav-breadcrumb a:hover {
            color: var(--accent-color);
        }

        /* User Menu */
        .user-menu {
            display: flex;
            align-items: center;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: white;
            font-weight: 500;
            padding: 0.6rem 1rem;
            background: var(--gradient-primary);
            border-radius: var(--radius-xl);
            backdrop-filter: var(--blur-backdrop);
            border: 1px solid var(--primary-color);
            box-shadow: var(--shadow-sm), var(--shadow-glow);
            white-space: nowrap;
        }

        .nav-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 0.6rem 1rem;
            background: var(--gradient-card);
            border: 1px solid rgba(255, 255, 255, 0.12);
            border-radius: var(--radius-xl);
            backdrop-filter: var(--blur-backdrop);
            color: var(--text-primary);
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition-normal);
            box-shadow: var(--shadow-sm);
            transform: var(--transform-card);
            position: relative;
            overflow: hidden;
            white-space: nowrap;
            min-width: fit-content;
        }

        .nav-btn:hover {
            background: var(--gradient-elevated);
            border-color: var(--primary-color);
            transform: var(--transform-hover) var(--transform-3d);
            box-shadow: var(--shadow-md), var(--shadow-glow);
            filter: var(--brightness-hover);
        }

        .nav-btn.logout-btn {
            background: rgba(239, 68, 68, 0.15);
            border-color: rgba(239, 68, 68, 0.3);
            color: var(--danger-color);
        }

        .nav-btn.logout-btn:hover {
            background: rgba(239, 68, 68, 0.25);
            border-color: var(--danger-color);
            color: white;
        }

        .nav-btn.admin-btn {
            background: rgba(245, 158, 11, 0.15);
            border-color: rgba(245, 158, 11, 0.3);
            color: var(--warning-color);
        }

        .nav-btn.admin-btn:hover {
            background: rgba(245, 158, 11, 0.25);
            border-color: var(--warning-color);
            color: white;
        }

        /* Hero Section Mejorada */
        .hero {
            background: var(--gradient-hero);
            padding: 1.5rem 0;
            border-bottom: 1px solid var(--border-color);
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 30% 20%, rgba(233, 30, 99, 0.1) 0%, transparent 50%),
                        radial-gradient(circle at 70% 80%, rgba(99, 102, 241, 0.1) 0%, transparent 50%);
            pointer-events: none;
        }

        .hero-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 1.5rem;
        }

        .hero-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .hero-header h1 {
            font-size: clamp(2rem, 4vw, 3rem);
            font-weight: 800;
            margin-bottom: 1rem;
            background: var(--gradient-support);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            letter-spacing: -0.025em;
        }

        .hero-header .subtitle {
            font-size: 1.2rem;
            color: var(--text-secondary);
            max-width: 600px;
            margin: 0 auto 2rem;
        }

        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem 1.5rem;
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.3);
            border-radius: var(--border-radius);
            color: var(--accent-color);
        }

        .status-dot {
            width: 8px;
            height: 8px;
            background: var(--accent-color);
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        /* Tarjetas de Servicios de Soporte */
        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }

        .service-card {
            background: var(--gradient-card);
            border: 1px solid rgba(255, 255, 255, 0.12);
            border-radius: var(--radius-xl);
            backdrop-filter: var(--blur-backdrop);
            padding: 1.5rem;
            transition: var(--transition-normal);
            cursor: pointer;
            position: relative;
            overflow: hidden;
            box-shadow: var(--shadow-3d);
            transform: var(--transform-card);
        }

        .service-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--gradient-primary);
            transform: scaleX(0);
            transition: var(--transition-normal);
            border-radius: var(--radius-xl) var(--radius-xl) 0 0;
        }

        .service-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%, rgba(255, 255, 255, 0.05) 100%);
            opacity: 0;
            transition: var(--transition-normal);
            pointer-events: none;
        }

        .service-card:hover {
            background: var(--gradient-elevated);
            border-color: var(--primary-color);
            transform: var(--transform-hover) var(--transform-3d);
            box-shadow: var(--shadow-2xl), var(--shadow-glow);
            filter: var(--brightness-hover);
        }

        .service-card:hover::before {
            transform: scaleX(1);
        }

        .service-card:hover::after {
            opacity: 1;
        }

        .service-icon {
            width: 56px;
            height: 56px;
            background: var(--gradient-primary);
            border-radius: var(--radius-xl);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
            color: white;
            font-size: 1.5rem;
            box-shadow: var(--shadow-3d), var(--shadow-glow);
            transition: var(--transition-normal);
            position: relative;
            overflow: hidden;
        }

        .service-icon::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
            opacity: 0;
            transition: var(--transition-fast);
        }

        .service-card:hover .service-icon {
            transform: scale(1.1) rotateY(15deg);
            box-shadow: var(--shadow-elevated), var(--shadow-glow);
        }

        .service-card:hover .service-icon::before {
            opacity: 1;
        }

        .service-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }

        .service-description {
            color: var(--text-secondary);
            font-size: 0.9rem;
            line-height: 1.5;
            margin-bottom: 1rem;
        }

        .service-action {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--primary-color);
            font-weight: 600;
            font-size: 0.9rem;
            text-decoration: none;
            transition: var(--transition);
        }

        .service-action:hover {
            color: var(--accent-color);
            transform: translateX(4px);
        }

        /* Responsive Mejorado */
        @media (max-width: 768px) {
            .header-container {
                flex-direction: column;
                gap: var(--space-md);
                padding: var(--space-md);
            }

            .services-grid {
                grid-template-columns: 1fr;
                gap: var(--space-lg);
            }

            .service-card {
                padding: 1rem;
            }

            .service-icon {
                width: 44px;
                height: 44px;
                font-size: 1.4rem;
            }

            .nav-btn span {
                display: none;
            }

            .user-info span {
                display: none;
            }

            /* FAB responsive */
            .fab-container {
                bottom: 1rem;
                right: 1rem;
            }

            .fab {
                width: 48px;
                height: 48px;
                font-size: 1.2rem;
            }

            .back-to-top {
                bottom: 1rem;
                left: 1rem;
                width: 40px;
                height: 40px;
            }

            /* Toast responsive */
            .toast {
                top: 10px;
                right: 10px;
                left: 10px;
                max-width: none;
                transform: translateY(-100%);
            }

            .toast.show {
                transform: translateY(0);
            }
        }

        @media (max-width: 480px) {
            .hero-container {
                padding: 0 1rem;
            }

            .services-grid {
                gap: 1rem;
                margin-top: 1.5rem;
            }

            .service-card {
                padding: 1rem;
            }

            .service-icon {
                width: 40px;
                height: 40px;
                font-size: 1.3rem;
                margin-bottom: 0.75rem;
            }

            .service-title {
                font-size: 1.1rem;
            }

            .service-description {
                font-size: 0.85rem;
            }
        }

        /* Animaciones */
        .fade-in {
            animation: fadeInUp 0.6s ease forwards;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .stagger-animation .service-card {
            opacity: 0;
            animation: fadeInUp 0.6s ease forwards;
        }

        .stagger-animation .service-card:nth-child(1) { animation-delay: 0.1s; }
        .stagger-animation .service-card:nth-child(2) { animation-delay: 0.2s; }
        .stagger-animation .service-card:nth-child(3) { animation-delay: 0.3s; }
        .stagger-animation .service-card:nth-child(4) { animation-delay: 0.4s; }
        .stagger-animation .service-card:nth-child(5) { animation-delay: 0.5s; }
        .stagger-animation .service-card:nth-child(6) { animation-delay: 0.6s; }

        /* ===== COMPONENTES MODERNOS ===== */

        /* Floating Action Button con Menú */
        .fab-container {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .fab {
            width: 56px;
            height: 56px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            box-shadow: var(--shadow-lg);
            transition: var(--transition-normal);
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--gradient-support);
            color: white;
            font-size: 1.5rem;
            position: relative;
        }

        .fab:hover {
            transform: scale(1.1);
            box-shadow: var(--shadow-xl);
        }

        .fab.active {
            transform: rotate(45deg);
        }

        .fab-menu {
            position: absolute;
            bottom: 70px;
            right: 0;
            display: flex;
            flex-direction: column;
            gap: var(--space-sm);
            opacity: 0;
            visibility: hidden;
            transform: translateY(20px);
            transition: var(--transition-normal);
        }

        .fab-container.open .fab-menu {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .mini-fab {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            box-shadow: var(--shadow-md);
            transition: var(--transition-normal);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            text-decoration: none;
            position: relative;
        }

        .mini-fab:hover {
            transform: scale(1.1);
            box-shadow: var(--shadow-lg);
        }

        .mini-fab.content {
            background: var(--gradient-primary);
        }

        .mini-fab.tickets {
            background: var(--gradient-secondary);
        }

        .mini-fab.chat {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        }

        /* Tooltips para mini FABs */
        .mini-fab::before {
            content: attr(data-tooltip);
            position: absolute;
            right: 60px;
            top: 50%;
            transform: translateY(-50%);
            background: var(--secondary-color);
            color: var(--text-primary);
            padding: var(--space-xs) var(--space-sm);
            border-radius: var(--radius-md);
            font-size: 0.8rem;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: var(--transition-fast);
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .mini-fab:hover::before {
            opacity: 1;
            visibility: visible;
        }

        /* Back to Top Button */
        .back-to-top {
            position: fixed;
            bottom: 2rem;
            left: 2rem;
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: var(--gradient-support);
            border: none;
            color: white;
            cursor: pointer;
            opacity: 0;
            transform: translateY(100px);
            transition: var(--transition-normal);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .back-to-top.show {
            opacity: 1;
            transform: translateY(0);
        }

        .back-to-top:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
        }

        /* Toast Notifications */
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--gradient-surface);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: var(--radius-lg);
            padding: 1rem;
            box-shadow: var(--shadow-xl);
            z-index: 10000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            backdrop-filter: blur(10px);
            max-width: 400px;
        }

        .toast.show {
            transform: translateX(0);
        }

        .toast-content {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--text-primary);
        }

        .toast-success { border-left: 3px solid var(--success-color); }
        .toast-error { border-left: 3px solid var(--danger-color); }
        .toast-warning { border-left: 3px solid var(--warning-color); }
        .toast-info { border-left: 3px solid var(--accent-secondary); }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-container">
            <a href="index.php" class="logo">
                <i class="fas fa-headset"></i>
                <span>RGS TOOL</span>
            </a>

            <div class="nav-breadcrumb">
                <a href="index.php">
                    <i class="fas fa-home"></i>
                    Inicio
                </a>
                <i class="fas fa-chevron-right"></i>
                <span>Servicios de Soporte</span>
            </div>

            <div class="user-menu">
                <div class="user-info">
                    <i class="fas fa-user"></i>
                    <span>Hola, <?php echo htmlspecialchars($username); ?></span>
                </div>

                <a href="index.php" class="nav-btn">
                    <i class="fas fa-film"></i>
                    <span>Contenido</span>
                </a>

                <a href="pedidos.php" class="nav-btn">
                    <i class="fas fa-shopping-cart"></i>
                    <span>Mis Pedidos</span>
                </a>

                <?php
                // Verificar si el usuario es admin
                $stmt = $pdo->prepare("SELECT username FROM users WHERE id = ? AND username IN ('admin', 'Infest84')");
                $stmt->execute([$user_id]);
                $is_admin = $stmt->fetch();
                if ($is_admin):
                ?>
                <a href="admin_login.php" class="nav-btn admin-btn">
                    <i class="fas fa-cog"></i>
                    <span>Admin</span>
                </a>
                <?php endif; ?>

                <a href="logout.php" class="nav-btn logout-btn">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Salir</span>
                </a>
            </div>
        </div>
    </header>

    <!-- Hero Section - Servicios de Soporte -->
    <section class="hero">
        <div class="hero-container">
            <div class="hero-header">
                <h1 class="fade-in">
                    <i class="fas fa-headset" style="color: var(--primary-color); margin-right: 0.5rem;"></i>
                    Centro de Servicios de Soporte
                </h1>
                <p class="subtitle fade-in">
                    Accede a todos nuestros servicios de soporte técnico especializado para IPTV. 
                    Gestiona tickets, obtén ayuda en tiempo real y accede a recursos técnicos.
                </p>
                <div class="status-indicator">
                    <div class="status-dot"></div>
                    <div>
                        <strong>Todos los Servicios Operativos</strong>
                        <span style="margin-left: 0.5rem; font-size: 0.9rem;">24/7 Disponible</span>
                    </div>
                </div>
            </div>

            <!-- Tarjetas de Servicios de Soporte -->
            <div class="services-grid stagger-animation">
                <div class="service-card" onclick="window.location.href='user_tickets.php'">
                    <div class="service-icon">
                        <i class="fas fa-ticket-alt"></i>
                    </div>
                    <h3 class="service-title">Gestión de Tickets</h3>
                    <p class="service-description">
                        Crea y gestiona tickets de soporte técnico. Seguimiento en tiempo real del estado de tus solicitudes
                        con notificaciones automáticas y historial completo.
                    </p>
                    <a href="user_tickets.php" class="service-action">
                        <span>Acceder a Tickets</span>
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>

                <div class="service-card" onclick="window.location.href='user_chat_realtime.php'">
                    <div class="service-icon">
                        <i class="fas fa-comments"></i>
                    </div>
                    <h3 class="service-title">Chat en Tiempo Real</h3>
                    <p class="service-description">
                        Soporte instantáneo con nuestros agentes técnicos especializados en IPTV.
                        Resolución rápida de problemas y asistencia en tiempo real.
                    </p>
                    <a href="user_chat_realtime.php" class="service-action">
                        <span>Iniciar Chat</span>
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>

                <div class="service-card" onclick="window.location.href='user_apps.php'">
                    <div class="service-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3 class="service-title">Aplicaciones IPTV</h3>
                    <p class="service-description">
                        Descarga aplicaciones oficiales para Android, iOS, Smart TV, Windows, macOS y otros
                        dispositivos compatibles con tu servicio IPTV.
                    </p>
                    <a href="user_apps.php" class="service-action">
                        <span>Ver Aplicaciones</span>
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>

                <div class="service-card" onclick="window.location.href='user_help.php'">
                    <div class="service-icon">
                        <i class="fas fa-question-circle"></i>
                    </div>
                    <h3 class="service-title">Centro de Ayuda</h3>
                    <p class="service-description">
                        Guías de configuración paso a paso, preguntas frecuentes, tutoriales para el primer uso
                        y documentación técnica completa.
                    </p>
                    <a href="user_help.php" class="service-action">
                        <span>Ver Guías</span>
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>

                <div class="service-card" onclick="window.location.href='user_channels.php'">
                    <div class="service-icon">
                        <i class="fas fa-list-ul"></i>
                    </div>
                    <h3 class="service-title">Solicitar Canales</h3>
                    <p class="service-description">
                        Solicita canales específicos de TV que no estén disponibles en tu lista actual.
                        Proceso rápido de evaluación y agregado de contenido.
                    </p>
                    <a href="user_channels.php" class="service-action">
                        <span>Solicitar Canal</span>
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>

                <div class="service-card" onclick="window.location.href='user_activation.php'">
                    <div class="service-icon">
                        <i class="fas fa-cog"></i>
                    </div>
                    <h3 class="service-title">Activación de Listas</h3>
                    <p class="service-description">
                        Activa y configura tus listas M3U y EPG para diferentes dispositivos y aplicaciones.
                        Generación automática de URLs personalizadas.
                    </p>
                    <a href="user_activation.php" class="service-action">
                        <span>Activar Listas</span>
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>
    </section>



    <!-- Floating Action Button con Menú -->
    <div class="fab-container" id="fabContainer">
        <button class="fab" onclick="toggleFabMenu()" title="Acciones rápidas">
            <i class="fas fa-plus"></i>
        </button>
        <div class="fab-menu">
            <a href="index.php" class="mini-fab content" data-tooltip="Contenido y Películas">
                <i class="fas fa-film"></i>
            </a>
            <a href="user_tickets.php" class="mini-fab tickets" data-tooltip="Gestión de Tickets">
                <i class="fas fa-ticket-alt"></i>
            </a>
            <a href="user_chat_realtime.php" class="mini-fab chat" data-tooltip="Chat en Tiempo Real">
                <i class="fas fa-comments"></i>
            </a>
        </div>
    </div>

    <!-- Back to Top Button -->
    <button class="back-to-top" id="backToTop" onclick="scrollToTop()">
        <i class="fas fa-chevron-up"></i>
    </button>

    <script>
        // ===== FUNCIONALIDADES MODERNAS =====

        // Inicializar funcionalidades modernas
        document.addEventListener('DOMContentLoaded', function() {
            initModernFeatures();
            initServiceCardAnimations();
        });

        function initModernFeatures() {
            initScrollEffects();
            initKeyboardNavigation();
            initAnimationsOnScroll();
        }

        // ===== EFECTOS DE SCROLL =====
        function initScrollEffects() {
            const backToTop = document.getElementById('backToTop');

            window.addEventListener('scroll', function() {
                if (window.pageYOffset > 300) {
                    backToTop.classList.add('show');
                } else {
                    backToTop.classList.remove('show');
                }
            });
        }

        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        // ===== NAVEGACIÓN POR TECLADO =====
        function initKeyboardNavigation() {
            document.addEventListener('keydown', function(e) {
                // Esc para cerrar modales
                if (e.key === 'Escape') {
                    closeAllModals();
                }

                // Ctrl/Cmd + K para ir a contenido
                if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                    e.preventDefault();
                    window.location.href = 'index.php';
                }
            });
        }

        function closeAllModals() {
            const fabContainer = document.getElementById('fabContainer');
            if (fabContainer) {
                fabContainer.classList.remove('open');
                fabContainer.querySelector('.fab').classList.remove('active');
            }
        }

        // ===== ANIMACIONES AL HACER SCROLL =====
        function initAnimationsOnScroll() {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate-in');
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            });

            // Agregar observador a elementos
            document.querySelectorAll('.service-card').forEach(el => {
                el.classList.add('animate-on-scroll');
                observer.observe(el);
            });
        }

        // ===== FLOATING ACTION BUTTON =====
        function toggleFabMenu() {
            const fabContainer = document.getElementById('fabContainer');
            const fab = fabContainer.querySelector('.fab');

            fabContainer.classList.toggle('open');
            fab.classList.toggle('active');
        }

        // Cerrar FAB menu al hacer click fuera
        document.addEventListener('click', function(event) {
            const fabContainer = document.getElementById('fabContainer');
            if (fabContainer && !fabContainer.contains(event.target)) {
                fabContainer.classList.remove('open');
                fabContainer.querySelector('.fab').classList.remove('active');
            }
        });

        // ===== ANIMACIONES DE TARJETAS DE SERVICIO =====
        function initServiceCardAnimations() {
            const cards = document.querySelectorAll('.service-card');

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, {
                threshold: 0.1
            });

            cards.forEach(card => {
                observer.observe(card);
            });
        }

        // ===== SISTEMA DE TOAST =====
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.innerHTML = `
                <div class="toast-content">
                    <i class="fas fa-${getToastIcon(type)}"></i>
                    <span>${message}</span>
                </div>
            `;

            document.body.appendChild(toast);

            // Mostrar toast
            setTimeout(() => toast.classList.add('show'), 100);

            // Ocultar toast
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => toast.remove(), 300);
            }, 3000);
        }

        function getToastIcon(type) {
            const icons = {
                'success': 'check-circle',
                'error': 'exclamation-circle',
                'warning': 'exclamation-triangle',
                'info': 'info-circle'
            };
            return icons[type] || 'info-circle';
        }

        // ===== MEJORAS DE INTERACCIÓN =====

        // Agregar efectos hover mejorados a las tarjetas
        document.querySelectorAll('.service-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-12px) scale(1.02)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });

        // Feedback visual al hacer click en tarjetas
        document.querySelectorAll('.service-card').forEach(card => {
            card.addEventListener('click', function() {
                this.style.transform = 'translateY(-8px) scale(0.98)';
                setTimeout(() => {
                    this.style.transform = 'translateY(-12px) scale(1.02)';
                }, 150);
            });
        });
    </script>
</body>
</html>
