<?php
// Analizador M3U con modal de progreso
error_reporting(E_ALL);
ini_set('display_errors', 1);
set_time_limit(300);

session_start();

if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Error de conexión: " . $e->getMessage());
}

// Obtener listas
$stmt = $pdo->query("SELECT id, name, is_active, last_scan, total_items FROM m3u_lists ORDER BY name");
$all_lists = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Analizador M3U - RGS TOOL</title>
    <style>
        :root {
            /* Paleta de colores profesional con verde como principal */
            --primary-color: #10b981;
            --primary-dark: #059669;
            --primary-light: #34d399;
            --secondary-color: #1f2937;
            --dark-bg: #111827;
            --darker-bg: #030712;
            --surface: #374151;
            --surface-light: #4b5563;
            --surface-elevated: #6b7280;

            /* Colores de texto profesionales */
            --text-primary: #f9fafb;
            --text-secondary: #d1d5db;
            --text-muted: #9ca3af;
            --text-accent: #34d399;

            /* Colores de estado para M3U */
            --accent-color: #10b981;
            --accent-dark: #059669;
            --warning-color: #d97706;
            --error-color: #dc2626;
            --success-color: #16a34a;
            --info-color: #2563eb;
            --m3u-color: #10b981;

            /* Gradientes corporativos 3D con verde */
            --gradient-primary: linear-gradient(135deg, #10b981 0%, #059669 50%, #047857 100%);
            --gradient-secondary: linear-gradient(135deg, #34d399 0%, #10b981 50%, #059669 100%);
            --gradient-m3u: linear-gradient(135deg, #10b981 0%, #059669 50%, #065f46 100%);
            --gradient-surface: linear-gradient(135deg, #1f2937 0%, #374151 50%, #4b5563 100%);
            --gradient-hero: linear-gradient(135deg, #030712 0%, #111827 25%, #1f2937 50%, #374151 100%);
            --gradient-card: linear-gradient(145deg, #1f2937 0%, #374151 100%);
            --gradient-elevated: linear-gradient(145deg, #374151 0%, #4b5563 100%);

            /* Sombras 3D profesionales */
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            --shadow-3d: 0 8px 16px rgba(0, 0, 0, 0.15), 0 4px 8px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);
            --shadow-elevated: 0 12px 24px rgba(0, 0, 0, 0.2), 0 6px 12px rgba(0, 0, 0, 0.15);
            --shadow-glow: 0 0 20px rgba(16, 185, 129, 0.4);
            --shadow-m3u: 0 0 20px rgba(16, 185, 129, 0.3);

            /* Espaciado consistente */
            --space-xs: 0.25rem;
            --space-sm: 0.5rem;
            --space-md: 1rem;
            --space-lg: 1.5rem;
            --space-xl: 2rem;
            --space-2xl: 3rem;

            /* Border radius moderno */
            --radius-sm: 6px;
            --radius-md: 8px;
            --radius-lg: 12px;
            --radius-xl: 16px;
            --radius-full: 9999px;

            /* Transiciones y efectos 3D */
            --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-bounce: 400ms cubic-bezier(0.68, -0.55, 0.265, 1.55);

            /* Transformaciones 3D */
            --transform-hover: translateY(-4px) scale(1.02);
            --transform-active: translateY(-2px) scale(0.98);
            --transform-3d: perspective(1000px) rotateX(2deg) rotateY(-2deg);
            --transform-card: perspective(1000px) rotateX(0deg) rotateY(0deg);

            /* Filtros profesionales */
            --blur-glass: blur(16px) saturate(180%);
            --blur-backdrop: blur(12px) saturate(150%);
            --brightness-hover: brightness(1.1);
            --contrast-elevated: contrast(1.05);

            /* Compatibilidad */
            --border-color: #374151;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gradient-hero);
            background-attachment: fixed;
            color: var(--text-primary);
            line-height: 1.6;
            font-feature-settings: "kern" 1, "liga" 1;
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            position: relative;
            overflow-x: hidden;
        }

        /* Efectos de fondo 3D para M3U Analyzer */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 25% 25%, rgba(16, 185, 129, 0.12) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(52, 211, 153, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 50% 50%, rgba(5, 150, 105, 0.08) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: var(--space-xl);
        }

        .back-link {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--text-primary);
            text-decoration: none;
            margin-bottom: var(--space-xl);
            font-weight: 500;
            padding: 0.5rem 1rem;
            background: var(--gradient-surface);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: var(--radius-lg);
            backdrop-filter: var(--blur-backdrop);
            transition: var(--transition-normal);
            box-shadow: var(--shadow-sm);
        }

        .back-link:hover {
            background: var(--gradient-elevated);
            border-color: var(--primary-color);
            transform: var(--transform-hover);
            box-shadow: var(--shadow-md), var(--shadow-glow);
            color: var(--text-accent);
        }

        .back-link i {
            transition: var(--transition-normal);
        }

        .back-link:hover i {
            transform: translateX(-3px);
        }

        .header {
            text-align: center;
            margin-bottom: var(--space-2xl);
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 200px;
            height: 200px;
            background: radial-gradient(circle, rgba(16, 185, 129, 0.1) 0%, transparent 70%);
            border-radius: 50%;
            z-index: -1;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 800;
            letter-spacing: -0.025em;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            position: relative;
        }

        .header h1 i {
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: analyzerPulse 3s ease-in-out infinite;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
        }

        @keyframes analyzerPulse {
            0%, 100% { transform: scale(1) rotateZ(0deg); }
            50% { transform: scale(1.1) rotateZ(-5deg); }
        }

        .header p {
            color: var(--text-secondary);
            font-size: 1.1rem;
            margin-bottom: var(--space-lg);
        }

        .section {
            background: var(--gradient-card);
            border: 1px solid rgba(255, 255, 255, 0.12);
            border-radius: var(--radius-xl);
            backdrop-filter: var(--blur-backdrop);
            padding: var(--space-xl);
            margin: var(--space-xl) 0;
            box-shadow: var(--shadow-3d);
            position: relative;
            overflow: hidden;
            transition: var(--transition-normal);
        }

        .section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--gradient-primary);
            opacity: 0.8;
        }

        .section:hover {
            border-color: var(--primary-color);
            box-shadow: var(--shadow-elevated), var(--shadow-glow);
        }

        .form-group {
            margin: 1.5rem 0;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: var(--text-secondary);
            font-weight: 600;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .form-input {
            width: 100%;
            padding: 1rem;
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: var(--radius-lg);
            background: var(--gradient-surface);
            color: var(--text-primary);
            font-size: 1rem;
            backdrop-filter: var(--blur-backdrop);
            transition: var(--transition-normal);
            box-shadow: var(--shadow-sm);
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: var(--shadow-md), var(--shadow-glow);
            transform: translateY(-2px);
            background: var(--gradient-elevated);
        }

        .form-input:hover {
            border-color: rgba(255, 255, 255, 0.2);
            transform: translateY(-1px);
        }

        /* Corregir options de los selects */
        .form-input option {
            background: var(--surface);
            color: var(--text-primary);
            padding: 0.5rem;
            border: none;
        }

        .form-input option:hover,
        .form-input option:focus,
        .form-input option:checked {
            background: var(--primary-color);
            color: white;
        }

        /* Para navegadores webkit */
        .form-input option:checked {
            background: var(--primary-color) !important;
            color: white !important;
        }

        /* Estilos específicos para select */
        select.form-input {
            cursor: pointer;
            appearance: none;
            -webkit-appearance: none;
            -moz-appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2310b981' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 0.7rem center;
            background-size: 1rem;
            padding-right: 2.5rem;
        }

        /* Estilos adicionales para mejor compatibilidad */
        select.form-input::-ms-expand {
            display: none;
        }

        /* Forzar estilos en dropdown */
        select.form-input optgroup {
            background: var(--surface);
            color: var(--text-primary);
        }

        select.form-input optgroup option {
            background: var(--surface);
            color: var(--text-primary);
        }

        .btn {
            padding: 1rem 2rem;
            border: 1px solid;
            border-radius: var(--radius-lg);
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: var(--transition-normal);
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            backdrop-filter: var(--blur-backdrop);
            box-shadow: var(--shadow-sm);
            position: relative;
            overflow: hidden;
            text-decoration: none;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
            opacity: 0;
            transition: var(--transition-fast);
        }

        .btn:hover::before {
            opacity: 1;
        }

        .btn-primary {
            background: var(--gradient-primary);
            border-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: var(--gradient-primary);
            transform: var(--transform-hover);
            box-shadow: var(--shadow-md), var(--shadow-glow);
            filter: var(--brightness-hover);
        }

        .btn:active {
            transform: var(--transform-active);
        }

        .btn-primary:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background: var(--secondary-color);
            margin: 10% auto;
            padding: 2rem;
            border-radius: 12px;
            width: 90%;
            max-width: 500px;
            border: 1px solid var(--border-color);
            position: relative;
            animation: modalSlideIn 0.3s ease;
        }

        @keyframes modalSlideIn {
            from {
                transform: translateY(-50px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .modal-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .modal-header h2 {
            color: var(--accent-color);
            margin-bottom: 0.5rem;
        }

        .progress-container {
            margin: var(--space-xl) 0;
        }

        .progress-bar {
            width: 100%;
            height: 24px;
            background: var(--gradient-surface);
            border-radius: var(--radius-full);
            overflow: hidden;
            position: relative;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: var(--shadow-sm);
        }

        .progress-fill {
            height: 100%;
            background: var(--gradient-primary);
            width: 0%;
            transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            border-radius: var(--radius-full);
            box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            background: linear-gradient(
                90deg,
                transparent,
                rgba(255, 255, 255, 0.2),
                transparent
            );
            animation: progressShine 2s infinite;
        }

        @keyframes progressShine {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .progress-text {
            text-align: center;
            margin-top: var(--space-md);
            color: var(--text-secondary);
            font-weight: 500;
        }

        .progress-percentage {
            font-size: 1.5rem;
            font-weight: 800;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .log-container {
            background: var(--primary-color);
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            max-height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.8rem;
            border: 1px solid var(--border-color);
        }

        .log-entry {
            margin: 0.2rem 0;
            color: var(--text-secondary);
        }

        .log-entry.success {
            color: var(--success-color);
        }

        .log-entry.error {
            color: var(--error-color);
        }

        .log-entry.info {
            color: var(--accent-color);
        }

        .results-container {
            display: none;
            margin-top: 2rem;
        }

        .results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .result-item {
            text-align: center;
            background: var(--gradient-card);
            border: 1px solid rgba(255, 255, 255, 0.12);
            border-radius: var(--radius-xl);
            backdrop-filter: var(--blur-backdrop);
            padding: var(--space-lg);
            box-shadow: var(--shadow-3d);
            transform: var(--transform-card);
            position: relative;
            overflow: hidden;
            transition: var(--transition-normal);
        }

        .result-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--gradient-primary);
            transform: scaleX(0);
            transition: var(--transition-normal);
            border-radius: var(--radius-xl) var(--radius-xl) 0 0;
        }

        .result-item:hover {
            background: var(--gradient-elevated);
            border-color: var(--primary-color);
            transform: var(--transform-hover) var(--transform-3d);
            box-shadow: var(--shadow-2xl), var(--shadow-glow);
            filter: var(--brightness-hover);
        }

        .result-item:hover::before {
            transform: scaleX(1);
        }

        .result-number {
            font-size: 1.8rem;
            font-weight: 800;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            transition: var(--transition-normal);
        }

        .result-item:hover .result-number {
            transform: scale(1.1);
        }

        .result-label {
            color: var(--text-secondary);
            font-size: 0.8rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            transition: var(--transition-normal);
        }

        .result-item:hover .result-label {
            color: var(--text-primary);
        }

        .close-btn {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: var(--gradient-surface);
            border: 1px solid rgba(255, 255, 255, 0.15);
            color: var(--text-secondary);
            font-size: 1.2rem;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 50%;
            transition: var(--transition-normal);
            backdrop-filter: var(--blur-backdrop);
            box-shadow: var(--shadow-sm);
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .close-btn:hover {
            background: var(--gradient-elevated);
            border-color: var(--primary-color);
            color: var(--text-primary);
            transform: var(--transform-hover);
            box-shadow: var(--shadow-md), var(--shadow-glow);
        }

        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid var(--border-color);
            border-radius: 50%;
            border-top-color: var(--accent-color);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        @keyframes slideInUp {
            from {
                transform: translateY(30px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .result-item:hover {
            transform: translateY(-2px);
            transition: transform 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="m3u_manager.php" class="back-link">
            <i class="fas fa-arrow-left"></i>
            Volver al Gestor M3U
        </a>
        
        <div class="header">
            <h1><i class="fas fa-search"></i> Analizador M3U</h1>
            <p>Analiza y procesa listas M3U con progreso en tiempo real</p>
        </div>

        <div class="section">
            <h2><i class="fas fa-cog"></i> Análisis de Lista M3U</h2>
            <form id="analyzeForm">
                <div class="form-group">
                    <label for="list_id">Seleccionar Lista M3U</label>
                    <select name="list_id" id="list_id" class="form-input" required>
                        <option value="">Seleccionar lista...</option>
                        <?php foreach ($all_lists as $list): ?>
                        <option value="<?php echo $list['id']; ?>">
                            <?php echo htmlspecialchars($list['name']); ?>
                            (<?php echo $list['total_items']; ?> elementos)
                            <?php if (!$list['is_active']): ?> - Inactiva<?php endif; ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <button type="submit" class="btn btn-primary" id="analyzeBtn">
                    <i class="fas fa-search"></i>
                    Iniciar Análisis
                </button>
            </form>
        </div>
    </div>

    <!-- Modal de Progreso -->
    <div id="progressModal" class="modal">
        <div class="modal-content">
            <button class="close-btn" onclick="closeModal()">&times;</button>
            
            <div class="modal-header">
                <h2><i class="fas fa-cog"></i> Analizando Lista M3U</h2>
                <p>Procesando contenido, por favor espera...</p>
            </div>

            <div class="progress-container">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="progress-text">
                    <div class="progress-percentage" id="progressPercentage">0%</div>
                    <div id="progressStatus">Iniciando análisis...</div>
                </div>
            </div>

            <div class="log-container" id="logContainer">
                <div class="log-entry info">Preparando análisis...</div>
            </div>

            <div class="results-container" id="resultsContainer">
                <h3><i class="fas fa-chart-bar"></i> Resultados del Análisis</h3>
                <div class="results-grid" id="resultsGrid">
                    <!-- Los resultados se llenarán dinámicamente -->
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
    <script>
        let currentProgress = 0;
        let analysisComplete = false;

        document.getElementById('analyzeForm').addEventListener('submit', function(e) {
            e.preventDefault();
            startAnalysis();
        });

        function startAnalysis() {
            const listId = document.getElementById('list_id').value;
            if (!listId) {
                alert('Por favor selecciona una lista M3U');
                return;
            }

            // Mostrar modal
            document.getElementById('progressModal').style.display = 'block';
            
            // Resetear estado
            currentProgress = 0;
            analysisComplete = false;
            updateProgress(0, 'Iniciando análisis...');
            clearLog();
            hideResults();

            // Iniciar análisis
            performAnalysis(listId);
        }

        function performAnalysis(listId) {
            addLog('Conectando al servidor...', 'info');
            updateProgress(10, 'Conectando...');

            fetch('m3u_analyzer_api.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=analyze&list_id=${listId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    simulateProgress(data);
                } else {
                    addLog('Error: ' + data.message, 'error');
                    updateProgress(100, 'Error en el análisis');
                }
            })
            .catch(error => {
                addLog('Error de conexión: ' + error.message, 'error');
                updateProgress(100, 'Error de conexión');
            });
        }

        function simulateProgress(data) {
            const totalItems = data.total_items || 0;
            const listName = data.list_name || 'Lista M3U';

            const steps = [
                { progress: 15, message: 'Conectando al servidor...', log: `Iniciando análisis de "${listName}"` },
                { progress: 25, message: 'Descargando lista M3U...', log: `Descargando desde URL remota` },
                { progress: 35, message: 'Validando formato M3U...', log: 'Archivo M3U válido detectado' },
                { progress: 50, message: 'Analizando contenido...', log: `Procesando ${data.total_lines || 'múltiples'} líneas del archivo` },
                { progress: 65, message: 'Clasificando elementos...', log: `Identificando películas y series` },
                { progress: 80, message: 'Limpiando datos anteriores...', log: 'Preparando base de datos' },
                { progress: 90, message: 'Guardando nuevo contenido...', log: `Insertando ${totalItems} elementos` },
                { progress: 95, message: 'Actualizando estadísticas...', log: 'Finalizando proceso' },
                { progress: 100, message: '¡Análisis completado!', log: `✅ Proceso completado: ${totalItems} elementos procesados` }
            ];

            let currentStep = 0;

            function nextStep() {
                if (currentStep < steps.length) {
                    const step = steps[currentStep];
                    updateProgress(step.progress, step.message);
                    addLog(step.log, currentStep === steps.length - 1 ? 'success' : 'info');

                    currentStep++;

                    if (currentStep < steps.length) {
                        // Tiempo variable según el paso
                        const delay = currentStep <= 3 ? 800 : (currentStep <= 6 ? 1200 : 600);
                        setTimeout(nextStep, delay + Math.random() * 500);
                    } else {
                        setTimeout(() => showResults(data), 500);
                    }
                }
            }

            nextStep();
        }

        function updateProgress(percentage, status) {
            document.getElementById('progressFill').style.width = percentage + '%';
            document.getElementById('progressPercentage').textContent = percentage + '%';
            document.getElementById('progressStatus').textContent = status;
        }

        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.textContent = `${new Date().toLocaleTimeString()} - ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function clearLog() {
            document.getElementById('logContainer').innerHTML = '<div class="log-entry info">Preparando análisis...</div>';
        }

        function showResults(data) {
            const resultsContainer = document.getElementById('resultsContainer');
            const resultsGrid = document.getElementById('resultsGrid');

            // Agregar efecto de éxito al progreso
            document.getElementById('progressFill').style.background = 'linear-gradient(90deg, #28a745, #20c997)';

            resultsGrid.innerHTML = `
                <div class="result-item" style="animation: slideInUp 0.5s ease 0.1s both;">
                    <div class="result-number">${data.total_items || 0}</div>
                    <div class="result-label">Total Elementos</div>
                </div>
                <div class="result-item" style="animation: slideInUp 0.5s ease 0.2s both;">
                    <div class="result-number">${data.movies || 0}</div>
                    <div class="result-label">🎬 Películas</div>
                </div>
                <div class="result-item" style="animation: slideInUp 0.5s ease 0.3s both;">
                    <div class="result-number">${data.tv_shows || 0}</div>
                    <div class="result-label">📺 Series</div>
                </div>
                <div class="result-item" style="animation: slideInUp 0.5s ease 0.4s both;">
                    <div class="result-number">${data.unknown || 0}</div>
                    <div class="result-label">❓ Sin clasificar</div>
                </div>
                <div class="result-item" style="animation: slideInUp 0.5s ease 0.5s both;">
                    <div class="result-number">${data.with_year || 0}</div>
                    <div class="result-label">📅 Con año</div>
                </div>
            `;

            // Mostrar resultados con animación
            setTimeout(() => {
                resultsContainer.style.display = 'block';
                resultsContainer.style.animation = 'fadeIn 0.5s ease';
            }, 200);

            // Agregar botón de cerrar
            setTimeout(() => {
                const closeButton = document.createElement('button');
                closeButton.className = 'btn btn-primary';
                closeButton.style.cssText = 'margin-top: 1rem; width: 100%;';
                closeButton.innerHTML = '<i class="fas fa-check"></i> Finalizar';
                closeButton.onclick = closeModal;
                resultsContainer.appendChild(closeButton);
            }, 1000);

            analysisComplete = true;
        }

        function hideResults() {
            document.getElementById('resultsContainer').style.display = 'none';
        }

        function closeModal() {
            if (analysisComplete || confirm('¿Estás seguro de que quieres cancelar el análisis?')) {
                document.getElementById('progressModal').style.display = 'none';
            }
        }

        // Cerrar modal al hacer clic fuera
        window.onclick = function(event) {
            const modal = document.getElementById('progressModal');
            if (event.target === modal && analysisComplete) {
                modal.style.display = 'none';
            }
        }
    </script>
</body>
</html>
